# Ikunc2 C2 Server - 自主嵌入式数据库版本

## 🚀 概述

Ikunc2 C2 Server 现已完全重构为**自主嵌入式数据库架构**，无需任何外部数据库依赖，实现真正的"开箱即用"体验。

## ✨ 主要特性

### 🔥 **完全自主**
- **内置 SQLite 数据库**：无需安装 MySQL、PostgreSQL 等外部数据库
- **零配置启动**：程序自动创建和初始化数据库
- **单文件部署**：可执行文件 + 数据库文件，完全独立运行

### 🛡️ **企业级特性**
- **ACID 事务支持**：数据完整性和一致性保证
- **自动索引优化**：查询性能优化
- **会话管理**：自动清理过期会话
- **数据持久化**：重启后数据完整保留

### 🌍 **跨平台兼容**
- **Windows**: 原生支持，包含系统服务安装
- **Linux/Ubuntu**: 完全兼容，支持 systemd 服务
- **macOS**: 原生支持

## 📊 数据库架构

### 数据表结构
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Agent 表
CREATE TABLE agents (
    id TEXT PRIMARY KEY,
    pc_name TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    username TEXT,
    process_name TEXT,
    os_type TEXT,
    is_connected BOOLEAN DEFAULT FALSE,
    first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 命令历史表
CREATE TABLE command_history (
    id TEXT PRIMARY KEY,
    client_id TEXT NOT NULL,
    command TEXT NOT NULL,
    output TEXT,
    success BOOLEAN DEFAULT FALSE,
    executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES agents (id)
);

-- 会话表
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    data TEXT NOT NULL,
    expiry DATETIME NOT NULL
);
```

### 性能优化索引
- `idx_agents_is_connected`: 快速查找在线 Agent
- `idx_command_history_client_id`: 快速检索命令历史
- `idx_sessions_expiry`: 高效会话清理

## 🚀 快速部署

### Windows 部署
```cmd
# 1. 以管理员身份运行
build_server_windows.bat

# 2. 启动服务
cd dist
start.bat

# 3. 或安装为系统服务
install_service.bat
```

### Ubuntu/Linux 部署
```bash
# 1. 运行部署脚本
bash build_server_ubuntu.sh

# 2. 启动服务
cd dist
./start.sh

# 3. 或安装为系统服务
sudo cp ikunc2.service /etc/systemd/system/
sudo systemctl enable ikunc2
sudo systemctl start ikunc2
```

## 🔧 配置信息

### 默认设置
- **Web 界面**: http://localhost:8080
- **TCP 监听**: 0.0.0.0:5555
- **默认登录**: admin/admin
- **数据库位置**: data/ikunc2.db

### 目录结构
```
dist/
├── c2-gui(.exe)           # 主程序
├── start.sh(.bat)         # 启动脚本
├── ikunc2.service         # Linux 服务文件
├── install_service.bat    # Windows 服务安装
├── uninstall_service.bat  # Windows 服务卸载
├── data/
│   └── ikunc2.db         # SQLite 数据库文件
└── logs/                 # 日志目录
```

## 🔐 安全特性

### 用户认证
- **bcrypt 密码哈希**：BCrypt DEFAULT_COST (12) 强度
- **会话管理**：安全的会话令牌系统
- **自动过期**：配置化的会话超时机制

### 数据保护
- **本地存储**：数据不离开本地环境
- **文件权限**：适当的文件系统权限设置
- **事务完整性**：ACID 特性保证数据一致性

## 📈 性能特点

### 资源占用
- **内存使用**: 低内存占用，适合资源受限环境
- **磁盘空间**: SQLite 文件大小随数据增长，高效压缩
- **CPU 使用**: 优化的查询和索引，低 CPU 占用

### 扩展性
- **并发连接**: 支持大量并发 Agent 连接
- **数据容量**: SQLite 支持 TB 级数据存储
- **查询性能**: 优化的索引结构，快速查询响应

## 🛠️ 运维管理

### 数据库维护
```sql
-- 查看数据库统计
SELECT 
    (SELECT COUNT(*) FROM users) as users,
    (SELECT COUNT(*) FROM agents) as agents,
    (SELECT COUNT(*) FROM agents WHERE is_connected = TRUE) as active_agents,
    (SELECT COUNT(*) FROM command_history) as commands,
    (SELECT COUNT(*) FROM sessions WHERE expiry > CURRENT_TIMESTAMP) as active_sessions;

-- 清理历史数据（保留最近30天）
DELETE FROM command_history 
WHERE executed_at < datetime('now', '-30 days');

-- 优化数据库
VACUUM;
ANALYZE;
```

### 备份策略
```bash
# 数据库备份
cp data/ikunc2.db backups/ikunc2_$(date +%Y%m%d_%H%M%S).db

# 压缩备份
tar -czf ikunc2_backup_$(date +%Y%m%d).tar.gz data/ logs/
```

## 🔄 迁移指南

### 从旧版本迁移
1. **停止旧版本服务**
2. **备份现有数据**（如果有）
3. **部署新版本**
4. **数据自动初始化**（首次运行时自动创建管理员账户）

### 数据导入
程序首次启动时自动：
- 创建数据库表结构
- 初始化默认管理员账户 (admin/admin)
- 设置必要的索引和约束

## 🐛 故障排除

### 常见问题

**问题**: 数据库文件损坏
```bash
# 解决方案：重建数据库
rm data/ikunc2.db
# 重启程序，自动重新创建
```

**问题**: 权限错误
```bash
# Linux/macOS
chmod 755 c2-gui
chmod 644 data/ikunc2.db

# Windows
# 确保程序具有 data 目录的读写权限
```

**问题**: 端口占用
- 修改配置或使用环境变量更改端口
- 检查防火墙设置

## 📋 API 接口

### 数据库 API
```rust
// 用户认证
db.authenticate_user(username, password).await

// Agent 管理
db.add_agent(agent).await
db.update_agent_status(agent_id, is_connected).await
db.get_all_agents().await

// 命令历史
db.add_command_history(history).await
db.get_command_history(client_id, limit).await

// 会话管理
db.save_session(session).await
db.get_session(session_id).await
db.delete_session(session_id).await
```

## 🎯 优势对比

| 特性 | 嵌入式数据库 | 外部数据库 |
|------|-------------|-----------|
| 部署复杂度 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 资源占用 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 维护成本 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 数据安全 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 扩展性 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 性能 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🔮 未来计划

- [ ] **数据库压缩**：自动数据压缩和归档
- [ ] **备份自动化**：内置备份调度系统
- [ ] **数据分析**：内置数据分析和报表功能
- [ ] **集群支持**：多节点数据同步（可选）
- [ ] **加密增强**：数据库文件加密选项

## 📞 技术支持

- **数据库性能调优**
- **故障诊断和恢复**
- **数据迁移支持**
- **安全配置建议**

---

**Ikunc2 C2 Server** - 企业级自主嵌入式数据库架构，为您提供最稳定、最安全、最易用的 C2 服务器解决方案。 