# Ikunc2 C2 Server - 混合存储架构版本

## 🚀 概述

Ikunc2 C2 Server 现已升级为**混合存储架构**，结合了 SQLite 数据库和 JSON 文件的优势，实现最优的数据存储策略：

- **SQLite 数据库**：用于存储用户账号和会话信息，提供 ACID 事务保证
- **JSON 文件**：用于存储 Agent 信息和命令历史，提供灵活性和可读性

## 🏗️ 架构设计

### 存储分层

```
┌─────────────────────────────────────────────────────────┐
│                 Ikunc2 C2 Server                       │
├─────────────────────────────────────────────────────────┤
│              Hybrid Storage Layer                      │
├─────────────────┬───────────────────────────────────────┤
│   SQLite DB     │           JSON Files                  │
├─────────────────┼───────────────────────────────────────┤
│ • Users         │ • Agents (agents.json)               │
│ • Sessions      │ • Command History                     │
│                 │   (command_history.json)             │
└─────────────────┴───────────────────────────────────────┘
```

### 为什么选择混合存储？

| 数据类型 | 存储方式 | 原因 |
|---------|---------|------|
| **用户账号** | SQLite | 需要事务支持、密码安全、查询性能 |
| **会话信息** | SQLite | 需要过期时间查询、事务一致性 |
| **Agent 信息** | JSON | 需要灵活结构、易于查看和备份 |
| **命令历史** | JSON | 需要灵活结构、易于导出和分析 |

## 📊 数据结构

### SQLite 数据库表

#### Users 表
```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### Sessions 表
```sql
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    data TEXT NOT NULL,
    expiry DATETIME NOT NULL
);
```

### JSON 文件结构

#### agents.json
```json
{
  "agent_id_1": {
    "id": "agent_id_1",
    "pc_name": "DESKTOP-ABC123",
    "ip_address": "*************",
    "username": "john_doe",
    "process_name": "agent.exe",
    "os_type": "Windows 10",
    "is_connected": true,
    "first_seen": "2024-01-01T12:00:00Z",
    "last_seen": "2024-01-01T15:30:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

#### command_history.json
```json
{
  "cmd_id_1": {
    "id": "cmd_id_1",
    "client_id": "agent_id_1",
    "command": "whoami",
    "output": "DESKTOP-ABC123\\john_doe",
    "success": true,
    "executed_at": "2024-01-01T13:00:00Z"
  }
}
```

## 🚀 快速部署

### Windows 部署
```cmd
# 1. 以管理员身份运行
build_server_windows.bat

# 2. 启动服务
cd dist
start.bat
```

### Ubuntu/Linux 部署
```bash
# 1. 运行部署脚本
bash build_server_ubuntu.sh

# 2. 启动服务
cd dist
./start.sh
```

## 🔧 配置和管理

### 目录结构
```
dist/
├── c2-gui(.exe)                    # 主程序
├── start.sh(.bat)                  # 启动脚本
├── data/                           # 数据目录
│   ├── users.db                    # SQLite 用户数据库
│   ├── agents.json                 # Agent 信息文件
│   └── command_history.json        # 命令历史文件
└── logs/                           # 日志目录
```

### 默认设置
- **Web 界面**: http://localhost:8080
- **TCP 监听**: 0.0.0.0:5555
- **默认登录**: admin/admin
- **用户数据库**: data/users.db
- **Agent 数据**: data/agents.json
- **命令历史**: data/command_history.json

## 💡 核心特性

### 🔐 安全特性
- **密码加密**: bcrypt 哈希算法
- **会话管理**: 安全的会话令牌系统
- **数据库事务**: ACID 特性保证数据完整性
- **权限控制**: 基于角色的访问控制

### 📈 性能特性
- **索引优化**: 数据库查询性能优化
- **内存缓存**: Agent 数据内存缓存
- **异步处理**: 全异步 I/O 操作
- **批量操作**: 批量 JSON 文件更新

### 🛠️ 运维特性
- **自动备份**: JSON 文件易于备份和恢复
- **数据清理**: 自动清理过期会话和历史数据
- **监控统计**: 实时数据统计和监控
- **日志管理**: 结构化日志记录

## 📋 API 接口

### 用户管理（SQLite）
```rust
// 用户认证
storage.authenticate_user(username, password).await

// 更新登录时间
storage.update_user_last_login(user_id).await

// 创建用户
storage.create_user(username, password).await
```

### Agent 管理（JSON）
```rust
// 添加 Agent
storage.add_agent(agent).await

// 更新 Agent 状态
storage.update_agent_status(agent_id, is_connected).await

// 获取所有 Agent
storage.get_all_agents().await

// 删除 Agent
storage.remove_agent(agent_id).await
```

### 命令历史（JSON）
```rust
// 添加命令历史
storage.add_command_history(history).await

// 获取命令历史
storage.get_command_history(client_id, limit).await

// 清理旧历史
storage.cleanup_old_command_history(days).await
```

### 会话管理（SQLite）
```rust
// 保存会话
storage.save_session(session).await

// 获取会话
storage.get_session(session_id).await

// 删除会话
storage.delete_session(session_id).await
```

## 🔄 数据管理

### 备份策略
```bash
# 完整备份
tar -czf backup_$(date +%Y%m%d).tar.gz data/ logs/

# 数据库备份
cp data/users.db backups/users_$(date +%Y%m%d_%H%M%S).db

# JSON 文件备份
cp data/*.json backups/
```

### 数据恢复
```bash
# 恢复数据库
cp backups/users_20240101_120000.db data/users.db

# 恢复 JSON 文件
cp backups/agents.json data/
cp backups/command_history.json data/
```

### 数据迁移
```rust
// 从旧版本迁移 Agent 数据
let agents = load_old_agent_data();
for agent in agents {
    storage.add_agent(agent).await?;
}
```

## 📊 监控和统计

### 获取系统统计
```rust
let stats = storage.get_stats().await?;
println!("Users: {}", stats.get("users").unwrap_or(&0));
println!("Agents: {}", stats.get("agents").unwrap_or(&0));
println!("Active Agents: {}", stats.get("active_agents").unwrap_or(&0));
println!("Commands: {}", stats.get("commands").unwrap_or(&0));
println!("Sessions: {}", stats.get("sessions").unwrap_or(&0));
```

### 存储路径信息
```rust
let paths = hybrid_storage::get_storage_paths();
println!("Data directory: {}", paths.get("data_dir").unwrap());
println!("Users DB: {}", paths.get("users_db").unwrap());
println!("Agents JSON: {}", paths.get("agents_json").unwrap());
println!("Commands JSON: {}", paths.get("command_history_json").unwrap());
```

## 🔧 高级配置

### 环境变量
```bash
# 数据目录
export IKUNC2_DATA_DIR="/custom/data/path"

# 日志级别
export RUST_LOG="info"

# 服务器配置
export IKUNC2_HTTP_ADDR="0.0.0.0:8080"
export IKUNC2_TCP_ADDR="0.0.0.0:5555"
```

### 自定义配置
```toml
[server]
http_addr = "0.0.0.0:8080"
tcp_addr = "0.0.0.0:5555"

[storage]
data_dir = "data"
auto_cleanup_days = 30

[security]
session_timeout_hours = 24
```

## 🐛 故障排除

### 常见问题

**问题 1**: 数据库连接失败
```bash
# 检查数据库文件权限
ls -la data/users.db
chmod 644 data/users.db
```

**问题 2**: JSON 文件损坏
```bash
# 验证 JSON 格式
python -m json.tool data/agents.json
# 或者使用 jq
jq . data/agents.json
```

**问题 3**: 权限错误
```bash
# Linux/macOS
sudo chown -R $USER:$USER data/
chmod -R 755 data/

# Windows (以管理员身份运行)
icacls data /grant Users:F /T
```

## 🔮 性能调优

### 数据库优化
```sql
-- 重建索引
REINDEX;

-- 分析查询计划
EXPLAIN QUERY PLAN SELECT * FROM users WHERE username = ?;

-- 数据库统计
PRAGMA table_info(users);
PRAGMA index_list(users);
```

### JSON 文件优化
- 定期清理无用数据
- 使用压缩存储
- 批量更新减少 I/O
- 内存缓存热点数据

## 🎯 最佳实践

### 数据安全
1. 定期备份数据库和 JSON 文件
2. 使用强密码策略
3. 启用访问日志
4. 设置文件系统权限

### 性能优化
1. 定期清理历史数据
2. 监控数据库大小
3. 使用索引优化查询
4. 控制 JSON 文件大小

### 运维管理
1. 监控磁盘空间
2. 设置日志轮转
3. 定期检查数据完整性
4. 建立监控告警

---

**Ikunc2 C2 Server** - 混合存储架构，为您提供最灵活、最高效、最可靠的 C2 服务器解决方案。