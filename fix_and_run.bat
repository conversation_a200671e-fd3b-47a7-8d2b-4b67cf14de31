@echo off
echo 🔧 Auto-fixing and running Ikunc2 C2 Server...

REM Kill any existing c2-gui processes
taskkill /F /IM c2-gui.exe 2>nul

REM Wait a moment for cleanup
timeout /t 2 /nobreak >nul

REM Set logging environment
set RUST_LOG=info
set RUST_BACKTRACE=1

REM Create directories
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "certs" mkdir certs

echo 🚀 Starting server...
echo 🌐 Web: http://localhost:8080
echo 🔐 Login: admin/admin
echo.

target\debug\c2-gui.exe
pause