<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Capabilities - Ikunc2 C2</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-weight: 600;
            color: #4a5568;
        }

        .logs-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 15px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success {
            color: #48bb78;
        }

        .log-error {
            color: #f56565;
        }

        .log-info {
            color: #4299e1;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .capability-item {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .capability-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .capability-item.selected {
            border-color: #667eea;
            background: #edf2f7;
        }

        .capability-item h4 {
            color: #4a5568;
            margin-bottom: 8px;
        }

        .capability-item p {
            color: #718096;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .risk-level {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .risk-high {
            background: #fed7d7;
            color: #c53030;
        }

        .risk-medium {
            background: #fef5e7;
            color: #d69e2e;
        }

        .risk-low {
            background: #f0fff4;
            color: #38a169;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-pending {
            background: #e2e8f0;
        }

        .status-in-progress {
            background: #4299e1;
            animation: pulse 1.5s infinite;
        }

        .status-completed {
            background: #48bb78;
        }

        .status-failed {
            background: #f56565;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-secondary {
            background: #718096;
        }

        .btn-danger {
            background: #f56565;
        }

        .btn-success {
            background: #48bb78;
        }

        .full-width {
            grid-column: 1 / -1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> Enhanced Capabilities</h1>
            <p>Advanced Agent Generation with Real-time Monitoring</p>
        </div>

        <div class="main-content">
            <!-- Configuration Panel -->
            <div class="card">
                <h2><i class="fas fa-cog"></i> Agent Configuration</h2>
                
                <div class="form-group">
                    <label for="template">Template:</label>
                    <select id="template">
                        <option value="">Select a template...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="server-ip">Server IP:</label>
                    <input type="text" id="server-ip" value="127.0.0.1" placeholder="Enter server IP">
                </div>

                <div class="form-group">
                    <label for="server-port">Server Port:</label>
                    <input type="number" id="server-port" value="5555" placeholder="Enter server port">
                </div>

                <div class="form-group">
                    <label for="agent-id">Agent ID:</label>
                    <input type="text" id="agent-id" placeholder="Auto-generated" readonly>
                </div>

                <div class="form-group">
                    <label>Capabilities:</label>
                    <div id="capabilities-grid" class="capabilities-grid">
                        <!-- Capabilities will be loaded here -->
                    </div>
                </div>

                <div class="actions">
                    <button class="btn" id="generate-btn">
                        <i class="fas fa-play"></i> Generate Agent
                    </button>
                    <button class="btn btn-secondary" id="reset-btn">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>
            </div>

            <!-- Progress Panel -->
            <div class="card">
                <h2><i class="fas fa-chart-line"></i> Generation Progress</h2>
                
                <div id="progress-container" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Ready to start...</div>
                    
                    <div class="logs-container" id="logs-container">
                        <!-- Logs will appear here -->
                    </div>
                    
                    <div class="actions">
                        <button class="btn btn-danger" id="cancel-btn" style="display: none;">
                            <i class="fas fa-stop"></i> Cancel
                        </button>
                        <button class="btn btn-success" id="download-btn" style="display: none;">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>

                <div id="no-progress" style="text-align: center; color: #718096; padding: 40px;">
                    <i class="fas fa-info-circle" style="font-size: 3em; margin-bottom: 20px;"></i>
                    <p>No active generation. Configure and start a new agent generation.</p>
                </div>
            </div>
        </div>

        <!-- Templates Panel -->
        <div class="card full-width">
            <h2><i class="fas fa-layer-group"></i> Available Templates</h2>
            <div id="templates-grid" class="capabilities-grid">
                <!-- Templates will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentGenerationId = null;
        let progressInterval = null;
        let capabilities = [];
        let templates = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadCapabilities();
            loadTemplates();
            generateAgentId();
            
            // Event listeners
            document.getElementById('generate-btn').addEventListener('click', startGeneration);
            document.getElementById('cancel-btn').addEventListener('click', cancelGeneration);
            document.getElementById('download-btn').addEventListener('click', downloadAgent);
            document.getElementById('reset-btn').addEventListener('click', resetForm);
        });

        // Load capabilities from API
        async function loadCapabilities() {
            try {
                const response = await fetch('/api/enhanced-capabilities');
                capabilities = await response.json();
                renderCapabilities();
            } catch (error) {
                console.error('Failed to load capabilities:', error);
                addLog('[-] Failed to load capabilities', 'error');
            }
        }

        // Load templates from API
        async function loadTemplates() {
            try {
                const response = await fetch('/api/enhanced-templates');
                templates = await response.json();
                renderTemplates();
                populateTemplateSelect();
            } catch (error) {
                console.error('Failed to load templates:', error);
                addLog('[-] Failed to load templates', 'error');
            }
        }

        // Render capabilities grid
        function renderCapabilities() {
            const grid = document.getElementById('capabilities-grid');
            grid.innerHTML = '';

            capabilities.forEach(capability => {
                const item = document.createElement('div');
                item.className = 'capability-item';
                item.dataset.id = capability.id;
                
                item.innerHTML = `
                    <h4>${capability.name}</h4>
                    <p>${capability.description}</p>
                    <span class="risk-level risk-${capability.risk_level.toLowerCase()}">${capability.risk_level}</span>
                `;
                
                item.addEventListener('click', () => toggleCapability(capability.id));
                grid.appendChild(item);
            });
        }

        // Render templates grid
        function renderTemplates() {
            const grid = document.getElementById('templates-grid');
            grid.innerHTML = '';

            templates.forEach(template => {
                const item = document.createElement('div');
                item.className = 'capability-item';
                
                item.innerHTML = `
                    <h4>${template.name}</h4>
                    <p>${template.description}</p>
                    <p><strong>Capabilities:</strong> ${template.capabilities.join(', ')}</p>
                    <p><strong>Target OS:</strong> ${template.target_os.join(', ')}</p>
                    <p><strong>Architecture:</strong> ${template.target_arch.join(', ')}</p>
                `;
                
                grid.appendChild(item);
            });
        }

        // Populate template select
        function populateTemplateSelect() {
            const select = document.getElementById('template');
            select.innerHTML = '<option value="">Select a template...</option>';
            
            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                select.appendChild(option);
            });
        }

        // Toggle capability selection
        function toggleCapability(capabilityId) {
            const item = document.querySelector(`[data-id="${capabilityId}"]`);
            item.classList.toggle('selected');
        }

        // Generate random agent ID
        function generateAgentId() {
            const agentId = 'agent_' + Math.random().toString(36).substr(2, 9);
            document.getElementById('agent-id').value = agentId;
        }

        // Start generation
        async function startGeneration() {
            const templateId = document.getElementById('template').value;
            const serverIp = document.getElementById('server-ip').value;
            const serverPort = parseInt(document.getElementById('server-port').value);
            const agentId = document.getElementById('agent-id').value;

            if (!templateId) {
                alert('Please select a template');
                return;
            }

            if (!serverIp || !serverPort) {
                alert('Please enter server IP and port');
                return;
            }

            // Get selected capabilities
            const selectedCapabilities = Array.from(document.querySelectorAll('.capability-item.selected'))
                .map(item => item.dataset.id);

            const request = {
                template_id: templateId,
                server_ip: serverIp,
                server_port: serverPort,
                agent_id: agentId,
                capabilities: selectedCapabilities,
                custom_config: {}
            };

            try {
                const response = await fetch('/api/enhanced-generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(request)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentGenerationId = result.generation_id;
                    showProgress();
                    startProgressMonitoring();
                    addLog('[+] Generation started successfully', 'success');
                } else {
                    addLog('[-] Failed to start generation', 'error');
                }
            } catch (error) {
                console.error('Generation error:', error);
                addLog('[-] Generation error: ' + error.message, 'error');
            }
        }

        // Show progress panel
        function showProgress() {
            document.getElementById('progress-container').style.display = 'block';
            document.getElementById('no-progress').style.display = 'none';
            document.getElementById('cancel-btn').style.display = 'inline-block';
            document.getElementById('download-btn').style.display = 'none';
        }

        // Start progress monitoring
        function startProgressMonitoring() {
            progressInterval = setInterval(async () => {
                if (!currentGenerationId) return;

                try {
                    const response = await fetch(`/api/enhanced-progress/${currentGenerationId}`);
                    const progress = await response.json();

                    updateProgress(progress.progress_percentage, progress.current_step);
                    
                    // Update logs
                    if (progress.logs) {
                        progress.logs.forEach(log => addLog(log, 'info'));
                    }

                    if (progress.status === 'Completed') {
                        clearInterval(progressInterval);
                        document.getElementById('download-btn').style.display = 'inline-block';
                        document.getElementById('cancel-btn').style.display = 'none';
                        addLog('[+] Generation completed successfully!', 'success');
                    } else if (progress.status === 'Failed') {
                        clearInterval(progressInterval);
                        addLog('[-] Generation failed: ' + (progress.error_message || 'Unknown error'), 'error');
                    }
                } catch (error) {
                    console.error('Progress monitoring error:', error);
                }
            }, 1000);
        }

        // Update progress bar
        function updateProgress(percentage, step) {
            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = step || `Progress: ${percentage}%`;
        }

        // Cancel generation
        async function cancelGeneration() {
            if (!currentGenerationId) return;

            try {
                const response = await fetch(`/api/enhanced-cancel/${currentGenerationId}`, {
                    method: 'POST'
                });

                const result = await response.json();
                
                if (result.success) {
                    clearInterval(progressInterval);
                    addLog('[+] Generation cancelled', 'info');
                    currentGenerationId = null;
                }
            } catch (error) {
                console.error('Cancel error:', error);
                addLog('[-] Failed to cancel generation', 'error');
            }
        }

        // Download agent
        async function downloadAgent() {
            if (!currentGenerationId) return;

            try {
                const response = await fetch(`/api/enhanced-download/${currentGenerationId}`);
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `agent_${currentGenerationId}.exe`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                addLog('[+] Agent downloaded successfully', 'success');
            } catch (error) {
                console.error('Download error:', error);
                addLog('[-] Failed to download agent', 'error');
            }
        }

        // Add log entry
        function addLog(message, type = 'info') {
            const container = document.getElementById('logs-container');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        // Reset form
        function resetForm() {
            document.getElementById('template').value = '';
            document.getElementById('server-ip').value = '127.0.0.1';
            document.getElementById('server-port').value = '5555';
            generateAgentId();
            
            // Clear capability selections
            document.querySelectorAll('.capability-item.selected').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Clear progress
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            currentGenerationId = null;
            document.getElementById('progress-container').style.display = 'none';
            document.getElementById('no-progress').style.display = 'block';
            document.getElementById('logs-container').innerHTML = '';
        }
    </script>
</body>
</html> 