// Anti-Debugging Capability
// 反调试能力模板

#[cfg(windows)]
use winapi::um::debugapi::IsDebuggerPresent;
#[cfg(windows)]
use winapi::um::processthreadsapi::GetCurrentProcess;
#[cfg(windows)]
use winapi::um::processthreadsapi::GetCurrentProcessId;
#[cfg(windows)]
use winapi::um::handleapi::CloseHandle;

pub struct AntiDebugging {
    enabled: bool,
}

impl AntiDebugging {
    pub fn new() -> Self {
        Self {
            enabled: true,
        }
    }
    
    pub fn check_debugger(&self) -> bool {
        #[cfg(windows)]
        {
            unsafe {
                let is_debugger = IsDebuggerPresent();
                if is_debugger != 0 {
                    println!("[-] Debugger detected!");
                    return true;
                }
            }
        }
        
        #[cfg(not(windows))]
        {
            // 在非Windows系统上，检查环境变量
            if std::env::var("GDB").is_ok() || std::env::var("LLDB").is_ok() {
                println!("[-] Debugger detected via environment variables!");
                return true;
            }
        }
        
        false
    }
    
    pub fn check_process_flags(&self) -> bool {
        #[cfg(windows)]
        {
            unsafe {
                let process = GetCurrentProcess();
                if process.is_null() {
                    return false;
                }
                
                // 检查进程标志
                let mut flags = 0u32;
                if winapi::um::processthreadsapi::GetProcessAffinityMask(
                    process, 
                    &mut flags, 
                    std::ptr::null_mut()
                ) == 0 {
                    CloseHandle(process);
                    return false;
                }
                
                CloseHandle(process);
                
                // 如果进程被调试，某些标志会被设置
                if flags & 0x1F != 0x1F {
                    println!("[-] Process flags indicate debugging!");
                    return true;
                }
            }
        }
        
        false
    }
    
    pub fn check_timing(&self) -> bool {
        use std::time::Instant;
        
        let start = Instant::now();
        
        // 执行一些操作
        for _ in 0..1000 {
            let _ = std::process::id();
        }
        
        let elapsed = start.elapsed();
        
        // 如果执行时间异常长，可能是被调试
        if elapsed.as_millis() > 100 {
            println!("[-] Timing anomaly detected!");
            return true;
        }
        
        false
    }
    
    pub fn check_parent_process(&self) -> bool {
        #[cfg(windows)]
        {
            unsafe {
                let current_pid = GetCurrentProcessId();
                let mut parent_pid = 0u32;
                
                // 获取父进程ID
                if winapi::um::tlhelp32::CreateToolhelp32Snapshot(
                    winapi::um::tlhelp32::TH32CS_SNAPPROCESS,
                    0
                ) != winapi::um::handleapi::INVALID_HANDLE_VALUE {
                    let mut pe32: winapi::um::tlhelp32::PROCESSENTRY32W = std::mem::zeroed();
                    pe32.dwSize = std::mem::size_of::<winapi::um::tlhelp32::PROCESSENTRY32W>() as u32;
                    
                    if winapi::um::tlhelp32::Process32FirstW(
                        winapi::um::tlhelp32::CreateToolhelp32Snapshot(
                            winapi::um::tlhelp32::TH32CS_SNAPPROCESS,
                            0
                        ),
                        &mut pe32
                    ) != 0 {
                        loop {
                            if pe32.th32ProcessID == current_pid {
                                parent_pid = pe32.th32ParentProcessID;
                                break;
                            }
                            
                            if winapi::um::tlhelp32::Process32NextW(
                                winapi::um::tlhelp32::CreateToolhelp32Snapshot(
                                    winapi::um::tlhelp32::TH32CS_SNAPPROCESS,
                                    0
                                ),
                                &mut pe32
                            ) == 0 {
                                break;
                            }
                        }
                    }
                }
                
                // 检查父进程是否是调试器
                if parent_pid != 0 {
                    let parent_name = self.get_process_name(parent_pid);
                    if parent_name.contains("windbg") || 
                       parent_name.contains("x64dbg") || 
                       parent_name.contains("ollydbg") ||
                       parent_name.contains("ida") {
                        println!("[-] Debugger parent process detected: {}", parent_name);
                        return true;
                    }
                }
            }
        }
        
        false
    }
    
    #[cfg(windows)]
    fn get_process_name(&self, pid: u32) -> String {
        unsafe {
            let mut pe32: winapi::um::tlhelp32::PROCESSENTRY32W = std::mem::zeroed();
            pe32.dwSize = std::mem::size_of::<winapi::um::tlhelp32::PROCESSENTRY32W>() as u32;
            
            let snapshot = winapi::um::tlhelp32::CreateToolhelp32Snapshot(
                winapi::um::tlhelp32::TH32CS_SNAPPROCESS,
                0
            );
            
            if snapshot != winapi::um::handleapi::INVALID_HANDLE_VALUE {
                if winapi::um::tlhelp32::Process32FirstW(snapshot, &mut pe32) != 0 {
                    loop {
                        if pe32.th32ProcessID == pid {
                            let name = String::from_utf16_lossy(
                                &pe32.szExeFile.iter()
                                    .take_while(|&&c| c != 0)
                                    .map(|&c| c as u16)
                                    .collect::<Vec<u16>>()
                            );
                            winapi::um::handleapi::CloseHandle(snapshot);
                            return name;
                        }
                        
                        if winapi::um::tlhelp32::Process32NextW(snapshot, &mut pe32) == 0 {
                            break;
                        }
                    }
                }
                winapi::um::handleapi::CloseHandle(snapshot);
            }
        }
        
        "Unknown".to_string()
    }
    
    pub fn run_checks(&self) -> bool {
        if !self.enabled {
            return false;
        }
        
        println!("[+] Running anti-debugging checks...");
        
        let checks = vec![
            self.check_debugger(),
            self.check_process_flags(),
            self.check_timing(),
            self.check_parent_process(),
        ];
        
        let debugger_detected = checks.iter().any(|&check| check);
        
        if debugger_detected {
            println!("[-] Debugger detected! Exiting...");
            std::process::exit(1);
        } else {
            println!("[+] No debugger detected");
        }
        
        debugger_detected
    }
}

// 在main函数开始时调用
pub fn init_anti_debugging() {
    let anti_debug = AntiDebugging::new();
    anti_debug.run_checks();
} 