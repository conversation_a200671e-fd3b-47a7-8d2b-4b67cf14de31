# Enhanced Capabilities - 增强能力模块

## 概述

Enhanced Capabilities 是 Ikunc2 C2 服务器的高级功能模块，提供基于模板的智能 agent 生成系统，支持实时监控和多种规避技术。

## 功能特性

### [+] 核心功能
- **模板化 Agent 生成**: 基于预定义模板快速生成自定义 agent
- **实时生成监控**: 实时查看生成进度和详细日志
- **多种规避技术**: 集成反调试、沙箱规避、进程注入等高级功能
- **可视化界面**: 现代化的 Web 界面，支持拖拽和实时交互

### [+] 技术能力

#### 1. 反调试技术 (Anti-Debugging)
- 检测调试器存在
- 检查进程标志
- 时间异常检测
- 父进程分析

#### 2. 沙箱规避 (Sandbox Evasion)
- 虚拟机检测
- 沙箱环境识别
- 系统资源检查
- 用户交互验证

#### 3. 进程注入 (Process Injection)
- DLL 注入
- Shellcode 注入
- 远程线程创建
- 内存操作

## 文件结构

```
enhanced_capabilities/
├── templates/                 # 模板文件
│   ├── basic_windows.rs      # 基础Windows代理模板
│   ├── advanced_windows.rs   # 高级Windows代理模板
│   ├── anti_debugging.rs     # 反调试能力模板
│   ├── sandbox_evasion.rs    # 沙箱规避能力模板
│   └── process_injection.rs  # 进程注入能力模板
├── frontend/                  # 前端界面
│   └── index.html            # 主界面文件
├── backend/                   # 后端处理
└── README.md                 # 说明文档
```

## 使用方法

### 1. 启动服务器
```bash
cargo build --release
./target/release/c2-gui.exe
```

### 2. 访问界面
打开浏览器访问: `http://localhost:8080/enhanced-capabilities`

### 3. 生成 Agent
1. 选择模板 (Basic Windows 或 Advanced Windows)
2. 配置服务器 IP 和端口
3. 选择所需的能力 (反调试、沙箱规避、进程注入)
4. 点击 "Generate Agent" 开始生成
5. 实时监控生成进度
6. 下载生成的 agent

## API 接口

### 获取能力列表
```
GET /api/enhanced-capabilities
```

### 获取模板列表
```
GET /api/enhanced-templates
```

### 开始生成
```
POST /api/enhanced-generate
{
    "template_id": "basic_windows",
    "server_ip": "127.0.0.1",
    "server_port": 5555,
    "agent_id": "agent_123",
    "capabilities": ["anti_debugging"],
    "custom_config": {}
}
```

### 获取生成进度
```
GET /api/enhanced-progress/{generation_id}
```

### 取消生成
```
POST /api/enhanced-cancel/{generation_id}
```

### 下载生成的 Agent
```
GET /api/enhanced-download/{generation_id}
```

## 模板系统

### 基础模板 (Basic Windows)
- 包含基本的通信功能
- 集成反调试能力
- 适合学习和测试

### 高级模板 (Advanced Windows)
- 包含多种规避技术
- 进程注入能力
- 沙箱环境检测
- 适合实际部署

## 能力模块

### 反调试 (Anti-Debugging)
```rust
// 检测调试器
if IsDebuggerPresent() != 0 {
    println!("[-] Debugger detected!");
    std::process::exit(1);
}
```

### 沙箱规避 (Sandbox Evasion)
```rust
// 检查虚拟机
let vm_indicators = vec!["VMware", "VBox", "Virtual"];
for indicator in vm_indicators {
    if manufacturer.contains(indicator) {
        println!("[-] Virtual machine detected!");
        std::process::exit(1);
    }
}
```

### 进程注入 (Process Injection)
```rust
// DLL 注入
let process_handle = OpenProcess(PROCESS_ALL_ACCESS, 0, target_pid);
let remote_memory = VirtualAllocEx(process_handle, ...);
WriteProcessMemory(process_handle, remote_memory, ...);
CreateRemoteThread(process_handle, ...);
```

## 实时监控

生成过程中会显示详细的进度信息：

```
[+] Starting agent generation...
[+] Loading template...
[+] Validating configuration...
[+] Generating base code...
[+] Applying capabilities...
[+] Finalizing code...
[+] Generation completed successfully!
```

## 错误处理

系统包含完善的错误处理机制：

- **模板加载失败**: 自动创建默认模板
- **生成过程错误**: 显示详细错误信息
- **网络连接问题**: 自动重试机制
- **文件系统错误**: 创建必要的目录结构

## 安全注意事项

⚠️ **重要提醒**:
- 本工具仅用于合法的安全测试和研究
- 请遵守当地法律法规
- 不要用于恶意攻击或非法活动
- 建议在隔离环境中测试

## 开发计划

### [+] 即将推出的功能
- [ ] 更多操作系统支持 (Linux, macOS)
- [ ] 更多架构支持 (ARM, MIPS)
- [ ] 自定义模板编辑器
- [ ] 批量生成功能
- [ ] 云端模板库
- [ ] 自动化测试框架

### [+] 技术改进
- [ ] 更高级的规避技术
- [ ] 机器学习检测绕过
- [ ] 动态代码生成
- [ ] 加密通信协议
- [ ] 分布式部署支持

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境设置
```bash
git clone <repository>
cd c2-gui
cargo build
```

### 代码规范
- 使用 Rust 标准编码规范
- 添加适当的注释和文档
- 编写单元测试
- 确保错误处理完善

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 项目 Wiki
- 开发者邮箱

---

**注意**: 本模块包含高级安全技术，请负责任地使用。 