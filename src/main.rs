// ===============================================================================
// Ikunc2 C2 服务器 - 主程序入口
// 命令与控制服务器的主要启动文件
// ===============================================================================

use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error, warn, debug};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use tower_http::cors::CorsLayer;
use axum::http::header::{AUTHORIZATION, ACCEPT, CONTENT_TYPE};
use axum::http::Method;
use axum::http::HeaderValue;
use chrono::Timelike; // 添加这个导入
use std::net::{SocketAddr, TcpListener}; // 添加端口检查需要的导入

// 使用 lib.rs 中导出的模块
use c2_gui::server;
use c2_gui::tcp_listener;
use c2_gui::web;
use c2_gui::agent_builder;
use c2_gui::config;
use c2_gui::client;
use c2_gui::hybrid_storage;
use c2_gui::terminal_manager;
use c2_gui::debug;

// Enhanced Capabilities module
use c2_gui::enhanced_capabilities;

mod debug_framework {
    pub struct DebugFramework;
    pub struct DebugEvent;
    pub enum DebugEventType { Info }
    pub enum DebugSeverity { Low }
}

mod threat_hunting {
    pub fn get_threat_hunting_engine() -> String {
        "placeholder".to_string()
    }
}

use server::AppState;
use client::ClientInfo;

// 主服务器入口
#[tokio::main]
async fn main() {
    // 加载配置
    let config = config::get_config();
    
    // 初始化日志
    init_logging(&config);
    
    // 解析命令行参数
    let args: Vec<String> = std::env::args().collect();
    
    if args.len() > 1 && args[1] == "agent" {
        // 以 agent 模式运行
        run_agent().await;
    } else {
        // 以服务器+Web GUI 模式运行
        run_server(config).await;
    }
}

// 日志初始化，根据配置决定输出到控制台和/或文件
fn init_logging(config: &config::Config) {
    let log_level = match config.logging.level.as_str() {
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };

    // 创建彩色控制台输出层
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(true)
        .with_level(true)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .with_ansi(true)
        .compact();

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("{}={}", env!("CARGO_PKG_NAME"), log_level).into()),
        )
        .with(console_layer)
        .init();

    // 显示启动横幅
    print_startup_banner();
    
    info!("Logging initialized with level: {}", config.logging.level);
}

// 添加启动横幅显示函数
fn print_startup_banner() {
    println!("\n{}", "=".repeat(60));
    println!("    🚀 Ikunc2 C2 Server - Real-time Debug Mode");
    println!("    📅 Started at: {}", chrono::Local::now().format("%Y-%m-%d %H:%M:%S"));
    println!("{}", "=".repeat(60));
    println!();
}

// 增强的服务器状态显示函数，包含端口变更信息
fn print_enhanced_server_status(config: &config::Config, original_http: u16, original_tcp: u16) {
    println!("{}", "=".repeat(70));
    println!("    📊 IKUNC2 C2 SERVER - ENHANCED STATUS");
    println!("{}", "=".repeat(70));
    println!("🎯 Ikunc2 C2 Server is running!");
    println!();
    
    // HTTP 服务器状态
    if config.server.http_addr.port() != original_http {
        println!("🌐 Web GUI: http://{} ⚠️  (自动从端口 {} 切换)", 
            config.server.http_addr, original_http);
    } else {
        println!("🌐 Web GUI: http://{} ✅", config.server.http_addr);
    }
    println!("   Protocol: HTTP (plaintext)");
    
    // TCP 监听器状态
    if config.server.tcp_addr.port() != original_tcp {
        println!("🔌 TCP Listener: {} ⚠️  (自动从端口 {} 切换)", 
            config.server.tcp_addr, original_tcp);
    } else {
        println!("🔌 TCP Listener: {} ✅", config.server.tcp_addr);
    }
    
    println!();
    println!("📊 Storage System:");
    println!("  📁 Data Directory: data");
    println!("  🗄️  Users Database: data/users.db");
    println!("  📄 Agents JSON: data/agents.json");
    println!("  📄 Commands JSON: data/command_history.json");
    println!();
    println!("🔧 Agent Communication:");
    println!("  📡 Supported Protocols: TCP, HTTP, HTTPS");
    println!("  🛡️  TLS Encryption: Disabled");
    println!("🔐 Default Login: admin/admin");
    println!();
    println!("⚙️  Port Management:");
    println!("  🔍 Automatic Port Discovery: Enabled");
    println!("  🔄 Port Conflict Resolution: Active");
    println!("  📈 Error Recovery: Enabled");
    println!("{}", "=".repeat(70));
    println!("           🔴 REAL-TIME LOGS");
    println!("{}", "=".repeat(70));
    println!();
}

// 添加实时状态监控函数
fn start_status_monitor() {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            debug!("💓 Server heartbeat - System running normally");
            
            // 每5分钟显示详细状态
            if chrono::Local::now().minute() % 5 == 0 {
                info!("📊 System Status Check:");
                info!("  🟢 Web Server: Active");
                info!("  🟢 TCP Listener: Active");
                info!("  🟢 Storage System: Operational");
                info!("  📈 Uptime: {}", format_uptime());
            }
        }
    });
}

// 格式化运行时间
fn format_uptime() -> String {
    static START_TIME: std::sync::OnceLock<std::time::Instant> = std::sync::OnceLock::new();
    let start = START_TIME.get_or_init(|| std::time::Instant::now());
    let elapsed = start.elapsed();
    
    let hours = elapsed.as_secs() / 3600;
    let minutes = (elapsed.as_secs() % 3600) / 60;
    let seconds = elapsed.as_secs() % 60;
    
    format!("{}h {}m {}s", hours, minutes, seconds)
}

// 添加端口检查和自动分配功能
fn is_port_available(addr: SocketAddr) -> bool {
    TcpListener::bind(addr).is_ok()
}

fn find_available_port(base_addr: SocketAddr, start_port: u16, max_attempts: u16) -> Option<SocketAddr> {
    let host = base_addr.ip();
    
    for port in start_port..start_port + max_attempts {
        let test_addr = SocketAddr::new(host, port);
        if is_port_available(test_addr) {
            return Some(test_addr);
        }
    }
    None
}

fn resolve_port_conflicts(mut http_addr: SocketAddr, mut tcp_addr: SocketAddr) -> std::result::Result<(SocketAddr, SocketAddr), String> {
    // 检查 HTTP 端口是否可用
    if !is_port_available(http_addr) {
        warn!("⚠️  HTTP端口 {} 被占用，正在寻找可用端口...", http_addr.port());
        match find_available_port(http_addr, http_addr.port() + 1, 100) {
            Some(new_addr) => {
                info!("✅ 找到可用HTTP端口: {}", new_addr.port());
                http_addr = new_addr;
            }
            None => {
                return Err(format!("无法找到可用的HTTP端口 (尝试了端口 {} 到 {})", 
                    http_addr.port() + 1, http_addr.port() + 100));
            }
        }
    }
    
    // 检查 TCP 端口是否可用，确保与 HTTP 端口不冲突
    while !is_port_available(tcp_addr) || tcp_addr.port() == http_addr.port() {
        if tcp_addr.port() == http_addr.port() {
            warn!("⚠️  TCP端口 {} 与HTTP端口冲突，正在寻找替代端口...", tcp_addr.port());
        } else {
            warn!("⚠️  TCP端口 {} 被占用，正在寻找可用端口...", tcp_addr.port());
        }
        
        match find_available_port(tcp_addr, tcp_addr.port() + 1, 100) {
            Some(new_addr) => {
                if new_addr.port() != http_addr.port() {
                    info!("✅ 找到可用TCP端口: {}", new_addr.port());
                    tcp_addr = new_addr;
                    break;
                }
                // 如果新端口与HTTP端口冲突，继续寻找
                tcp_addr = SocketAddr::new(tcp_addr.ip(), new_addr.port() + 1);
            }
            None => {
                return Err(format!("无法找到可用的TCP端口 (尝试了端口 {} 到 {})", 
                    tcp_addr.port() + 1, tcp_addr.port() + 100));
            }
        }
    }
    
    Ok((http_addr, tcp_addr))
}

async fn run_server(config: config::Config) {
    let original_http_addr = config.server.http_addr;
    let original_tcp_addr = config.server.tcp_addr;
    
    // 显示启动横幅
    print_startup_banner();
    
    info!("🚀 启动 Ikunc2 C2 Server - 混合存储版");
    info!("📋 配置加载成功");
    
    // 添加详细的调试信息
    println!("🔍 正在检查端口配置...");
    println!("   HTTP地址: {}", original_http_addr);
    println!("   TCP地址: {}", original_tcp_addr);
    println!("   HTTP端口: {}", original_http_addr.port());
    println!("   TCP端口: {}", original_tcp_addr.port());
    
    // 自动解决端口冲突
    println!("⚙️  正在解析端口冲突...");
    let (http_addr, tcp_addr) = match resolve_port_conflicts(original_http_addr, original_tcp_addr) {
        Ok((http, tcp)) => {
            if http != original_http_addr {
                warn!("📍 HTTP端口已从 {} 更改为 {}", original_http_addr.port(), http.port());
                println!("   [+] HTTP端口已调整为: {}", http.port());
            } else {
                println!("   [+] HTTP端口无需调整: {}", http.port());
            }
            if tcp != original_tcp_addr {
                warn!("📍 TCP端口已从 {} 更改为 {}", original_tcp_addr.port(), tcp.port());
                println!("   [+] TCP端口已调整为: {}", tcp.port());
            } else {
                println!("   [+] TCP端口无需调整: {}", tcp.port());
            }
            (http, tcp)
        }
        Err(e) => {
            error!("❌ 端口解析失败: {}", e);
            error!("💡 请检查端口占用情况或重启相关服务");
            println!("   [-] 端口解析失败，程序退出");
            std::process::exit(1);
        }
    };
    
    println!("✅ 端口配置检查完成");
    println!("   HTTP地址: {}", http_addr);
    println!("   TCP地址: {}", tcp_addr);
    println!("   HTTP端口: {}", http_addr.port());
    println!("   TCP端口: {}", tcp_addr.port());
    
    // 初始化数据目录
    println!("📁 正在初始化数据目录...");
    println!("   📂 目标目录: ./data");
    if let Err(e) = tokio::fs::create_dir_all("data").await {
        error!("❌ Failed to create data directory: {}", e);
        println!("   [-] 数据目录创建失败: {}", e);
        std::process::exit(1);
    }
    println!("✅ 数据目录初始化完成");
    println!("   📁 数据目录: ./data");
    println!("   📄 用户数据库: ./data/users.db");
    println!("   📄 代理JSON: ./data/agents.json");
    println!("   📄 命令历史: ./data/command_history.json");
    
    // Initialize debug system
    println!("🔧 正在初始化调试系统...");
    debug::log_info("Starting C2 server initialization", "Main");
    println!("   [+] 调试系统初始化完成");
    
    // Initialize hybrid storage
    println!("💾 正在初始化混合存储...");
    println!("   🔄 正在连接数据库...");
    let storage = match hybrid_storage::HybridStorage::new().await {
        Ok(storage) => {
            debug::log_success("Hybrid storage initialized successfully", "Storage");
            info!("✅ 混合存储初始化成功");
            println!("   [+] 混合存储初始化成功");
            println!("   [+] 存储类型: SQLite + JSON");
            println!("   [+] 数据加密: 启用");
            Arc::new(storage)
        }
        Err(e) => {
            debug::log_warning(&format!("Failed to initialize hybrid storage: {}", e), "Storage");
            error!("❌ Failed to initialize hybrid storage: {}", e);
            println!("   [-] 混合存储初始化失败: {}", e);
            println!("   [+] 尝试修复JSON文件格式...");
            
            // [+] 尝试删除损坏的JSON文件并重新创建
            let _ = tokio::fs::remove_file("data/agents.json").await;
            let _ = tokio::fs::remove_file("data/command_history.json").await;
            
            // [+] 重新尝试初始化
            match hybrid_storage::HybridStorage::new().await {
                Ok(storage) => {
                    println!("   [+] 修复成功，混合存储重新初始化完成");
                    Arc::new(storage)
                }
                Err(e2) => {
                    println!("   [-] 修复失败: {}", e2);
                    std::process::exit(1);
                }
            }
        }
    };
    println!("✅ 混合存储初始化完成");
    
    // 更新配置以反映实际使用的端口
    println!("⚙️  正在更新服务器配置...");
    let mut updated_config = config.clone();
    updated_config.server.http_addr = http_addr;
    updated_config.server.tcp_addr = tcp_addr;
    println!("   [+] 配置更新完成");
    
    println!("🔗 正在初始化应用状态...");
    let state = server::AppState::with_storage(updated_config.clone(), storage);
    println!("   [+] 应用状态初始化完成");
    
    // 显示服务器状态信息（使用更新后的配置）
    println!("📊 正在显示服务器状态信息...");
    print_enhanced_server_status(&updated_config, original_http_addr.port(), original_tcp_addr.port());
    
    // Log debug information
    debug::log_success("C2 server startup completed", "Main");
    debug::log_info(&format!("Web server listening on {}", http_addr), "WebServer");
    debug::log_info(&format!("TCP listener listening on {}", tcp_addr), "TCPListener");
    
    // 启动实时状态监控
    println!("📡 正在启动实时状态监控...");
    start_status_monitor();
    println!("   [+] 状态监控已启动");
    
    // 添加连接统计监控
    println!("📊 正在启动连接统计监控...");
    let stats_state = state.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        loop {
            interval.tick().await;
            let client_count = stats_state.clients.read().await.len();
            if client_count > 0 {
                info!("📊 Active Agents: {}", client_count);
            } else {
                debug!("📊 No active agents connected");
            }
        }
    });
    println!("   [+] 连接统计监控已启动");
    
    // CORS 配置 - 动态设置允许的源
    println!("🌐 正在配置CORS策略...");
    let cors = CorsLayer::new()
        .allow_origin([
            HeaderValue::from_str(&format!("http://localhost:{}", http_addr.port())).unwrap_or_else(|_| HeaderValue::from_static("http://localhost:8080")),
            HeaderValue::from_str(&format!("http://127.0.0.1:{}", http_addr.port())).unwrap_or_else(|_| HeaderValue::from_static("http://127.0.0.1:8080"))
        ])
        .allow_methods([Method::GET, Method::POST, Method::OPTIONS])
        .allow_headers([AUTHORIZATION, ACCEPT, CONTENT_TYPE])
        .allow_credentials(true);
    println!("   [+] CORS配置完成");
    
    // 创建 Web 应用路由
    println!("🛣️  正在创建Web应用路由...");
    let app = web::create_routes()
        .layer(cors)
        .with_state(state.clone());
    println!("   [+] Web路由创建完成");
    
    // 改进的服务器启动逻辑，使用更好的错误处理
    println!("🚀 正在启动Web服务器...");
    let tcp_state = state.clone();
    let http_handle = tokio::spawn(async move {
        info!("🌐 Web 服务器启动于 {} (HTTP)", http_addr);
        println!("   [+] Web服务器已启动: http://{}", http_addr);
        
        // 尝试绑定 HTTP 地址，使用更好的错误处理
        match tokio::net::TcpListener::bind(http_addr).await {
            Ok(listener) => {
                println!("   [+] HTTP监听器绑定成功");
                match axum::serve(listener, app).await {
                    Ok(_) => {
                        info!("✅ Web server stopped gracefully");
                        println!("   [+] Web服务器正常停止");
                    }
                    Err(e) => {
                        error!("❌ HTTP server error: {}", e);
                        println!("   [-] Web服务器错误: {}", e);
                    }
                }
            }
            Err(e) => {
                error!("❌ Failed to bind HTTP address {}: {}", http_addr, e);
                error!("💡 Port {} may have been taken by another process", http_addr.port());
                println!("   [-] HTTP地址绑定失败: {}", e);
                println!("   [-] 端口 {} 可能被其他进程占用", http_addr.port());
            }
        }
    });
    
    println!("🔌 正在启动TCP监听器...");
    let tcp_handle = tokio::spawn(async move {
        info!("🔌 TCP监听器启动于 {}", tcp_addr);
        println!("   [+] TCP监听器已启动: {}", tcp_addr);
        if let Err(e) = tcp_listener::start_tcp_listener(tcp_addr, tcp_state).await {
            error!("❌ TCP listener error: {}", e);
            warn!("🔌 TCP server exited - continuing with Web server only");
            println!("   [-] TCP监听器错误: {}", e);
            println!("   [-] TCP服务器退出 - 继续运行Web服务器");
        }
    });
    
    // 改进的优雅关闭处理
    let shutdown_signal = async {
        match tokio::signal::ctrl_c().await {
            Ok(()) => {
                info!("🛑 Received shutdown signal (Ctrl+C)");
                println!("   [+] 收到关闭信号 (Ctrl+C)");
            }
            Err(err) => {
                error!("❌ Unable to listen for shutdown signal: {}", err);
                println!("   [-] 无法监听关闭信号: {}", err);
                // 如果无法监听 Ctrl+C，我们仍然可以通过其他方式关闭
            }
        }
    };
    
    // 添加超时退出机制（可选：如果需要自动退出功能）
    let timeout_shutdown = async {
        // 可以在这里添加基于时间的自动关闭逻辑
        // 例如：运行特定时间后自动关闭
        tokio::time::sleep(tokio::time::Duration::from_secs(u64::MAX)).await;
    };
    
    info!("📡 服务器已启动，按 Ctrl+C 停止服务器");
    info!("🌐 Web界面地址: http://{}", http_addr);
    info!("🔌 TCP监听地址: {}", tcp_addr);
    
    // 添加详细的操作指南
    println!("\n{}", "⭐".repeat(70));
    println!("🎉 IKUNC2 C2 SERVER SUCCESSFULLY STARTED!");
    println!("{}", "⭐".repeat(70));
    println!();
    println!("📝 QUICK START GUIDE:");
    println!("   1️⃣  Open your browser and visit: http://{}", http_addr);
    println!("   2️⃣  Login with credentials: admin/admin");
    println!("   3️⃣  Build agents to connect to: {}", tcp_addr);
    println!("   4️⃣  Press Ctrl+C anytime to stop the server gracefully");
    println!();
    println!("🔧 TROUBLESHOOTING:");
    println!("   🔹 If Web GUI is unreachable, check firewall settings");
    println!("   🔹 If agents can't connect, verify TCP port {} is open", tcp_addr.port());
    println!("   🔹 Port conflicts are automatically resolved");
    println!("   🔹 Check logs below for real-time server status");
    println!();
    println!("🛡️  SECURITY NOTES:");
    println!("   ⚠️  Change default credentials in production");
    println!("   ⚠️  Enable TLS for secure communications");
    println!("   ⚠️  Restrict network access as needed");
    println!("{}", "⭐".repeat(70));
    println!();
    
    println!("🚀 服务器已成功启动并运行在后台！");
    println!("🌐 你现在可以打开浏览器访问: http://{}", http_addr);
    println!("🔌 代理可以连接到: {}", tcp_addr);
    println!("📊 实时日志将在下方显示...");
    println!();
    
    // 等待任一服务器退出或关闭信号
    tokio::select! {
        _ = http_handle => {
            warn!("🌐 HTTP server exited");
            println!("   [-] HTTP服务器已退出");
        }
        _ = tcp_handle => {
            warn!("🔌 TCP server exited");
            println!("   [-] TCP服务器已退出");
        }
        _ = shutdown_signal => {
            info!("🔄 Starting graceful shutdown...");
            println!("   [+] 开始优雅关闭...");
        }
        _ = timeout_shutdown => {
            info!("⏰ Timeout reached, shutting down...");
            println!("   [-] 超时到达，正在关闭...");
        }
    }
    
    info!("🔄 Shutting down servers gracefully...");
    println!("🔄 正在优雅关闭服务器...");
    
    // 给服务器一些时间来完成当前请求
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
    
    info!("✅ Server shutdown completed");
    println!("✅ 服务器关闭完成");
    println!("\n{}", "=".repeat(60));
    println!("    🔴 Ikunc2 C2 Server Stopped");
    println!("    📅 Stopped at: {}", chrono::Local::now().format("%Y-%m-%d %H:%M:%S"));
    println!("    ⏱️  Total uptime: {}", format_uptime());
    println!("{}", "=".repeat(60));
}

async fn run_agent() {
    info!("🤖 Starting in agent mode");
    
    // 代理模式逻辑
    let server_ip = std::env::var("AGENT_IP").unwrap_or_else(|_| "127.0.0.1".to_string());
    let server_port = std::env::var("AGENT_PORT").unwrap_or_else(|_| "5555".to_string());
    
    info!("🔗 Connecting to C2 server: {}:{}", server_ip, server_port);
    
    // 这里应该实现代理连接逻辑
    // 目前只是一个占位符
    let mut heartbeat_count = 0;
    loop {
        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        heartbeat_count += 1;
        debug!("🤖 Agent heartbeat #{}", heartbeat_count);
        
        if heartbeat_count % 6 == 0 {
            info!("🔄 Agent status: Connected to {}:{}", server_ip, server_port);
        }
    }
}