// ===============================================================================
// Ikunc2 C2 服务器 - 配置管理模块
// 处理服务器配置的加载、保存和默认值
// ===============================================================================

use std::env;
use std::net::SocketAddr;
use serde::{Deserialize, Serialize};
use base64::Engine;

// 主配置结构体 - 包含所有服务器配置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,     // 服务器配置
    pub storage: StorageConfig,   // 存储配置
    pub security: SecurityConfig, // 安全配置
    pub logging: LoggingConfig,   // 日志配置
}

// 服务器配置 - 网络和协议设置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub http_addr: SocketAddr,    // HTTP服务器监听地址
    pub tcp_addr: SocketAddr,     // TCP监听器地址
    pub hostname: String,         // 服务器主机名
    pub port: u16,                // 主端口号
    pub enable_tls: bool,         // 是否启用TLS加密
    pub cert_path: String,        // TLS证书文件路径
    pub key_path: String,         // TLS私钥文件路径
    pub agent_protocol: String,   // 代理通信协议 (tcp, http, https)
}

// 存储配置 - 数据存储相关设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub data_dir: String,         // JSON数据存储目录
    pub auto_save_interval: u64,  // 自动保存间隔（秒）
    pub max_file_size: u64,       // 最大文件大小（字节）
}

// 安全配置 - 会话和认证相关设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub session_secret: String,      // 会话密钥
    pub session_timeout_hours: u64,  // 会话超时时间（小时）
    pub bcrypt_cost: u32,           // bcrypt加密强度
}

// 日志配置 - 日志输出和级别设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,              // 日志级别
    pub file_path: Option<String>,  // 日志文件路径（可选）
    pub enable_console: bool,       // 是否启用控制台输出
}

impl Config {
    // 加载配置文件
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        // 尝试从环境变量或配置文件加载
        // 目前返回默认配置
        Ok(Self::default())
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                http_addr: "0.0.0.0:8080".parse().unwrap(),
                tcp_addr: "0.0.0.0:5555".parse().unwrap(),
                hostname: "0.0.0.0".to_string(),
                port: 8080,
                enable_tls: false,
                cert_path: "certs/server.crt".to_string(),
                key_path: "certs/server.key".to_string(),
                agent_protocol: "http".to_string(),
            },
            storage: StorageConfig {
                // 默认 JSON 数据存储目录
                data_dir: "data/json".to_string(),
                auto_save_interval: 300, // 5分钟自动保存
                max_file_size: 10 * 1024 * 1024, // 10MB
            },
            security: SecurityConfig {
                session_secret: generate_session_secret(),
                session_timeout_hours: 24,
                bcrypt_cost: 12,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_path: None,
                enable_console: true,
            },
        }
    }
}

impl Config {
    pub fn from_env() -> Self {
        let mut config = Self::default();
        
        // Server configuration
        if let Ok(host) = env::var("IKUNC2_HOST") {
            config.server.hostname = host;
        }
        
        if let Ok(port) = env::var("IKUNC2_PORT") {
            if let Ok(port_num) = port.parse::<u16>() {
                config.server.port = port_num;
                config.server.http_addr = format!("{}:{}", config.server.hostname, port_num)
                    .parse()
                    .unwrap_or(config.server.http_addr);
            }
        }
        
        if let Ok(tcp_port) = env::var("IKUNC2_TCP_PORT") {
            if let Ok(port_num) = tcp_port.parse::<u16>() {
                config.server.tcp_addr = format!("{}:{}", config.server.hostname, port_num)
                    .parse()
                    .unwrap_or(config.server.tcp_addr);
            }
        }
        
        // Storage configuration
        if let Ok(data_dir) = env::var("IKUNC2_DATA_DIR") {
            config.storage.data_dir = data_dir;
        }
        
        if let Ok(auto_save) = env::var("IKUNC2_AUTO_SAVE_INTERVAL") {
            if let Ok(interval) = auto_save.parse::<u64>() {
                config.storage.auto_save_interval = interval;
            }
        }
        
        // TLS and protocol configuration
        if let Ok(enable_tls) = env::var("IKUNC2_ENABLE_TLS") {
            config.server.enable_tls = enable_tls.to_lowercase() == "true";
        }
        
        if let Ok(cert_path) = env::var("IKUNC2_CERT_PATH") {
            config.server.cert_path = cert_path;
        }
        
        if let Ok(key_path) = env::var("IKUNC2_KEY_PATH") {
            config.server.key_path = key_path;
        }
        
        if let Ok(protocol) = env::var("IKUNC2_AGENT_PROTOCOL") {
            config.server.agent_protocol = protocol;
        }
        
        // Security configuration
        if let Ok(session_secret) = env::var("IKUNC2_SESSION_SECRET") {
            config.security.session_secret = session_secret;
        }
        
        if let Ok(timeout) = env::var("IKUNC2_SESSION_TIMEOUT") {
            if let Ok(timeout_hours) = timeout.parse::<u64>() {
                config.security.session_timeout_hours = timeout_hours;
            }
        }
        
        // Logging configuration
        if let Ok(log_level) = env::var("IKUNC2_LOG_LEVEL") {
            config.logging.level = log_level;
        }
        
        if let Ok(log_file) = env::var("IKUNC2_LOG_FILE") {
            config.logging.file_path = Some(log_file);
        }
        
        config
    }
    
    pub fn validate(&self) -> Result<(), String> {
        // Validate server configuration
        if self.server.port == 0 {
            return Err("Invalid server port".to_string());
        }
        
        // Validate storage configuration
        if self.storage.data_dir.is_empty() {
            return Err("Storage data directory cannot be empty".to_string());
        }
        
        // Validate security configuration
        if self.security.session_secret.len() < 32 {
            return Err("Session secret must be at least 32 characters".to_string());
        }
        
        Ok(())
    }
}

fn generate_session_secret() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let bytes: Vec<u8> = (0..64).map(|_| rng.gen()).collect();
    base64::engine::general_purpose::STANDARD.encode(bytes)
}

pub fn get_config() -> Config {
    let config = Config::from_env();
    if let Err(e) = config.validate() {
        eprintln!("Configuration error: {}", e);
        std::process::exit(1);
    }
    config
} 