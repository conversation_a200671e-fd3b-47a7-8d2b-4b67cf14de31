use rcgen::{Certificate, CertificateParams, DistinguishedName};
use rustls::{ServerConfig, PrivateKey, Certificate as RustlsCertificate};
use rustls_pemfile;
use std::fs;
use std::io::BufReader;
use std::path::Path;
use std::sync::Arc;
use tracing::info;

pub fn generate_self_signed_cert() -> Result<(String, String), Box<dyn std::error::Error>> {
    let mut params = CertificateParams::new(vec!["localhost".to_string()]);
    params.distinguished_name = DistinguishedName::new();
    params.distinguished_name.push(rcgen::DnType::CommonName, "Ikunc2 C2 Server");
    params.distinguished_name.push(rcgen::DnType::OrganizationName, "Ikunc2");
    params.distinguished_name.push(rcgen::DnType::CountryName, "US");

    let cert = Certificate::from_params(params)?;
    let cert_pem = cert.serialize_pem()?;
    let key_pem = cert.serialize_private_key_pem();

    Ok((cert_pem, key_pem))
}

pub fn create_tls_config(cert_path: &str, key_path: &str) -> Result<Arc<ServerConfig>, Box<dyn std::error::Error>> {
    // If cert/key files don't exist, generate self-signed certificate
    if !Path::new(cert_path).exists() || !Path::new(key_path).exists() {
        info!("TLS certificate not found, generating self-signed certificate");
        let (cert_pem, key_pem) = generate_self_signed_cert()?;
        
        // Create certs directory if it doesn't exist
        if let Some(parent) = Path::new(cert_path).parent() {
            fs::create_dir_all(parent)?;
        }
        
        fs::write(cert_path, cert_pem)?;
        fs::write(key_path, key_pem)?;
        info!("Generated self-signed certificate: {} and {}", cert_path, key_path);
    }

    // Load certificate and private key
    let cert_file = fs::File::open(cert_path)?;
    let mut cert_reader = BufReader::new(cert_file);
    let cert_chain = rustls_pemfile::certs(&mut cert_reader)?
        .into_iter()
        .map(RustlsCertificate)
        .collect();

    let key_file = fs::File::open(key_path)?;
    let mut key_reader = BufReader::new(key_file);
    let mut keys = rustls_pemfile::pkcs8_private_keys(&mut key_reader)?;
    
    if keys.is_empty() {
        // Try RSA keys if PKCS8 doesn't work
        let key_file = fs::File::open(key_path)?;
        let mut key_reader = BufReader::new(key_file);
        keys = rustls_pemfile::rsa_private_keys(&mut key_reader)?;
    }

    if keys.is_empty() {
        return Err("No private key found".into());
    }

    let config = ServerConfig::builder()
        .with_safe_defaults()
        .with_no_client_auth()
        .with_single_cert(cert_chain, PrivateKey(keys[0].clone()))?;

    Ok(Arc::new(config))
}

pub fn get_default_cert_paths() -> (String, String) {
    let cert_dir = "certs";
    let cert_path = format!("{}/server.crt", cert_dir);
    let key_path = format!("{}/server.key", cert_dir);
    (cert_path, key_path)
} 