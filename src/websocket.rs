// ===============================================================================
// Ikunc2 C2 服务器 - WebSocket模块
// 提供实时通信支持，用于Web界面和服务器之间的双向通信
// ===============================================================================

use axum::{
    extract::{ws::{Message, WebSocket, WebSocketUpgrade}, State},
    response::Response,
    routing::get,
    Router,
};
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use tracing::{info, warn, error, debug};
use uuid::Uuid;

use crate::server::AppState;
use crate::client::ClientInfo;
use crate::logging;

/// WebSocket消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WSMessage {
    /// 客户端连接状态更新
    ClientStatusUpdate {
        client_id: String,
        status: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    /// 新客户端连接
    ClientConnected {
        client: ClientInfo,
    },
    /// 客户端断开连接
    ClientDisconnected {
        client_id: String,
    },
    /// 命令执行结果
    CommandResult {
        client_id: String,
        command: String,
        output: String,
        success: bool,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    /// 服务器统计信息
    ServerStats {
        connected_clients: usize,
        total_clients: usize,
        uptime: u64,
        memory_usage: u64,
        cpu_usage: f32,
    },
    /// 系统日志事件
    LogEvent {
        level: String,
        message: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    /// 心跳消息
    Ping,
    Pong,
    /// 错误消息
    Error {
        message: String,
    },
}

/// WebSocket连接管理器
pub struct WebSocketManager {
    connections: Arc<RwLock<HashMap<String, broadcast::Sender<WSMessage>>>>,
    broadcast_tx: broadcast::Sender<WSMessage>,
}

impl WebSocketManager {
    pub fn new() -> Self {
        let (broadcast_tx, _) = broadcast::channel(1000);
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            broadcast_tx,
        }
    }

    /// 添加新的WebSocket连接
    pub async fn add_connection(&self, connection_id: String, tx: broadcast::Sender<WSMessage>) {
        let mut connections = self.connections.write().await;
        connections.insert(connection_id.clone(), tx);
        info!("WebSocket连接已添加: {}", connection_id);
    }

    /// 移除WebSocket连接
    pub async fn remove_connection(&self, connection_id: &str) {
        let mut connections = self.connections.write().await;
        connections.remove(connection_id);
        info!("WebSocket连接已移除: {}", connection_id);
    }

    /// 广播消息到所有连接
    pub async fn broadcast(&self, message: WSMessage) {
        if let Err(e) = self.broadcast_tx.send(message.clone()) {
            warn!("广播消息失败: {}", e);
        }
        
        let connections = self.connections.read().await;
        for (id, tx) in connections.iter() {
            if let Err(e) = tx.send(message.clone()) {
                debug!("发送消息到连接 {} 失败: {}", id, e);
            }
        }
    }

    /// 发送消息到特定连接
    pub async fn send_to_connection(&self, connection_id: &str, message: WSMessage) {
        let connections = self.connections.read().await;
        if let Some(tx) = connections.get(connection_id) {
            if let Err(e) = tx.send(message) {
                warn!("发送消息到连接 {} 失败: {}", connection_id, e);
            }
        }
    }

    /// 获取活跃连接数
    pub async fn active_connections(&self) -> usize {
        self.connections.read().await.len()
    }
}

// 全局WebSocket管理器
lazy_static::lazy_static! {
    pub static ref WS_MANAGER: WebSocketManager = WebSocketManager::new();
}

/// 启动WebSocket服务器
pub async fn start_websocket_server(addr: SocketAddr, app_state: AppState) -> Result<(), anyhow::Error> {
    let app = Router::new()
        .route("/ws", get(websocket_handler))
        .with_state(app_state);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    info!("WebSocket服务器监听于: {}", addr);

    axum::serve(listener, app).await?;
    Ok(())
}

/// WebSocket连接处理器
async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(app_state): State<AppState>,
) -> Response {
    ws.on_upgrade(|socket| handle_websocket(socket, app_state))
}

/// 处理WebSocket连接
async fn handle_websocket(socket: WebSocket, app_state: AppState) {
    let connection_id = Uuid::new_v4().to_string();
    let (mut sender, mut receiver) = socket.split();
    
    // 创建广播通道
    let (tx, mut rx) = broadcast::channel(100);
    
    // 注册连接
    WS_MANAGER.add_connection(connection_id.clone(), tx).await;
    
    logging::audit_log("websocket_connected", None, &format!("Connection ID: {}", connection_id));

    // 发送欢迎消息
    let welcome_stats = get_server_stats(&app_state).await;
    if let Ok(msg) = serde_json::to_string(&welcome_stats) {
        if sender.send(Message::Text(msg)).await.is_err() {
            error!("发送欢迎消息失败");
            return;
        }
    }

    // 处理接收到的消息
    let app_state_clone = app_state.clone();
    let connection_id_clone = connection_id.clone();
    let receive_task = tokio::spawn(async move {
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    if let Err(e) = handle_websocket_message(text, &app_state_clone).await {
                        warn!("处理WebSocket消息失败: {}", e);
                    }
                }
                Ok(Message::Binary(_)) => {
                    warn!("收到二进制消息，暂不支持");
                }
                Ok(Message::Ping(ping)) => {
                    debug!("收到Ping消息");
                    // Axum会自动回复Pong
                }
                Ok(Message::Pong(_)) => {
                    debug!("收到Pong消息");
                }
                Ok(Message::Close(_)) => {
                    info!("WebSocket连接关闭: {}", connection_id_clone);
                    break;
                }
                Err(e) => {
                    error!("WebSocket错误: {}", e);
                    break;
                }
            }
        }
    });

    // 处理广播消息
    let send_task = tokio::spawn(async move {
        while let Ok(msg) = rx.recv().await {
            if let Ok(json) = serde_json::to_string(&msg) {
                if sender.send(Message::Text(json)).await.is_err() {
                    break;
                }
            }
        }
    });

    // 等待任务完成
    tokio::select! {
        _ = receive_task => {},
        _ = send_task => {},
    }

    // 清理连接
    WS_MANAGER.remove_connection(&connection_id).await;
    logging::audit_log("websocket_disconnected", None, &format!("Connection ID: {}", connection_id));
}

/// 处理WebSocket消息
async fn handle_websocket_message(text: String, app_state: &AppState) -> Result<(), anyhow::Error> {
    let message: serde_json::Value = serde_json::from_str(&text)?;
    
    if let Some(msg_type) = message.get("type").and_then(|v| v.as_str()) {
        match msg_type {
            "get_stats" => {
                let stats = get_server_stats(app_state).await;
                WS_MANAGER.broadcast(stats).await;
            }
            "ping" => {
                WS_MANAGER.broadcast(WSMessage::Pong).await;
            }
            _ => {
                warn!("未知的WebSocket消息类型: {}", msg_type);
            }
        }
    }
    
    Ok(())
}

/// 获取服务器统计信息
async fn get_server_stats(app_state: &AppState) -> WSMessage {
    let clients = app_state.clients.read().await;
    let connected_clients = clients.values().filter(|c| c.is_connected).count();
    let total_clients = clients.len();

    // 获取系统信息
    let sys = sysinfo::System::new_all();
    let memory_usage = sys.used_memory();
    let cpu_usage = sys.global_cpu_info().cpu_usage();

    WSMessage::ServerStats {
        connected_clients,
        total_clients,
        uptime: 0, // TODO: 实现运行时间计算
        memory_usage,
        cpu_usage,
    }
}

/// 广播客户端状态更新
pub async fn broadcast_client_status(client_id: &str, status: &str) {
    let message = WSMessage::ClientStatusUpdate {
        client_id: client_id.to_string(),
        status: status.to_string(),
        timestamp: chrono::Utc::now(),
    };
    WS_MANAGER.broadcast(message).await;
}

/// 广播新客户端连接
pub async fn broadcast_client_connected(client: ClientInfo) {
    let message = WSMessage::ClientConnected { client };
    WS_MANAGER.broadcast(message).await;
}

/// 广播客户端断开连接
pub async fn broadcast_client_disconnected(client_id: &str) {
    let message = WSMessage::ClientDisconnected {
        client_id: client_id.to_string(),
    };
    WS_MANAGER.broadcast(message).await;
}

/// 广播命令执行结果
pub async fn broadcast_command_result(
    client_id: &str,
    command: &str,
    output: &str,
    success: bool,
) {
    let message = WSMessage::CommandResult {
        client_id: client_id.to_string(),
        command: command.to_string(),
        output: output.to_string(),
        success,
        timestamp: chrono::Utc::now(),
    };
    WS_MANAGER.broadcast(message).await;
} 