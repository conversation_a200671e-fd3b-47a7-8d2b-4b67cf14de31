// ===============================================================================
// Ikunc2 C2 服务器 - 增强日志模块
// 提供结构化日志、文件轮转和多种输出格式
// ===============================================================================

use tracing_subscriber::{
    fmt::{self, time::LocalTime},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};
use tracing_appender::{non_blocking, rolling};
use std::fs;
use std::path::Path;
use anyhow::Result;

/// 初始化增强日志系统
pub fn init_logging() -> Result<()> {
    // 创建日志目录
    let logs_dir = "logs";
    if !Path::new(logs_dir).exists() {
        fs::create_dir_all(logs_dir)?;
    }

    // 文件滚动日志（每天一个文件）
    let file_appender = rolling::daily(logs_dir, "ikunc2.log");
    let (file_writer, _guard) = non_blocking(file_appender);

    // 控制台输出层 - 人类可读格式
    let console_layer = fmt::layer()
        .with_timer(LocalTime::rfc_3339())
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true)
        .with_ansi(true)
        .with_filter(EnvFilter::from_default_env().add_directive("ikunc2=info".parse()?));

    // 文件输出层 - JSON格式便于日志分析
    let file_layer = fmt::layer()
        .json()
        .with_timer(LocalTime::rfc_3339())
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true)
        .with_current_span(true)
        .with_span_list(true)
        .with_writer(file_writer)
        .with_filter(EnvFilter::from_default_env().add_directive("ikunc2=debug".parse()?));

    // 组合所有层
    tracing_subscriber::registry()
        .with(console_layer)
        .with(file_layer)
        .init();

    // 保持guard活跃以防止日志丢失
    std::mem::forget(_guard);

    Ok(())
}

/// 日志级别枚举
#[derive(Debug, Clone)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

/// 结构化日志事件
#[derive(Debug, Clone, serde::Serialize)]
pub struct LogEvent {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: String,
    pub message: String,
    pub module: String,
    pub client_id: Option<String>,
    pub command: Option<String>,
    pub metadata: std::collections::HashMap<String, serde_json::Value>,
}

/// 安全审计日志
pub fn audit_log(event: &str, client_id: Option<&str>, details: &str) {
    tracing::info!(
        event_type = "security_audit",
        event = event,
        client_id = client_id,
        details = details,
        "Security audit event"
    );
}

/// 命令执行日志
pub fn command_log(client_id: &str, command: &str, success: bool, output_length: usize) {
    tracing::info!(
        event_type = "command_execution",
        client_id = client_id,
        command = command,
        success = success,
        output_length = output_length,
        "Command executed"
    );
}

/// 客户端连接日志
pub fn client_log(client_id: &str, event: &str, ip: &str) {
    tracing::info!(
        event_type = "client_connection",
        client_id = client_id,
        event = event,
        ip = ip,
        "Client connection event"
    );
}

/// 系统性能日志
pub fn performance_log(metric: &str, value: f64, unit: &str) {
    tracing::debug!(
        event_type = "performance_metric",
        metric = metric,
        value = value,
        unit = unit,
        "Performance metric"
    );
}

/// 错误日志带上下文
pub fn error_with_context(error: &anyhow::Error, context: &str, client_id: Option<&str>) {
    tracing::error!(
        event_type = "error_with_context",
        error = %error,
        context = context,
        client_id = client_id,
        "Error occurred with context"
    );
} 