// ===============================================================================
// Ikunc2 C2 服务器 - 混合存储模块
// 结合SQLite数据库和JSON文件实现高效的数据存储
// SQLite用于结构化数据，JSON用于灵活的配置和缓存
// ===============================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use chrono::{DateTime, Utc};
use tracing::info;
use bcrypt::{hash, verify, DEFAULT_COST};
use sqlx::{SqlitePool, Row};
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::fs;
use anyhow;
use tracing::warn;

// 用户信息结构（存储在 SQLite 数据库中）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub password_hash: String,
    pub is_active: bool,
    pub last_login: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Agent 信息结构（存储在 JSON 文件中）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Agent {
    pub id: String,
    pub pc_name: String,
    pub ip_address: String,
    pub username: String,
    pub process_name: Option<String>,
    pub os_type: String,
    pub is_connected: bool,
    pub first_seen: DateTime<Utc>,
    pub last_seen: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

// 命令历史结构（存储在 JSON 文件中）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandHistory {
    pub id: String,
    pub client_id: String,
    pub command: String,
    pub output: Option<String>,
    pub success: bool,
    pub executed_at: DateTime<Utc>,
}

// 会话信息结构（存储在 SQLite 数据库中）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub data: serde_json::Value,
    pub expiry: DateTime<Utc>,
}

// 混合存储管理器
#[derive(Debug, Clone)]
pub struct HybridStorage {
    // SQLite 数据库（用于用户和会话）
    db_pool: SqlitePool,
    data_dir: PathBuf,
    
    // JSON 存储（用于 Agent 信息）
    agents: Arc<RwLock<HashMap<String, Agent>>>,
    command_history: Arc<RwLock<HashMap<String, CommandHistory>>>,
    
    // 文件路径
    agents_file: PathBuf,
    command_history_file: PathBuf,
}

impl HybridStorage {
    pub async fn new() -> Result<Self, anyhow::Error> {
        let data_dir = Self::get_data_dir()?;
        
        // 初始化 SQLite 数据库（仅用于用户和会话）
        let db_path = data_dir.join("users.db");
        
        // Ensure the database file can be created
        if let Some(parent) = db_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        let database_url = format!("sqlite:{}?mode=rwc", db_path.to_string_lossy());
        info!("Connecting to database: {}", database_url);
        
        let db_pool = SqlitePool::connect(&database_url).await
            .map_err(|e| anyhow::anyhow!("Failed to connect to database {}: {}", database_url, e))?;
        
        // JSON 文件路径
        let agents_file = data_dir.join("agents.json");
        let command_history_file = data_dir.join("command_history.json");
        
        let storage = Self {
            db_pool,
            data_dir,
            agents: Arc::new(RwLock::new(HashMap::new())),
            command_history: Arc::new(RwLock::new(HashMap::new())),
            agents_file,
            command_history_file,
        };
        
        // 初始化数据库表（仅用户和会话）
        storage.init_database_tables().await?;
        storage.create_default_admin().await?;
        
        // 加载 JSON 数据
        storage.load_agents().await?;
        storage.load_command_history().await?;
        
        info!("Hybrid storage initialized:");
        info!("- SQLite database (users & sessions): {}", db_path.display());
        info!("- JSON agents file: {}", storage.agents_file.display());
        info!("- JSON command history file: {}", storage.command_history_file.display());
        info!("Hybrid storage system initialized successfully");
        info!("- Users & Sessions: SQLite database");
        info!("- Agents & Commands: JSON files");
        
        Ok(storage)
    }
    
    // 获取数据目录
    fn get_data_dir() -> Result<PathBuf, anyhow::Error> {
        // Use current working directory instead of executable path for development
        let current_dir = std::env::current_dir()?;
        let data_dir = current_dir.join("data");
        
        // Ensure the data directory exists
        if !data_dir.exists() {
            std::fs::create_dir_all(&data_dir)?;
            info!("Created data directory: {}", data_dir.display());
        }
        
        Ok(data_dir)
    }
    
    // 初始化 SQLite 数据库表（仅用户和会话）
    async fn init_database_tables(&self) -> Result<(), sqlx::Error> {
        // 用户表
        sqlx::query(
            r#"CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )"#,
        ).execute(&self.db_pool).await?;
        
        // 会话表
        sqlx::query(
            r#"CREATE TABLE IF NOT EXISTS sessions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                data TEXT NOT NULL,
                expiry DATETIME NOT NULL
            )"#,
        ).execute(&self.db_pool).await?;
        
        // 创建索引
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_sessions_expiry ON sessions(expiry)").execute(&self.db_pool).await?;
        
        info!("SQLite database tables initialized (users & sessions only)");
        Ok(())
    }
    
    // 创建默认管理员用户
    async fn create_default_admin(&self) -> Result<(), anyhow::Error> {
        let admin_exists = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE username = 'admin'"
        ).fetch_one(&self.db_pool).await?;
        
        if admin_exists == 0 {
            let user_id = uuid::Uuid::new_v4().to_string();
            let password_hash = hash("admin", DEFAULT_COST)?;
            let now = Utc::now();
            
            sqlx::query(
                r#"INSERT INTO users (id, username, password_hash, is_active, created_at, updated_at) 
                   VALUES (?, ?, ?, ?, ?, ?)"#,
            )
            .bind(&user_id)
            .bind("admin")
            .bind(&password_hash)
            .bind(true)
            .bind(now)
            .bind(now)
            .execute(&self.db_pool).await?;
            
            info!("Default admin user created (username: admin, password: admin)");
        }
        Ok(())
    }
    
    // === 用户管理（SQLite 数据库）===
    
    pub async fn authenticate_user(&self, username: &str, password: &str) -> Result<Option<User>, anyhow::Error> {
        let row = sqlx::query(
            "SELECT id, username, password_hash, is_active, last_login, created_at, updated_at FROM users WHERE username = ? AND is_active = TRUE"
        )
        .bind(username)
        .fetch_optional(&self.db_pool).await?;
        
        if let Some(row) = row {
            let password_hash: String = row.get("password_hash");
            if verify(password, &password_hash)? {
                let user = User {
                    id: row.get("id"),
                    username: row.get("username"),
                    password_hash,
                    is_active: row.get("is_active"),
                    last_login: row.get("last_login"),
                    created_at: row.get("created_at"),
                    updated_at: row.get("updated_at"),
                };
                return Ok(Some(user));
            }
        }
        Ok(None)
    }
    
    pub async fn update_user_last_login(&self, user_id: &str) -> Result<(), anyhow::Error> {
        let now = Utc::now();
        sqlx::query(
            "UPDATE users SET last_login = ?, updated_at = ? WHERE id = ?"
        )
        .bind(now)
        .bind(now)
        .bind(user_id)
        .execute(&self.db_pool).await?;
        Ok(())
    }
    
    pub async fn create_user(&self, username: &str, password: &str) -> Result<User, anyhow::Error> {
        let user_id = uuid::Uuid::new_v4().to_string();
        let password_hash = hash(password, DEFAULT_COST)?;
        let now = Utc::now();
        
        sqlx::query(
            r#"INSERT INTO users (id, username, password_hash, is_active, created_at, updated_at) 
               VALUES (?, ?, ?, ?, ?, ?)"#,
        )
        .bind(&user_id)
        .bind(username)
        .bind(&password_hash)
        .bind(true)
        .bind(now)
        .bind(now)
        .execute(&self.db_pool).await?;
        
        Ok(User {
            id: user_id,
            username: username.to_string(),
            password_hash,
            is_active: true,
            last_login: None,
            created_at: now,
            updated_at: now,
        })
    }
    
    // === Agent 管理（JSON 文件）===
    
    async fn load_agents(&self) -> Result<(), anyhow::Error> {
        if self.agents_file.exists() {
            let content = fs::read_to_string(&self.agents_file).await?;
            
            // [+] 添加JSON格式验证
            if content.trim().is_empty() {
                info!("[+] Agents JSON file is empty, starting with empty collection");
                return Ok(());
            }
            
            // [+] 尝试解析JSON，如果失败则创建新的空文件
            match serde_json::from_str::<HashMap<String, Agent>>(&content) {
                Ok(agents) => {
                    let mut storage_agents = self.agents.write().await;
                    *storage_agents = agents;
                    info!("[+] Loaded {} agents from JSON file", storage_agents.len());
                }
                Err(e) => {
                    warn!("[-] Failed to parse agents JSON file: {}", e);
                    info!("[+] Creating new empty agents file");
                    // [+] 创建新的空JSON文件
                    let empty_agents: HashMap<String, Agent> = HashMap::new();
                    let content = serde_json::to_string_pretty(&empty_agents)?;
                    fs::write(&self.agents_file, content).await?;
                }
            }
        } else {
            info!("[+] Agents JSON file not found, starting with empty collection");
            // [+] 创建新的空JSON文件
            let empty_agents: HashMap<String, Agent> = HashMap::new();
            let content = serde_json::to_string_pretty(&empty_agents)?;
            fs::write(&self.agents_file, content).await?;
        }
        Ok(())
    }
    
    async fn save_agents(&self) -> Result<(), anyhow::Error> {
        let agents = self.agents.read().await;
        let content = serde_json::to_string_pretty(&*agents)?;
        fs::write(&self.agents_file, content).await?;
        Ok(())
    }
    
    pub async fn add_agent(&self, agent: Agent) -> Result<(), anyhow::Error> {
        {
            let mut agents = self.agents.write().await;
            agents.insert(agent.id.clone(), agent);
        }
        self.save_agents().await?;
        Ok(())
    }
    
    pub async fn update_agent_status(&self, agent_id: &str, is_connected: bool) -> Result<(), anyhow::Error> {
        {
            let mut agents = self.agents.write().await;
            if let Some(agent) = agents.get_mut(agent_id) {
                agent.is_connected = is_connected;
                agent.last_seen = Utc::now();
            }
        }
        self.save_agents().await?;
        Ok(())
    }
    
    pub async fn get_all_agents(&self) -> Result<Vec<Agent>, anyhow::Error> {
        let agents = self.agents.read().await;
        let mut agent_list: Vec<Agent> = agents.values().cloned().collect();
        agent_list.sort_by(|a, b| b.last_seen.cmp(&a.last_seen));
        Ok(agent_list)
    }
    
    pub async fn remove_agent(&self, agent_id: &str) -> Result<(), anyhow::Error> {
        {
            let mut agents = self.agents.write().await;
            agents.remove(agent_id);
        }
        self.save_agents().await?;
        Ok(())
    }
    
    // === 命令历史管理（JSON 文件）===
    
    async fn load_command_history(&self) -> Result<(), anyhow::Error> {
        if self.command_history_file.exists() {
            let content = fs::read_to_string(&self.command_history_file).await?;
            
            // [+] 添加JSON格式验证
            if content.trim().is_empty() {
                info!("[+] Command history JSON file is empty, starting with empty collection");
                return Ok(());
            }
            
            // [+] 尝试解析JSON，如果失败则创建新的空文件
            match serde_json::from_str::<HashMap<String, CommandHistory>>(&content) {
                Ok(history) => {
                    let mut storage_history = self.command_history.write().await;
                    *storage_history = history;
                    info!("[+] Loaded {} command history entries from JSON file", storage_history.len());
                }
                Err(e) => {
                    warn!("[-] Failed to parse command history JSON file: {}", e);
                    info!("[+] Creating new empty command history file");
                    // [+] 创建新的空JSON文件
                    let empty_history: HashMap<String, CommandHistory> = HashMap::new();
                    let content = serde_json::to_string_pretty(&empty_history)?;
                    fs::write(&self.command_history_file, content).await?;
                }
            }
        } else {
            info!("[+] Command history JSON file not found, starting with empty collection");
            // [+] 创建新的空JSON文件
            let empty_history: HashMap<String, CommandHistory> = HashMap::new();
            let content = serde_json::to_string_pretty(&empty_history)?;
            fs::write(&self.command_history_file, content).await?;
        }
        Ok(())
    }
    
    async fn save_command_history(&self) -> Result<(), anyhow::Error> {
        let history = self.command_history.read().await;
        let content = serde_json::to_string_pretty(&*history)?;
        fs::write(&self.command_history_file, content).await?;
        Ok(())
    }
    
    pub async fn add_command_history(&self, history: CommandHistory) -> Result<(), anyhow::Error> {
        {
            let mut cmd_history = self.command_history.write().await;
            cmd_history.insert(history.id.clone(), history);
        }
        self.save_command_history().await?;
        Ok(())
    }
    
    pub async fn get_command_history(&self, client_id: &str, limit: usize) -> Result<Vec<CommandHistory>, anyhow::Error> {
        let cmd_history = self.command_history.read().await;
        let mut filtered: Vec<CommandHistory> = cmd_history
            .values()
            .filter(|h| h.client_id == client_id)
            .cloned()
            .collect();
        
        filtered.sort_by(|a, b| b.executed_at.cmp(&a.executed_at));
        filtered.truncate(limit);
        Ok(filtered)
    }
    
    pub async fn cleanup_old_command_history(&self, days: i64) -> Result<(), anyhow::Error> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days);
        let mut removed_count = 0;
        
        {
            let mut cmd_history = self.command_history.write().await;
            cmd_history.retain(|_, h| {
                if h.executed_at < cutoff_date {
                    removed_count += 1;
                    false
                } else {
                    true
                }
            });
        }
        
        if removed_count > 0 {
            self.save_command_history().await?;
            info!("Cleaned up {} old command history entries", removed_count);
        }
        
        Ok(())
    }
    
    // === 会话管理（SQLite 数据库）===
    
    pub async fn save_session(&self, session: Session) -> Result<(), anyhow::Error> {
        let data_json = serde_json::to_string(&session.data)?;
        sqlx::query(
            "INSERT OR REPLACE INTO sessions (id, user_id, data, expiry) VALUES (?, ?, ?, ?)"
        )
        .bind(&session.id)
        .bind(&session.user_id)
        .bind(&data_json)
        .bind(session.expiry)
        .execute(&self.db_pool).await?;
        Ok(())
    }
    
    pub async fn get_session(&self, session_id: &str) -> Result<Option<Session>, anyhow::Error> {
        let row = sqlx::query(
            "SELECT id, user_id, data, expiry FROM sessions WHERE id = ? AND expiry > CURRENT_TIMESTAMP"
        )
        .bind(session_id)
        .fetch_optional(&self.db_pool).await?;
        
        if let Some(row) = row {
            let data_json: String = row.get("data");
            let data = serde_json::from_str(&data_json)?;
            Ok(Some(Session {
                id: row.get("id"),
                user_id: row.get("user_id"),
                data,
                expiry: row.get("expiry"),
            }))
        } else {
            Ok(None)
        }
    }
    
    pub async fn delete_session(&self, session_id: &str) -> Result<(), anyhow::Error> {
        sqlx::query("DELETE FROM sessions WHERE id = ?")
            .bind(session_id)
            .execute(&self.db_pool).await?;
        Ok(())
    }
    
    pub async fn cleanup_expired_sessions(&self) -> Result<(), anyhow::Error> {
        let deleted = sqlx::query("DELETE FROM sessions WHERE expiry <= CURRENT_TIMESTAMP")
            .execute(&self.db_pool).await?;
        
        if deleted.rows_affected() > 0 {
            info!("Cleaned up {} expired sessions", deleted.rows_affected());
        }
        Ok(())
    }
    
    // === 统计信息 ===
    
    pub async fn get_stats(&self) -> Result<HashMap<String, i64>, anyhow::Error> {
        let mut stats = HashMap::new();
        
        // SQLite 统计
        let users_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users").fetch_one(&self.db_pool).await?;
        let sessions_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM sessions WHERE expiry > CURRENT_TIMESTAMP").fetch_one(&self.db_pool).await?;
        
        // JSON 统计
        let agents_count = self.agents.read().await.len() as i64;
        let active_agents = self.agents.read().await.values().filter(|a| a.is_connected).count() as i64;
        let commands_count = self.command_history.read().await.len() as i64;
        
        stats.insert("users".to_string(), users_count);
        stats.insert("agents".to_string(), agents_count);
        stats.insert("active_agents".to_string(), active_agents);
        stats.insert("commands".to_string(), commands_count);
        stats.insert("sessions".to_string(), sessions_count);
        
        Ok(stats)
    }
    
    // === 获取数据库路径信息 ===
    
    pub fn get_storage_info(&self) -> HashMap<String, String> {
        let mut info = HashMap::new();
        info.insert("users_db".to_string(), self.data_dir.join("users.db").to_string_lossy().to_string());
        info.insert("agents_json".to_string(), self.agents_file.to_string_lossy().to_string());
        info.insert("command_history_json".to_string(), self.command_history_file.to_string_lossy().to_string());
        info.insert("data_dir".to_string(), self.data_dir.to_string_lossy().to_string());
        info
    }
}

// 全局存储实例
static STORAGE: std::sync::OnceLock<HybridStorage> = std::sync::OnceLock::new();

// 初始化混合存储
pub async fn init_hybrid_storage() -> Result<(), anyhow::Error> {
    let storage = HybridStorage::new().await?;
    STORAGE.set(storage).unwrap();
    info!("Hybrid storage system initialized successfully");
    info!("- Users & Sessions: SQLite database");
    info!("- Agents & Commands: JSON files");
    Ok(())
}

// 使用配置初始化存储 - 主入口函数使用
pub async fn init_storage(_config: &crate::config::StorageConfig) -> Result<(), anyhow::Error> {
    init_hybrid_storage().await
}

// 获取存储实例
pub fn get_storage() -> &'static HybridStorage {
    STORAGE.get().expect("Hybrid storage not initialized")
}

// 获取存储路径信息
pub fn get_storage_paths() -> HashMap<String, String> {
    get_storage().get_storage_info()
} 