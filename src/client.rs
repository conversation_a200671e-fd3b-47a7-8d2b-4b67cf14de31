// ===============================================================================
// Ikunc2 C2 Client Information Module
// Data structures and utilities for managing client/agent information
// ===============================================================================

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Client/Agent information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientInfo {
    pub id: String,
    pub pc_name: String,
    pub ip: String,
    pub username: String,
    pub process_name: Option<String>,
    pub is_connected: bool,
    pub operating_system: Option<String>,
    pub architecture: Option<String>,
    pub first_seen: Option<DateTime<Utc>>,
    pub last_seen: Option<DateTime<Utc>>,
    pub capabilities: Vec<String>,
    pub metadata: HashMap<String, String>,
}

impl ClientInfo {
    pub fn new(
        pc_name: String,
        ip: String,
        username: String,
        process_name: Option<String>,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            pc_name,
            ip,
            username,
            process_name,
            is_connected: true,
            operating_system: None,
            architecture: None,
            first_seen: Some(Utc::now()),
            last_seen: Some(Utc::now()),
            capabilities: vec!["basic_commands".to_string()],
            metadata: HashMap::new(),
        }
    }

    pub fn update_last_seen(&mut self) {
        self.last_seen = Some(Utc::now());
    }

    pub fn set_connection_status(&mut self, connected: bool) {
        self.is_connected = connected;
        self.update_last_seen();
    }

    pub fn add_capability(&mut self, capability: String) {
        if !self.capabilities.contains(&capability) {
            self.capabilities.push(capability);
        }
    }

    pub fn set_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }

    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }
}

/// 客户端管理器
pub struct ClientManager {
    clients: HashMap<String, ClientInfo>,
}

impl ClientManager {
    pub fn new() -> Self {
        Self {
            clients: HashMap::new(),
        }
    }

    pub fn add_client(&mut self, client: ClientInfo) {
        self.clients.insert(client.id.clone(), client);
    }

    pub fn get_client(&self, id: &str) -> Option<&ClientInfo> {
        self.clients.get(id)
    }

    pub fn get_client_mut(&mut self, id: &str) -> Option<&mut ClientInfo> {
        self.clients.get_mut(id)
    }

    pub fn remove_client(&mut self, id: &str) -> Option<ClientInfo> {
        self.clients.remove(id)
    }

    pub fn update_client_status(&mut self, id: &str, is_connected: bool) {
        if let Some(client) = self.clients.get_mut(id) {
            client.is_connected = is_connected;
            if is_connected {
                client.update_last_seen();
            }
        }
    }

    pub fn get_all_clients(&self) -> Vec<&ClientInfo> {
        self.clients.values().collect()
    }

    pub fn get_connected_clients(&self) -> Vec<&ClientInfo> {
        self.clients.values().filter(|c| c.is_connected).collect()
    }

    pub fn client_count(&self) -> usize {
        self.clients.len()
    }

    pub fn connected_count(&self) -> usize {
        self.clients.values().filter(|c| c.is_connected).count()
    }
}

impl Default for ClientManager {
    fn default() -> Self {
        Self::new()
    }
}