// ===============================================================================
// Ikunc2 C2 Library - Core Types and Error Handling
// Modern Rust command and control framework
// ===============================================================================

pub mod client;
pub mod server;
pub mod web;
pub mod tcp_listener;
pub mod agent_builder;
pub mod hybrid_storage;
pub mod config;
pub mod debug;
pub mod terminal_manager;
pub mod circuit_diagrams;
pub mod agent_attributes;
pub mod custom_agent_editor;
pub mod enhanced_capabilities;
pub mod enhanced_capabilities_web;
pub mod agent_generator;
pub mod shellcode_generator;

use thiserror::Error;
use crate::custom_agent_editor::CustomAgentEditor;

/// Comprehensive error types for the C2 framework
#[derive(Error, Debug)]
pub enum C2Error {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Agent error: {0}")]
    Agent(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Storage error: {0}")]
    Storage(String),
    
    #[error("Command execution error: {0}")]
    CommandExecution(String),
    
    #[error("Protocol error: {0}")]
    Protocol(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Resource not found: {0}")]
    NotFound(String),
    
    #[error("Permission denied: {0}")]
    PermissionDenied(String),
    
    #[error("Timeout error: {0}")]
    Timeout(String),
    
    #[error("Internal error: {0}")]
    Internal(String),
}

impl C2Error {
    pub fn network<T: std::fmt::Display>(msg: T) -> Self {
        Self::Network(msg.to_string())
    }
    
    pub fn authentication<T: std::fmt::Display>(msg: T) -> Self {
        Self::Authentication(msg.to_string())
    }
    
    pub fn agent<T: std::fmt::Display>(msg: T) -> Self {
        Self::Agent(msg.to_string())
    }
    
    pub fn configuration<T: std::fmt::Display>(msg: T) -> Self {
        Self::Configuration(msg.to_string())
    }
    
    pub fn storage<T: std::fmt::Display>(msg: T) -> Self {
        Self::Storage(msg.to_string())
    }
    
    pub fn command_execution<T: std::fmt::Display>(msg: T) -> Self {
        Self::CommandExecution(msg.to_string())
    }
    
    pub fn protocol<T: std::fmt::Display>(msg: T) -> Self {
        Self::Protocol(msg.to_string())
    }
    
    pub fn validation<T: std::fmt::Display>(msg: T) -> Self {
        Self::Validation(msg.to_string())
    }
    
    pub fn not_found<T: std::fmt::Display>(msg: T) -> Self {
        Self::NotFound(msg.to_string())
    }
    
    pub fn permission_denied<T: std::fmt::Display>(msg: T) -> Self {
        Self::PermissionDenied(msg.to_string())
    }
    
    pub fn timeout<T: std::fmt::Display>(msg: T) -> Self {
        Self::Timeout(msg.to_string())
    }
    
    pub fn internal<T: std::fmt::Display>(msg: T) -> Self {
        Self::Internal(msg.to_string())
    }
}

pub type Result<T> = std::result::Result<T, C2Error>;

/// Common response wrapper for API endpoints
#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub message: String,
    pub data: Option<T>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            message: "Operation completed successfully".to_string(),
            data: Some(data),
            timestamp: chrono::Utc::now(),
        }
    }
    
    pub fn error<E: std::fmt::Display>(error: E) -> Self {
        Self {
            success: false,
            message: error.to_string(),
            data: None,
            timestamp: chrono::Utc::now(),
        }
    }
}

/// Metrics and monitoring structures
#[derive(serde::Serialize, serde::Deserialize, Debug, Clone)]
pub struct SystemMetrics {
    pub active_agents: usize,
    pub total_commands: u64,
    pub uptime_seconds: u64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub network_connections: usize,
    pub storage_usage_mb: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Enhanced logging configuration
#[derive(Debug, Clone)]
pub struct LogConfig {
    pub level: String,
    pub file: Option<String>,
    pub console: bool,
}

/// Initialize enhanced logging with tracing
pub fn init_logging(config: &LogConfig) -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, Layer};
    
    let level_filter = match config.level.as_str() {
        "trace" => tracing::Level::TRACE,
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };
    
    let mut layers = Vec::new();
    
    if config.console {
        layers.push(
            tracing_subscriber::fmt::layer()
                .with_target(true)
                .with_level(true)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true)
                .boxed(),
        );
    }
    
    if let Some(file_path) = &config.file {
        let file_appender = tracing_appender::rolling::daily("logs", file_path);
        let (non_blocking, _guard) = tracing_appender::non_blocking(file_appender);
        
        layers.push(
            tracing_subscriber::fmt::layer()
                .with_writer(non_blocking)
                .with_ansi(false)
                .with_target(true)
                .with_level(true)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true)
                .boxed(),
        );
    }
    
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("c2_gui={}", level_filter).into()),
        )
        .with(layers)
        .init();
    
    color_eyre::install().map_err(|e| C2Error::Internal(e.to_string()))?;
    
    tracing::info!("Enhanced logging initialized with level: {}", config.level);
    Ok(())
}

/// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const AUTHORS: &str = env!("CARGO_PKG_AUTHORS");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

// Global state for custom agent editor
lazy_static::lazy_static! {
    pub static ref CUSTOM_AGENT_EDITOR: std::sync::Arc<tokio::sync::RwLock<CustomAgentEditor>> = 
        std::sync::Arc::new(tokio::sync::RwLock::new(CustomAgentEditor::new()));
}

pub fn version_info() -> String {
    format!("{} v{} - {}", NAME, VERSION, DESCRIPTION)
} 

// ========== Main entry logic migrated from main.rs ========== //
use std::sync::Arc;

use tracing::{info, error, warn, debug};
use tower_http::cors::CorsLayer;
use axum::http::header::{AUTHORIZATION, ACCEPT, CONTENT_TYPE};
use axum::http::Method;
use axum::http::HeaderValue;
use chrono::Timelike;
use std::net::{SocketAddr, TcpListener};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

// Enhanced capabilities module is now properly defined in src/enhanced_capabilities.rs

pub async fn run() {
    let config = config::get_config();
    init_logging_main(&config);
    let args: Vec<String> = std::env::args().collect();
    if args.len() > 1 && args[1] == "agent" {
        run_agent().await;
    } else {
        run_server(config).await;
    }
}

fn init_logging_main(config: &config::Config) {
    let log_level = match config.logging.level.as_str() {
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };
    let console_layer = tracing_subscriber::fmt::layer()
        .with_target(true)
        .with_level(true)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .with_ansi(true)
        .compact();
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("{}={}", env!("CARGO_PKG_NAME"), log_level).into()),
        )
        .with(console_layer)
        .init();
    print_startup_banner();
    info!("Logging initialized with level: {}", config.logging.level);
}

fn print_startup_banner() {
    println!("\n{}", "=".repeat(60));
    println!("    🚀 Ikunc2 C2 Server - Real-time Debug Mode");
    println!("    📅 Started at: {}", chrono::Local::now().format("%Y-%m-%d %H:%M:%S"));
    println!("{}", "=".repeat(60));
    println!();
}

fn print_enhanced_server_status(config: &config::Config, original_http: u16, original_tcp: u16) {
    println!("{}", "=".repeat(70));
    println!("    📊 IKUNC2 C2 SERVER - ENHANCED STATUS");
    println!("{}", "=".repeat(70));
    println!("🎯 Ikunc2 C2 Server is running!");
    println!();
    if config.server.http_addr.port() != original_http {
        println!("🌐 Web GUI: http://{} ⚠️  (自动从端口 {} 切换)", 
            config.server.http_addr, original_http);
    } else {
        println!("🌐 Web GUI: http://{} ✅", config.server.http_addr);
    }
    println!("   Protocol: HTTP (plaintext)");
    if config.server.tcp_addr.port() != original_tcp {
        println!("🔌 TCP Listener: {} ⚠️  (自动从端口 {} 切换)", 
            config.server.tcp_addr, original_tcp);
    } else {
        println!("🔌 TCP Listener: {} ✅", config.server.tcp_addr);
    }
    println!();
    println!("📊 Storage System:");
    println!("  📁 Data Directory: data");
    println!("  🗄️  Users Database: data/users.db");
    println!("  📄 Agents JSON: data/agents.json");
    println!("  📄 Commands JSON: data/command_history.json");
    println!();
    println!("🔧 Agent Communication:");
    println!("  📡 Supported Protocols: TCP, HTTP, HTTPS");
    println!("  🛡️  TLS Encryption: Disabled");
    println!("🔐 Default Login: admin/admin");
    println!();
    println!("⚙️  Port Management:");
    println!("  🔍 Automatic Port Discovery: Enabled");
    println!("  🔄 Port Conflict Resolution: Active");
    println!("  📈 Error Recovery: Enabled");
    println!("{}", "=".repeat(70));
    println!("           🔴 REAL-TIME LOGS");
    println!("{}", "=".repeat(70));
    println!();
}

fn start_status_monitor() {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            debug!("💓 Server heartbeat - System running normally");
            if chrono::Local::now().minute() % 5 == 0 {
                info!("📊 System Status Check:");
                info!("  🟢 Web Server: Active");
                info!("  🟢 TCP Listener: Active");
                info!("  🟢 Storage System: Operational");
                info!("  📈 Uptime: {}", format_uptime());
            }
        }
    });
}

fn format_uptime() -> String {
    static START_TIME: std::sync::OnceLock<std::time::Instant> = std::sync::OnceLock::new();
    let start = START_TIME.get_or_init(|| std::time::Instant::now());
    let elapsed = start.elapsed();
    let hours = elapsed.as_secs() / 3600;
    let minutes = (elapsed.as_secs() % 3600) / 60;
    let seconds = elapsed.as_secs() % 60;
    format!("{}h {}m {}s", hours, minutes, seconds)
}

fn is_port_available(addr: SocketAddr) -> bool {
    TcpListener::bind(addr).is_ok()
}

fn find_available_port(base_addr: SocketAddr, start_port: u16, max_attempts: u16) -> Option<SocketAddr> {
    let host = base_addr.ip();
    for port in start_port..start_port + max_attempts {
        let test_addr = SocketAddr::new(host, port);
        if is_port_available(test_addr) {
            return Some(test_addr);
        }
    }
    None
}

fn resolve_port_conflicts(mut http_addr: SocketAddr, mut tcp_addr: SocketAddr) -> std::result::Result<(SocketAddr, SocketAddr), String> {
    if !is_port_available(http_addr) {
        warn!("⚠️  HTTP端口 {} 被占用，正在寻找可用端口...", http_addr.port());
        match find_available_port(http_addr, http_addr.port() + 1, 100) {
            Some(new_addr) => {
                info!("✅ 找到可用HTTP端口: {}", new_addr.port());
                http_addr = new_addr;
            }
            None => {
                return Err(format!("无法找到可用的HTTP端口 (尝试了端口 {} 到 {})", 
                    http_addr.port() + 1, http_addr.port() + 100));
            }
        }
    }
    while !is_port_available(tcp_addr) || tcp_addr.port() == http_addr.port() {
        if tcp_addr.port() == http_addr.port() {
            warn!("⚠️  TCP端口 {} 与HTTP端口冲突，正在寻找替代端口...", tcp_addr.port());
        } else {
            warn!("⚠️  TCP端口 {} 被占用，正在寻找可用端口...", tcp_addr.port());
        }
        match find_available_port(tcp_addr, tcp_addr.port() + 1, 100) {
            Some(new_addr) => {
                if new_addr.port() != http_addr.port() {
                    info!("✅ 找到可用TCP端口: {}", new_addr.port());
                    tcp_addr = new_addr;
                    break;
                }
                tcp_addr = SocketAddr::new(tcp_addr.ip(), new_addr.port() + 1);
            }
            None => {
                return Err(format!("无法找到可用的TCP端口 (尝试了端口 {} 到 {})", 
                    tcp_addr.port() + 1, tcp_addr.port() + 100));
            }
        }
    }
    Ok((http_addr, tcp_addr))
}

async fn run_server(config: config::Config) {
    let original_http_addr = config.server.http_addr;
    let original_tcp_addr = config.server.tcp_addr;
    info!("🚀 启动 Ikunc2 C2 Server - 混合存储版");
    info!("📋 配置加载成功");
    let (http_addr, tcp_addr) = match resolve_port_conflicts(original_http_addr, original_tcp_addr) {
        Ok((http, tcp)) => {
            if http != original_http_addr {
                warn!("📍 HTTP端口已从 {} 更改为 {}", original_http_addr.port(), http.port());
            }
            if tcp != original_tcp_addr {
                warn!("📍 TCP端口已从 {} 更改为 {}", original_tcp_addr.port(), tcp.port());
            }
            (http, tcp)
        }
        Err(e) => {
            error!("❌ 端口解析失败: {}", e);
            error!("💡 请检查端口占用情况或重启相关服务");
            std::process::exit(1);
        }
    };
    if let Err(e) = tokio::fs::create_dir_all("data").await {
        error!("❌ Failed to create data directory: {}", e);
        std::process::exit(1);
    }
    debug::log_info("Starting C2 server initialization", "Main");
    let storage = match hybrid_storage::HybridStorage::new().await {
        Ok(storage) => {
            debug::log_success("Hybrid storage initialized successfully", "Storage");
            info!("✅ 混合存储初始化成功");
            Arc::new(storage)
        }
        Err(e) => {
            debug::log_warning(&format!("Failed to initialize hybrid storage: {}", e), "Storage");
            error!("❌ Failed to initialize hybrid storage: {}", e);
            std::process::exit(1);
        }
    };
    let mut updated_config = config.clone();
    updated_config.server.http_addr = http_addr;
    updated_config.server.tcp_addr = tcp_addr;
    let state = server::AppState::with_storage(updated_config.clone(), storage);
    print_enhanced_server_status(&updated_config, original_http_addr.port(), original_tcp_addr.port());
    debug::log_success("C2 server startup completed", "Main");
    debug::log_info(&format!("Web server listening on {}", http_addr), "WebServer");
    debug::log_info(&format!("TCP listener listening on {}", tcp_addr), "TCPListener");
    start_status_monitor();
    let stats_state = state.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        loop {
            interval.tick().await;
            let client_count = stats_state.clients.read().await.len();
            if client_count > 0 {
                info!("📊 Active Agents: {}", client_count);
            } else {
                debug!("📊 No active agents connected");
            }
        }
    });
    let cors = CorsLayer::new()
        .allow_origin([
            HeaderValue::from_str(&format!("http://localhost:{}", http_addr.port())).unwrap_or_else(|_| HeaderValue::from_static("http://localhost:8080")),
            HeaderValue::from_str(&format!("http://127.0.0.1:{}", http_addr.port())).unwrap_or_else(|_| HeaderValue::from_static("http://127.0.0.1:8080"))
        ])
        .allow_methods([Method::GET, Method::POST, Method::OPTIONS])
        .allow_headers([AUTHORIZATION, ACCEPT, CONTENT_TYPE])
        .allow_credentials(true);
    let app = web::create_routes()
        .layer(cors)
        .with_state(state.clone());
    let tcp_state = state.clone();
    let http_handle = tokio::spawn(async move {
        info!("🌐 Web 服务器启动于 {} (HTTP)", http_addr);
        match tokio::net::TcpListener::bind(http_addr).await {
            Ok(listener) => {
                match axum::serve(listener, app).await {
                    Ok(_) => info!("✅ Web server stopped gracefully"),
                    Err(e) => error!("❌ HTTP server error: {}", e),
                }
            }
            Err(e) => {
                error!("❌ Failed to bind HTTP address {}: {}", http_addr, e);
                error!("💡 Port {} may have been taken by another process", http_addr.port());
            }
        }
    });
    let tcp_handle = tokio::spawn(async move {
        info!("🔌 TCP监听器启动于 {}", tcp_addr);
        if let Err(e) = tcp_listener::start_tcp_listener(tcp_addr, tcp_state).await {
            error!("❌ TCP listener error: {}", e);
            warn!("🔌 TCP server exited - continuing with Web server only");
        }
    });
    let shutdown_signal = async {
        match tokio::signal::ctrl_c().await {
            Ok(()) => {
                info!("🛑 Received shutdown signal (Ctrl+C)");
            }
            Err(err) => {
                error!("❌ Unable to listen for shutdown signal: {}", err);
            }
        }
    };
    let timeout_shutdown = async {
        tokio::time::sleep(tokio::time::Duration::from_secs(u64::MAX)).await;
    };
    info!("📡 服务器已启动，按 Ctrl+C 停止服务器");
    info!("🌐 Web界面地址: http://{}", http_addr);
    info!("🔌 TCP监听地址: {}", tcp_addr);
    println!("\n{}", "⭐".repeat(70));
    println!("🎉 IKUNC2 C2 SERVER SUCCESSFULLY STARTED!");
    println!("{}", "⭐".repeat(70));
    println!();
    println!("📝 QUICK START GUIDE:");
    println!("   1️⃣  Open your browser and visit: http://{}", http_addr);
    println!("   2️⃣  Login with credentials: admin/admin");
    println!("   3️⃣  Build agents to connect to: {}", tcp_addr);
    println!("   4️⃣  Press Ctrl+C anytime to stop the server gracefully");
    println!();
    println!("🔧 TROUBLESHOOTING:");
    println!("   🔹 If Web GUI is unreachable, check firewall settings");
    println!("   🔹 If agents can't connect, verify TCP port {} is open", tcp_addr.port());
    println!("   🔹 Port conflicts are automatically resolved");
    println!("   🔹 Check logs below for real-time server status");
    println!();
    println!("🛡️  SECURITY NOTES:");
    println!("   ⚠️  Change default credentials in production");
    println!("   ⚠️  Enable TLS for secure communications");
    println!("   ⚠️  Restrict network access as needed");
    println!("{}", "⭐".repeat(70));
    println!();
    tokio::select! {
        _ = http_handle => {
            warn!("🌐 HTTP server exited");
        }
        _ = tcp_handle => {
            warn!("🔌 TCP server exited");
        }
        _ = shutdown_signal => {
            info!("🔄 Starting graceful shutdown...");
        }
        _ = timeout_shutdown => {
            info!("⏰ Timeout reached, shutting down...");
        }
    }
    info!("🔄 Shutting down servers gracefully...");
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
    info!("✅ Server shutdown completed");
    println!("\n{}", "=".repeat(60));
    println!("    🔴 Ikunc2 C2 Server Stopped");
    println!("    📅 Stopped at: {}", chrono::Local::now().format("%Y-%m-%d %H:%M:%S"));
    println!("    ⏱️  Total uptime: {}", format_uptime());
    println!("{}", "=".repeat(60));
}

async fn run_agent() {
    info!("🤖 Starting in agent mode");
    let server_ip = std::env::var("AGENT_IP").unwrap_or_else(|_| "127.0.0.1".to_string());
    let server_port = std::env::var("AGENT_PORT").unwrap_or_else(|_| "5555".to_string());
    info!("🔗 Connecting to C2 server: {}:{}", server_ip, server_port);
    let mut heartbeat_count = 0;
    loop {
        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        heartbeat_count += 1;
        debug!("🤖 Agent heartbeat #{}", heartbeat_count);
        if heartbeat_count % 6 == 0 {
            info!("🔄 Agent status: Connected to {}:{}", server_ip, server_port);
        }
    }
} 