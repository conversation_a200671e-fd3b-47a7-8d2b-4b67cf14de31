[package]
name = "ikunc2_agent"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
whoami = "1.0"
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.21"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true 