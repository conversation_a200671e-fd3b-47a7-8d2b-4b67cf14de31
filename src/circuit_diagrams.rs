// ===============================================================================
// Ikunc2 C2 Circuit Diagrams Module
// Network topology visualization and communication flow diagrams
// ===============================================================================

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Network node representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkNode {
    pub id: String,
    pub name: String,
    pub node_type: NodeType,
    pub ip_address: Option<String>,
    pub hostname: Option<String>,
    pub username: Option<String>,
    pub os_info: Option<String>,
    pub status: NodeStatus,
    pub last_seen: DateTime<Utc>,
    pub first_seen: DateTime<Utc>,
    pub metadata: HashMap<String, String>,
    pub position: Option<NodePosition>,
    pub beacon_info: Option<BeaconInfo>,
}

/// Node position for visual layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodePosition {
    pub x: f64,
    pub y: f64,
    pub z: Option<f64>,
}

/// Beacon information (similar to CS Beacon)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BeaconInfo {
    pub beacon_id: String,
    pub process_name: String,
    pub pid: Option<u32>,
    pub architecture: String,
    pub privileges: String,
    pub sleep_time: u64,
    pub jitter: u64,
    pub kill_date: Option<DateTime<Utc>>,
    pub working_hours: Option<String>,
    pub listener: String,
    pub external_ip: Option<String>,
    pub internal_ip: Option<String>,
    pub computer_name: String,
    pub user_name: String,
    pub os_version: String,
    pub session_id: Option<String>,
    pub note: Option<String>,
    pub tasks_count: u32,
    pub completed_tasks: u32,
    pub failed_tasks: u32,
}

/// Node types in the network
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NodeType {
    C2Server,
    Agent,
    Relay,
    Gateway,
    Target,
    Decoy,
    Beacon,
}

/// Node status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NodeStatus {
    Online,
    Offline,
    Compromised,
    Suspicious,
    Unknown,
}

/// Network connection between nodes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConnection {
    pub id: String,
    pub source_id: String,
    pub target_id: String,
    pub connection_type: ConnectionType,
    pub protocol: String,
    pub status: ConnectionStatus,
    pub bandwidth: Option<u64>,
    pub latency: Option<u64>,
    pub created_at: DateTime<Utc>,
}

/// Connection types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConnectionType {
    Direct,
    Relay,
    Proxy,
    Tunnel,
    Encrypted,
}

/// Connection status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionStatus {
    Active,
    Inactive,
    Blocked,
    Monitoring,
    Suspicious,
}

/// Communication flow representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationFlow {
    pub id: String,
    pub name: String,
    pub source_node: String,
    pub target_node: String,
    pub flow_type: FlowType,
    pub data_type: DataType,
    pub encryption: bool,
    pub timestamp: DateTime<Utc>,
    pub metadata: HashMap<String, String>,
}

/// Flow types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FlowType {
    Command,
    Response,
    Data,
    Heartbeat,
    Authentication,
    FileTransfer,
}

/// Data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataType {
    Text,
    Binary,
    Encrypted,
    Compressed,
    Steganographic,
}

/// Network topology
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkTopology {
    pub id: String,
    pub name: String,
    pub description: String,
    pub nodes: Vec<NetworkNode>,
    pub connections: Vec<NetworkConnection>,
    pub flows: Vec<CommunicationFlow>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Circuit diagram manager
pub struct CircuitDiagramManager {
    topologies: HashMap<String, NetworkTopology>,
    node_counter: u32,
    connection_counter: u32,
    flow_counter: u32,
}

impl CircuitDiagramManager {
    pub fn new() -> Self {
        Self {
            topologies: HashMap::new(),
            node_counter: 0,
            connection_counter: 0,
            flow_counter: 0,
        }
    }

    /// Create a new network topology
    pub fn create_topology(&mut self, name: &str, description: &str) -> String {
        let topology_id = format!("topology_{}", uuid::Uuid::new_v4());
        let topology = NetworkTopology {
            id: topology_id.clone(),
            name: name.to_string(),
            description: description.to_string(),
            nodes: Vec::new(),
            connections: Vec::new(),
            flows: Vec::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        self.topologies.insert(topology_id.clone(), topology);
        topology_id
    }

    /// Add a node to a topology
    pub fn add_node(&mut self, topology_id: &str, node: NetworkNode) -> Result<(), String> {
        if let Some(topology) = self.topologies.get_mut(topology_id) {
            topology.nodes.push(node);
            topology.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Add a connection between nodes
    pub fn add_connection(&mut self, topology_id: &str, connection: NetworkConnection) -> Result<(), String> {
        if let Some(topology) = self.topologies.get_mut(topology_id) {
            topology.connections.push(connection);
            topology.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Add a communication flow
    pub fn add_flow(&mut self, topology_id: &str, flow: CommunicationFlow) -> Result<(), String> {
        if let Some(topology) = self.topologies.get_mut(topology_id) {
            topology.flows.push(flow);
                topology.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Get topology by ID
    pub fn get_topology(&self, topology_id: &str) -> Option<&NetworkTopology> {
        self.topologies.get(topology_id)
    }

    /// Get all topologies
    pub fn get_all_topologies(&self) -> Vec<&NetworkTopology> {
        self.topologies.values().collect()
    }

    /// Update node status
    pub fn update_node_status(&mut self, topology_id: &str, node_id: &str, status: NodeStatus) -> Result<(), String> {
        if let Some(topology) = self.topologies.get_mut(topology_id) {
            if let Some(node) = topology.nodes.iter_mut().find(|n| n.id == node_id) {
                node.status = status;
                node.last_seen = Utc::now();
                topology.updated_at = Utc::now();
                Ok(())
            } else {
                Err("Node not found".to_string())
            }
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Generate DOT format for Graphviz
    pub fn generate_dot(&self, topology_id: &str) -> Result<String, String> {
        if let Some(topology) = self.get_topology(topology_id) {
            let mut dot = format!("digraph {} {{\n", topology.name.replace(" ", "_"));
            dot.push_str("  rankdir=TB;\n");
            dot.push_str("  node [shape=box, style=filled];\n\n");

            // Add nodes
            for node in &topology.nodes {
                let color = match node.node_type {
                    NodeType::C2Server => "lightblue",
                    NodeType::Agent => "lightgreen",
                    NodeType::Relay => "lightyellow",
                    NodeType::Gateway => "lightcoral",
                    NodeType::Target => "lightgray",
                    NodeType::Decoy => "lightpink",
                    NodeType::Beacon => "lightcyan",
                };
                
                let status_color = match node.status {
                    NodeStatus::Online => "green",
                    NodeStatus::Offline => "red",
                    NodeStatus::Compromised => "orange",
                    NodeStatus::Suspicious => "yellow",
                    NodeStatus::Unknown => "gray",
                };

                dot.push_str(&format!("  \"{}\" [label=\"{}\", fillcolor=\"{}\", color=\"{}\"];\n", 
                    node.id, node.name, color, status_color));
            }

            dot.push_str("\n");

            // Add connections
            for connection in &topology.connections {
                let style = match connection.connection_type {
                    ConnectionType::Direct => "solid",
                    ConnectionType::Relay => "dashed",
                    ConnectionType::Proxy => "dotted",
                    ConnectionType::Tunnel => "bold",
                    ConnectionType::Encrypted => "bold",
                };

                let color = match connection.status {
                    ConnectionStatus::Active => "green",
                    ConnectionStatus::Inactive => "gray",
                    ConnectionStatus::Blocked => "red",
                    ConnectionStatus::Monitoring => "blue",
                    ConnectionStatus::Suspicious => "orange",
                };

                dot.push_str(&format!("  \"{}\" -> \"{}\" [style={}, color={}, label=\"{}\"];\n",
                    connection.source_id, connection.target_id, style, color, connection.protocol));
            }

            dot.push_str("}\n");
            Ok(dot)
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Generate JSON representation
    pub fn generate_json(&self, topology_id: &str) -> Result<String, String> {
        if let Some(topology) = self.get_topology(topology_id) {
            serde_json::to_string_pretty(topology)
                .map_err(|e| format!("JSON serialization error: {}", e))
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Generate SVG representation
    pub fn generate_svg(&self, topology_id: &str) -> Result<String, String> {
        // This would require a Graphviz library or external tool
        // For now, return a placeholder
        Ok(format!("<svg>Topology {} SVG representation</svg>", topology_id))
    }

    /// Create a simple C2 topology
    pub fn create_c2_topology(&mut self, server_ip: &str, agents: Vec<String>) -> String {
        let topology_id = self.create_topology("C2 Network", "Command and Control Network Topology");

        // Add C2 server node
        let server_node = NetworkNode {
            id: "c2_server".to_string(),
            name: "C2 Server".to_string(),
            node_type: NodeType::C2Server,
            ip_address: Some(server_ip.to_string()),
            hostname: Some("c2-server".to_string()),
            username: Some("admin".to_string()),
            os_info: Some("Linux".to_string()),
            status: NodeStatus::Online,
            last_seen: Utc::now(),
            first_seen: Utc::now(),
            metadata: HashMap::new(),
            position: Some(NodePosition { x: 0.0, y: 0.0, z: None }),
            beacon_info: None,
        };

        self.add_node(&topology_id, server_node).unwrap();

        // Add agent nodes
        for (i, agent_ip) in agents.iter().enumerate() {
            let agent_node = NetworkNode {
                id: format!("agent_{}", i),
                name: format!("Agent {}", i + 1),
                node_type: NodeType::Agent,
                ip_address: Some(agent_ip.clone()),
                hostname: Some(format!("host-{}", i + 1)),
                username: Some(format!("user{}", i + 1)),
                os_info: Some("Windows 10".to_string()),
                status: NodeStatus::Online,
                last_seen: Utc::now(),
                first_seen: Utc::now(),
                metadata: HashMap::new(),
                position: Some(NodePosition { x: (i as f64 * 200.0) % 800.0, y: 200.0, z: None }),
                beacon_info: None,
            };

            self.add_node(&topology_id, agent_node).unwrap();

            // Add connection from server to agent
            let connection = NetworkConnection {
                id: format!("conn_{}", i),
                source_id: "c2_server".to_string(),
                target_id: format!("agent_{}", i),
                connection_type: ConnectionType::Encrypted,
                protocol: "HTTPS".to_string(),
                status: ConnectionStatus::Active,
                bandwidth: Some(1000000), // 1 Mbps
                latency: Some(50), // 50ms
                created_at: Utc::now(),
            };

            self.add_connection(&topology_id, connection).unwrap();
        }

        topology_id
    }

    /// Add a beacon node (similar to CS Beacon)
    pub fn add_beacon_node(&mut self, topology_id: &str, beacon_info: BeaconInfo) -> Result<String, String> {
        let node_id = format!("beacon_{}", beacon_info.beacon_id);
        
        let node = NetworkNode {
            id: node_id.clone(),
            name: format!("Beacon {}", beacon_info.beacon_id),
            node_type: NodeType::Beacon,
            ip_address: beacon_info.external_ip.clone(),
            hostname: Some(beacon_info.computer_name.clone()),
            username: Some(beacon_info.user_name.clone()),
            os_info: Some(beacon_info.os_version.clone()),
            status: NodeStatus::Online,
            last_seen: Utc::now(),
            first_seen: Utc::now(),
            metadata: HashMap::new(),
            position: None, // Will be calculated by layout algorithm
            beacon_info: Some(beacon_info),
        };
        
        self.add_node(topology_id, node)?;
        Ok(node_id)
    }

    /// Update beacon information
    pub fn update_beacon_info(&mut self, topology_id: &str, beacon_id: &str, beacon_info: BeaconInfo) -> Result<(), String> {
        if let Some(topology) = self.topologies.get_mut(topology_id) {
            if let Some(node) = topology.nodes.iter_mut().find(|n| n.id == format!("beacon_{}", beacon_id)) {
                node.beacon_info = Some(beacon_info);
                node.last_seen = Utc::now();
                topology.updated_at = Utc::now();
                Ok(())
            } else {
                Err("Beacon node not found".to_string())
            }
        } else {
            Err("Topology not found".to_string())
        }
    }

    /// Get beacon statistics
    pub fn get_beacon_statistics(&self, topology_id: &str) -> BeaconStatistics {
        if let Some(topology) = self.get_topology(topology_id) {
            let mut stats = BeaconStatistics {
                total_beacons: 0,
                online_beacons: 0,
                total_tasks: 0,
                completed_tasks: 0,
                failed_tasks: 0,
                average_sleep_time: 0,
                os_distribution: HashMap::new(),
                privilege_distribution: HashMap::new(),
            };

            for node in &topology.nodes {
                if node.node_type == NodeType::Beacon {
                    stats.total_beacons += 1;
                    
                    if node.status == NodeStatus::Online {
                        stats.online_beacons += 1;
                    }

                    if let Some(beacon) = &node.beacon_info {
                        stats.total_tasks += beacon.tasks_count;
                        stats.completed_tasks += beacon.completed_tasks;
                        stats.failed_tasks += beacon.failed_tasks;
                        stats.average_sleep_time += beacon.sleep_time;

                        // OS distribution
                        let os_count = stats.os_distribution.entry(beacon.os_version.clone()).or_insert(0);
                        *os_count += 1;

                        // Privilege distribution
                        let priv_count = stats.privilege_distribution.entry(beacon.privileges.clone()).or_insert(0);
                        *priv_count += 1;
                }
            }
        }

            if stats.total_beacons > 0 {
                stats.average_sleep_time /= stats.total_beacons as u64;
            }

            stats
        } else {
            BeaconStatistics {
                total_beacons: 0,
                online_beacons: 0,
                total_tasks: 0,
                completed_tasks: 0,
                failed_tasks: 0,
                average_sleep_time: 0,
                os_distribution: HashMap::new(),
                privilege_distribution: HashMap::new(),
        }
    }
}

    /// Analyze network security
    pub fn analyze_security(&self, topology_id: &str) -> SecurityAnalysis {
        if let Some(topology) = self.get_topology(topology_id) {
            let mut analysis = SecurityAnalysis {
                total_nodes: topology.nodes.len(),
                online_nodes: 0,
                compromised_nodes: 0,
                suspicious_connections: 0,
                encrypted_connections: 0,
                vulnerabilities: Vec::new(),
                recommendations: Vec::new(),
            };

            for node in &topology.nodes {
                match node.status {
                    NodeStatus::Online => analysis.online_nodes += 1,
                    NodeStatus::Compromised => analysis.compromised_nodes += 1,
                    _ => {}
                }
            }

            for connection in &topology.connections {
                match connection.connection_type {
                    ConnectionType::Encrypted => analysis.encrypted_connections += 1,
                    _ => {}
                }

                if connection.status == ConnectionStatus::Suspicious {
                    analysis.suspicious_connections += 1;
                }
            }

            // Generate recommendations
            if analysis.compromised_nodes > 0 {
                analysis.recommendations.push("Isolate compromised nodes immediately".to_string());
                    }

            if analysis.encrypted_connections < topology.connections.len() {
                analysis.recommendations.push("Enable encryption for all connections".to_string());
            }

            analysis
        } else {
            SecurityAnalysis {
                total_nodes: 0,
                online_nodes: 0,
                compromised_nodes: 0,
                suspicious_connections: 0,
                encrypted_connections: 0,
                vulnerabilities: Vec::new(),
                recommendations: Vec::new(),
            }
        }
    }
}

/// Security analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    pub total_nodes: usize,
    pub online_nodes: usize,
    pub compromised_nodes: usize,
    pub suspicious_connections: usize,
    pub encrypted_connections: usize,
    pub vulnerabilities: Vec<String>,
    pub recommendations: Vec<String>,
}

/// Beacon statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BeaconStatistics {
    pub total_beacons: usize,
    pub online_beacons: usize,
    pub total_tasks: u32,
    pub completed_tasks: u32,
    pub failed_tasks: u32,
    pub average_sleep_time: u64,
    pub os_distribution: HashMap<String, usize>,
    pub privilege_distribution: HashMap<String, usize>,
}

/// Builder pattern for creating nodes
pub struct NetworkNodeBuilder {
    id: Option<String>,
    name: Option<String>,
    node_type: Option<NodeType>,
    ip_address: Option<String>,
    hostname: Option<String>,
    username: Option<String>,
    os_info: Option<String>,
    status: Option<NodeStatus>,
    metadata: HashMap<String, String>,
    position: Option<NodePosition>,
    beacon_info: Option<BeaconInfo>,
}

impl NetworkNodeBuilder {
    pub fn new() -> Self {
        Self {
            id: None,
            name: None,
            node_type: None,
            ip_address: None,
            hostname: None,
            username: None,
            os_info: None,
            status: Some(NodeStatus::Unknown),
            metadata: HashMap::new(),
            position: None,
            beacon_info: None,
        }
    }

    pub fn id(mut self, id: String) -> Self {
        self.id = Some(id);
        self
    }

    pub fn name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    pub fn node_type(mut self, node_type: NodeType) -> Self {
        self.node_type = Some(node_type);
        self
    }

    pub fn ip_address(mut self, ip_address: String) -> Self {
        self.ip_address = Some(ip_address);
        self
    }

    pub fn hostname(mut self, hostname: String) -> Self {
        self.hostname = Some(hostname);
        self
    }

    pub fn username(mut self, username: String) -> Self {
        self.username = Some(username);
        self
    }

    pub fn os_info(mut self, os_info: String) -> Self {
        self.os_info = Some(os_info);
        self
    }

    pub fn status(mut self, status: NodeStatus) -> Self {
        self.status = Some(status);
        self
    }

    pub fn position(mut self, x: f64, y: f64) -> Self {
        self.position = Some(NodePosition { x, y, z: None });
        self
    }

    pub fn beacon_info(mut self, beacon_info: BeaconInfo) -> Self {
        self.beacon_info = Some(beacon_info);
        self
    }

    pub fn metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    pub fn build(self) -> Result<NetworkNode, String> {
        Ok(NetworkNode {
            id: self.id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string()),
            name: self.name.ok_or("Name is required")?,
            node_type: self.node_type.ok_or("Node type is required")?,
            ip_address: self.ip_address,
            hostname: self.hostname,
            username: self.username,
            os_info: self.os_info,
            status: self.status.unwrap_or(NodeStatus::Unknown),
            last_seen: Utc::now(),
            first_seen: Utc::now(),
            metadata: self.metadata,
            position: self.position,
            beacon_info: self.beacon_info,
        })
    }
}

/// Builder pattern for creating connections
pub struct NetworkConnectionBuilder {
    id: Option<String>,
    source_id: Option<String>,
    target_id: Option<String>,
    connection_type: Option<ConnectionType>,
    protocol: Option<String>,
    status: Option<ConnectionStatus>,
    bandwidth: Option<u64>,
    latency: Option<u64>,
}

impl NetworkConnectionBuilder {
    pub fn new() -> Self {
        Self {
            id: None,
            source_id: None,
            target_id: None,
            connection_type: Some(ConnectionType::Direct),
            protocol: Some("TCP".to_string()),
            status: Some(ConnectionStatus::Active),
            bandwidth: None,
            latency: None,
        }
    }

    pub fn id(mut self, id: String) -> Self {
        self.id = Some(id);
        self
    }

    pub fn source(mut self, source_id: String) -> Self {
        self.source_id = Some(source_id);
        self
    }

    pub fn target(mut self, target_id: String) -> Self {
        self.target_id = Some(target_id);
        self
    }

    pub fn connection_type(mut self, connection_type: ConnectionType) -> Self {
        self.connection_type = Some(connection_type);
        self
    }

    pub fn protocol(mut self, protocol: String) -> Self {
        self.protocol = Some(protocol);
        self
    }

    pub fn status(mut self, status: ConnectionStatus) -> Self {
        self.status = Some(status);
        self
    }

    pub fn bandwidth(mut self, bandwidth: u64) -> Self {
        self.bandwidth = Some(bandwidth);
        self
    }

    pub fn latency(mut self, latency: u64) -> Self {
        self.latency = Some(latency);
        self
    }

    pub fn build(self) -> Result<NetworkConnection, String> {
        Ok(NetworkConnection {
            id: self.id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string()),
            source_id: self.source_id.ok_or("Source ID is required")?,
            target_id: self.target_id.ok_or("Target ID is required")?,
            connection_type: self.connection_type.unwrap_or(ConnectionType::Direct),
            protocol: self.protocol.unwrap_or_else(|| "TCP".to_string()),
            status: self.status.unwrap_or(ConnectionStatus::Active),
            bandwidth: self.bandwidth,
            latency: self.latency,
            created_at: Utc::now(),
        })
    }
} 