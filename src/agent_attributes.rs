// ===============================================================================
// Ikunc2 C2 Agent Attributes Module
// Agent capabilities, attributes, and evasion techniques management
// ===============================================================================

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Agent privilege levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PrivilegeLevel {
    User,
    Administrator,
    System,
    TrustedInstaller,
    Unknown,
}

/// Agent architecture
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Architecture {
    X86,
    X64,
    ARM,
    ARM64,
    Unknown,
}

/// Operating system types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OperatingSystem {
    Windows,
    Linux,
    MacOS,
    Android,
    IOS,
    Unknown,
}

/// Agent status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AgentStatus {
    Active,
    Sleep,
    Dead,
    Compromised,
    Quarantined,
    Unknown,
}

/// Evasion techniques
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EvasionTechnique {
    AntiDebug,
    AntiVM,
    AntiSandbox,
    ProcessInjection,
    MemoryObfuscation,
    StringEncryption,
    ImportObfuscation,
    CodeVirtualization,
    PolymorphicCode,
    RootkitTechniques,
    NetworkEvasion,
    FileSystemEvasion,
    RegistryEvasion,
    ServiceEvasion,
    DriverEvasion,
    Custom(String),
}

/// Agent capabilities
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AgentCapability {
    // Basic capabilities
    ShellExecution,
    FileOperations,
    ProcessManagement,
    NetworkScanning,
    RegistryAccess,
    ServiceControl,
    
    // Advanced capabilities
    MemoryDump,
    Keylogging,
    Screenshot,
    WebcamCapture,
    AudioRecording,
    ClipboardMonitoring,
    
    // Evasion capabilities
    ProcessInjection,
    DLLInjection,
    ThreadHijacking,
    CodeCaveInjection,
    APCInjection,
    
    // Persistence capabilities
    RegistryPersistence,
    ServicePersistence,
    TaskSchedulerPersistence,
    StartupFolderPersistence,
    WMIEventPersistence,
    
    // Network capabilities
    PortForwarding,
    SOCKSProxy,
    HTTPTunnel,
    DNSTunnel,
    ICMPTunnel,
    
    // Custom capabilities
    Custom(String),
}

/// Agent attributes structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentAttributes {
    pub agent_id: String,
    pub name: String,
    pub version: String,
    pub architecture: Architecture,
    pub operating_system: OperatingSystem,
    pub privilege_level: PrivilegeLevel,
    pub status: AgentStatus,
    pub capabilities: Vec<AgentCapability>,
    pub evasion_techniques: Vec<EvasionTechnique>,
    pub custom_attributes: HashMap<String, String>,
    pub created_at: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
    pub health_score: u8, // 0-100
    pub stealth_score: u8, // 0-100
    pub performance_score: u8, // 0-100
}

/// Evasion technique configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EvasionConfig {
    pub technique: EvasionTechnique,
    pub enabled: bool,
    pub parameters: HashMap<String, String>,
    pub effectiveness: u8, // 0-100
    pub detection_risk: u8, // 0-100
}

/// Agent performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub network_bandwidth: u64,
    pub response_time: u64,
    pub success_rate: f64,
    pub failure_count: u32,
    pub last_heartbeat: DateTime<Utc>,
}

/// Agent security profile
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityProfile {
    pub antivirus_detection: bool,
    pub sandbox_detection: bool,
    pub debugger_detection: bool,
    pub network_detection: bool,
    pub behavioral_detection: bool,
    pub signature_detection: bool,
    pub heuristic_detection: bool,
    pub last_scan_result: Option<String>,
    pub detection_count: u32,
    pub evasion_success_rate: f64,
}

/// Agent attributes manager
pub struct AgentAttributesManager {
    agents: HashMap<String, AgentAttributes>,
    evasion_configs: HashMap<String, Vec<EvasionConfig>>,
    performance_metrics: HashMap<String, PerformanceMetrics>,
    security_profiles: HashMap<String, SecurityProfile>,
}

impl AgentAttributesManager {
    pub fn new() -> Self {
        Self {
            agents: HashMap::new(),
            evasion_configs: HashMap::new(),
            performance_metrics: HashMap::new(),
            security_profiles: HashMap::new(),
        }
    }

    /// Create a new agent with attributes
    pub fn create_agent(&mut self, agent_id: String, name: String, os: OperatingSystem, arch: Architecture) -> Result<(), String> {
        let attributes = AgentAttributes {
            agent_id: agent_id.clone(),
            name,
            version: "1.0.0".to_string(),
            architecture: arch,
            operating_system: os,
            privilege_level: PrivilegeLevel::Unknown,
            status: AgentStatus::Unknown,
            capabilities: vec![
                AgentCapability::ShellExecution,
                AgentCapability::FileOperations,
                AgentCapability::ProcessManagement,
            ],
            evasion_techniques: vec![],
            custom_attributes: HashMap::new(),
            created_at: Utc::now(),
            last_updated: Utc::now(),
            health_score: 100,
            stealth_score: 50,
            performance_score: 100,
        };

        self.agents.insert(agent_id.clone(), attributes);
        
        // Initialize default security profile
        let security_profile = SecurityProfile {
            antivirus_detection: false,
            sandbox_detection: false,
            debugger_detection: false,
            network_detection: false,
            behavioral_detection: false,
            signature_detection: false,
            heuristic_detection: false,
            last_scan_result: None,
            detection_count: 0,
            evasion_success_rate: 1.0,
        };
        
        self.security_profiles.insert(agent_id.clone(), security_profile);
        
        // Initialize performance metrics
        let performance_metrics = PerformanceMetrics {
            cpu_usage: 0.0,
            memory_usage: 0.0,
            network_bandwidth: 0,
            response_time: 0,
            success_rate: 1.0,
            failure_count: 0,
            last_heartbeat: Utc::now(),
        };
        
        self.performance_metrics.insert(agent_id, performance_metrics);
        
        Ok(())
    }

    /// Get agent attributes
    pub fn get_agent_attributes(&self, agent_id: &str) -> Option<&AgentAttributes> {
        self.agents.get(agent_id)
    }

    /// Update agent attributes
    pub fn update_agent_attributes(&mut self, agent_id: &str, attributes: AgentAttributes) -> Result<(), String> {
        if self.agents.contains_key(agent_id) {
            self.agents.insert(agent_id.to_string(), attributes);
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Add capability to agent
    pub fn add_capability(&mut self, agent_id: &str, capability: AgentCapability) -> Result<(), String> {
        if let Some(agent) = self.agents.get_mut(agent_id) {
            if !agent.capabilities.contains(&capability) {
                agent.capabilities.push(capability);
                agent.last_updated = Utc::now();
            }
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Remove capability from agent
    pub fn remove_capability(&mut self, agent_id: &str, capability: &AgentCapability) -> Result<(), String> {
        if let Some(agent) = self.agents.get_mut(agent_id) {
            agent.capabilities.retain(|c| c != capability);
            agent.last_updated = Utc::now();
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Add evasion technique to agent
    pub fn add_evasion_technique(&mut self, agent_id: &str, technique: EvasionTechnique) -> Result<(), String> {
        if let Some(agent) = self.agents.get_mut(agent_id) {
            if !agent.evasion_techniques.contains(&technique) {
                agent.evasion_techniques.push(technique);
                agent.stealth_score = (agent.stealth_score + 10).min(100);
                agent.last_updated = Utc::now();
            }
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Configure evasion technique
    pub fn configure_evasion(&mut self, agent_id: &str, config: EvasionConfig) -> Result<(), String> {
        let configs = self.evasion_configs.entry(agent_id.to_string()).or_insert_with(Vec::new);
        
        // Update existing config or add new one
        if let Some(existing_config) = configs.iter_mut().find(|c| c.technique == config.technique) {
            *existing_config = config;
        } else {
            configs.push(config);
        }
        
        Ok(())
    }

    /// Get evasion configurations for agent
    pub fn get_evasion_configs(&self, agent_id: &str) -> Vec<EvasionConfig> {
        self.evasion_configs.get(agent_id).cloned().unwrap_or_default()
    }

    /// Update performance metrics
    pub fn update_performance_metrics(&mut self, agent_id: &str, metrics: PerformanceMetrics) -> Result<(), String> {
        if self.performance_metrics.contains_key(agent_id) {
            self.performance_metrics.insert(agent_id.to_string(), metrics);
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Get performance metrics
    pub fn get_performance_metrics(&self, agent_id: &str) -> Option<&PerformanceMetrics> {
        self.performance_metrics.get(agent_id)
    }

    /// Update security profile
    pub fn update_security_profile(&mut self, agent_id: &str, profile: SecurityProfile) -> Result<(), String> {
        if self.security_profiles.contains_key(agent_id) {
            self.security_profiles.insert(agent_id.to_string(), profile);
            Ok(())
        } else {
            Err("Agent not found".to_string())
        }
    }

    /// Get security profile
    pub fn get_security_profile(&self, agent_id: &str) -> Option<&SecurityProfile> {
        self.security_profiles.get(agent_id)
    }

    /// Calculate agent health score
    pub fn calculate_health_score(&self, agent_id: &str) -> u8 {
        if let Some(metrics) = self.performance_metrics.get(agent_id) {
            let mut score: u8 = 100;
            
            // Reduce score based on failures
            if metrics.failure_count > 10 {
                score = score.saturating_sub(20);
            }
            
            // Reduce score based on high resource usage
            if metrics.cpu_usage > 80.0 || metrics.memory_usage > 80.0 {
                score = score.saturating_sub(15);
            }
            
            // Reduce score based on poor success rate
            if metrics.success_rate < 0.8 {
                score = score.saturating_sub(25);
            }
            
            score
        } else {
            0
        }
    }

    /// Get all agents
    pub fn get_all_agents(&self) -> Vec<&AgentAttributes> {
        self.agents.values().collect()
    }

    /// Get agents by status
    pub fn get_agents_by_status(&self, status: AgentStatus) -> Vec<&AgentAttributes> {
        self.agents.values().filter(|a| a.status == status).collect()
    }

    /// Get agents by operating system
    pub fn get_agents_by_os(&self, os: OperatingSystem) -> Vec<&AgentAttributes> {
        self.agents.values().filter(|a| a.operating_system == os).collect()
    }

    /// Get agents with specific capability
    pub fn get_agents_with_capability(&self, capability: &AgentCapability) -> Vec<&AgentAttributes> {
        self.agents.values().filter(|a| a.capabilities.contains(capability)).collect()
    }

    /// Get agents with specific evasion technique
    pub fn get_agents_with_evasion(&self, technique: &EvasionTechnique) -> Vec<&AgentAttributes> {
        self.agents.values().filter(|a| a.evasion_techniques.contains(technique)).collect()
    }

    /// Generate agent report
    pub fn generate_agent_report(&self, agent_id: &str) -> Option<AgentReport> {
        if let (Some(attributes), Some(metrics), Some(profile)) = (
            self.agents.get(agent_id),
            self.performance_metrics.get(agent_id),
            self.security_profiles.get(agent_id)
        ) {
            Some(AgentReport {
                agent_id: agent_id.to_string(),
                attributes: attributes.clone(),
                performance: metrics.clone(),
                security: profile.clone(),
                evasion_configs: self.evasion_configs.get(agent_id).cloned().unwrap_or_default(),
                health_score: self.calculate_health_score(agent_id),
                generated_at: Utc::now(),
            })
        } else {
            None
        }
    }
}

/// Agent report structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentReport {
    pub agent_id: String,
    pub attributes: AgentAttributes,
    pub performance: PerformanceMetrics,
    pub security: SecurityProfile,
    pub evasion_configs: Vec<EvasionConfig>,
    pub health_score: u8,
    pub generated_at: DateTime<Utc>,
}

/// Builder pattern for creating agent attributes
pub struct AgentAttributesBuilder {
    agent_id: Option<String>,
    name: Option<String>,
    version: Option<String>,
    architecture: Option<Architecture>,
    operating_system: Option<OperatingSystem>,
    privilege_level: Option<PrivilegeLevel>,
    status: Option<AgentStatus>,
    capabilities: Vec<AgentCapability>,
    evasion_techniques: Vec<EvasionTechnique>,
    custom_attributes: HashMap<String, String>,
}

impl AgentAttributesBuilder {
    pub fn new() -> Self {
        Self {
            agent_id: None,
            name: None,
            version: Some("1.0.0".to_string()),
            architecture: None,
            operating_system: None,
            privilege_level: Some(PrivilegeLevel::Unknown),
            status: Some(AgentStatus::Unknown),
            capabilities: vec![],
            evasion_techniques: vec![],
            custom_attributes: HashMap::new(),
        }
    }

    pub fn agent_id(mut self, agent_id: String) -> Self {
        self.agent_id = Some(agent_id);
        self
    }

    pub fn name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    pub fn version(mut self, version: String) -> Self {
        self.version = Some(version);
        self
    }

    pub fn architecture(mut self, architecture: Architecture) -> Self {
        self.architecture = Some(architecture);
        self
    }

    pub fn operating_system(mut self, os: OperatingSystem) -> Self {
        self.operating_system = Some(os);
        self
    }

    pub fn privilege_level(mut self, privilege: PrivilegeLevel) -> Self {
        self.privilege_level = Some(privilege);
        self
    }

    pub fn status(mut self, status: AgentStatus) -> Self {
        self.status = Some(status);
        self
    }

    pub fn capability(mut self, capability: AgentCapability) -> Self {
        self.capabilities.push(capability);
        self
    }

    pub fn evasion_technique(mut self, technique: EvasionTechnique) -> Self {
        self.evasion_techniques.push(technique);
        self
    }

    pub fn custom_attribute(mut self, key: String, value: String) -> Self {
        self.custom_attributes.insert(key, value);
        self
    }

    pub fn build(self) -> Result<AgentAttributes, String> {
        Ok(AgentAttributes {
            agent_id: self.agent_id.ok_or("Agent ID is required")?,
            name: self.name.ok_or("Name is required")?,
            version: self.version.unwrap_or_else(|| "1.0.0".to_string()),
            architecture: self.architecture.ok_or("Architecture is required")?,
            operating_system: self.operating_system.ok_or("Operating system is required")?,
            privilege_level: self.privilege_level.unwrap_or(PrivilegeLevel::Unknown),
            status: self.status.unwrap_or(AgentStatus::Unknown),
            capabilities: self.capabilities,
            evasion_techniques: self.evasion_techniques,
            custom_attributes: self.custom_attributes,
            created_at: Utc::now(),
            last_updated: Utc::now(),
            health_score: 100,
            stealth_score: 50,
            performance_score: 100,
        })
    }
} 