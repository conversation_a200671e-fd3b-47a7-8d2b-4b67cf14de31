// ===============================================================================
// Ikunc2 C2 Debug Framework Module
// Comprehensive debugging, monitoring, and feedback system for C2 operations
// ===============================================================================

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::{RwLock, broadcast};
use tracing::{debug, error, info, warn, Level};
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use std::time::Instant;

/// Debug event types for comprehensive monitoring
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DebugEventType {
    AgentConnection,
    AgentDisconnection,
    CommandExecution,
    CommandCompletion,
    CommandFailure,
    NetworkEvent,
    SecurityEvent,
    PerformanceMetric,
    ErrorEvent,
    WarningEvent,
    InfoEvent,
    SystemEvent,
    CustomEvent(String),
}

/// Debug severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum DebugSeverity {
    Critical = 5,
    High = 4,
    Medium = 3,
    Low = 2,
    Info = 1,
}

/// Debug event structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugEvent {
    pub id: String,
    pub event_type: DebugEventType,
    pub severity: DebugSeverity,
    pub title: String,
    pub description: String,
    pub source: String, // Agent ID, Server, etc.
    pub timestamp: DateTime<Utc>,
    pub duration: Option<u64>, // milliseconds
    pub metadata: HashMap<String, String>,
    pub stack_trace: Option<String>,
    pub related_events: Vec<String>,
    pub resolved: bool,
    pub resolution_notes: Option<String>,
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: DateTime<Utc>,
    pub cpu_usage: f64,
    pub memory_usage: u64, // bytes
    pub network_in: u64,   // bytes per second
    pub network_out: u64,  // bytes per second
    pub active_connections: u32,
    pub command_queue_size: u32,
    pub response_time_avg: f64, // milliseconds
    pub response_time_p95: f64, // milliseconds
    pub error_rate: f64,        // percentage
    pub throughput: f64,        // operations per second
}

/// System health status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
    Down,
    Unknown,
}

/// Health check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheck {
    pub component: String,
    pub status: HealthStatus,
    pub message: String,
    pub timestamp: DateTime<Utc>,
    pub response_time: u64, // milliseconds
    pub details: HashMap<String, String>,
}

/// Debug configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugConfig {
    pub enabled: bool,
    pub log_level: String,
    pub max_events: usize,
    pub retention_hours: u64,
    pub auto_cleanup: bool,
    pub real_time_monitoring: bool,
    pub performance_monitoring: bool,
    pub security_monitoring: bool,
    pub alert_thresholds: AlertThresholds,
    pub export_format: Vec<String>, // json, csv, xml
}

/// Alert threshold configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    pub cpu_threshold: f64,        // percentage
    pub memory_threshold: u64,     // bytes
    pub response_time_threshold: f64, // milliseconds
    pub error_rate_threshold: f64, // percentage
    pub connection_threshold: u32,
    pub queue_size_threshold: u32,
}

/// Debug framework manager
pub struct DebugFramework {
    config: Arc<RwLock<DebugConfig>>,
    events: Arc<RwLock<VecDeque<DebugEvent>>>,
    metrics_history: Arc<RwLock<VecDeque<PerformanceMetrics>>>,
    health_checks: Arc<RwLock<HashMap<String, HealthCheck>>>,
    event_broadcaster: broadcast::Sender<DebugEvent>,
    metrics_broadcaster: broadcast::Sender<PerformanceMetrics>,
    active_trackers: Arc<RwLock<HashMap<String, ExecutionTracker>>>,
    alert_handlers: Vec<Box<dyn AlertHandler + Send + Sync>>,
}

/// Execution tracker for monitoring command/operation execution
#[derive(Debug, Clone)]
pub struct ExecutionTracker {
    pub id: String,
    pub operation: String,
    pub agent_id: Option<String>,
    pub start_time: Instant,
    pub start_timestamp: DateTime<Utc>,
    pub checkpoints: Vec<ExecutionCheckpoint>,
    pub metadata: HashMap<String, String>,
}

/// Execution checkpoint for detailed tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionCheckpoint {
    pub name: String,
    pub timestamp: DateTime<Utc>,
    pub elapsed_ms: u64,
    pub status: String,
    pub data: Option<String>,
}

/// Alert handler trait
#[async_trait::async_trait]
pub trait AlertHandler {
    async fn handle_alert(&self, event: &DebugEvent) -> Result<(), String>;
    fn get_handler_name(&self) -> String;
    fn get_severity_filter(&self) -> Vec<DebugSeverity>;
}

impl DebugFramework {
    pub fn new() -> Self {
        let (event_tx, _) = broadcast::channel(1000);
        let (metrics_tx, _) = broadcast::channel(100);
        
        let default_config = DebugConfig {
            enabled: true,
            log_level: "DEBUG".to_string(),
            max_events: 10000,
            retention_hours: 24,
            auto_cleanup: true,
            real_time_monitoring: true,
            performance_monitoring: true,
            security_monitoring: true,
            alert_thresholds: AlertThresholds {
                cpu_threshold: 80.0,
                memory_threshold: 1024 * 1024 * 1024, // 1GB
                response_time_threshold: 5000.0, // 5 seconds
                error_rate_threshold: 5.0, // 5%
                connection_threshold: 1000,
                queue_size_threshold: 100,
            },
            export_format: vec!["json".to_string()],
        };
        
        let mut framework = Self {
            config: Arc::new(RwLock::new(default_config)),
            events: Arc::new(RwLock::new(VecDeque::new())),
            metrics_history: Arc::new(RwLock::new(VecDeque::new())),
            health_checks: Arc::new(RwLock::new(HashMap::new())),
            event_broadcaster: event_tx,
            metrics_broadcaster: metrics_tx,
            active_trackers: Arc::new(RwLock::new(HashMap::new())),
            alert_handlers: Vec::new(),
        };
        
        // Register default alert handlers
        framework.register_default_handlers();
        framework
    }

    fn register_default_handlers(&mut self) {
        self.alert_handlers.push(Box::new(LogAlertHandler::new()));
        self.alert_handlers.push(Box::new(MetricsAlertHandler::new()));
    }

    /// Log a debug event
    pub async fn log_event(&self, event: DebugEvent) {
        let config = self.config.read().await;
        
        if !config.enabled {
            return;
        }
        
        // Add to events queue
        {
            let mut events = self.events.write().await;
            events.push_back(event.clone());
            
            // Maintain max events limit
            if events.len() > config.max_events {
                events.pop_front();
            }
        }
        
        // Broadcast to subscribers
        let _ = self.event_broadcaster.send(event.clone());
        
        // Check for alerts
        if event.severity >= DebugSeverity::High {
            self.handle_alert(event).await;
        }
        
        // Log to tracing system
        match event.severity {
            DebugSeverity::Critical => error!("{}: {}", event.title, event.description),
            DebugSeverity::High => warn!("{}: {}", event.title, event.description),
            DebugSeverity::Medium => info!("{}: {}", event.title, event.description),
            DebugSeverity::Low | DebugSeverity::Info => debug!("{}: {}", event.title, event.description),
        }
    }

    /// Start tracking an operation
    pub async fn start_tracking(&self, operation: String, agent_id: Option<String>) -> String {
        let tracker_id = Uuid::new_v4().to_string();
        
        let tracker = ExecutionTracker {
            id: tracker_id.clone(),
            operation: operation.clone(),
            agent_id: agent_id.clone(),
            start_time: Instant::now(),
            start_timestamp: Utc::now(),
            checkpoints: Vec::new(),
            metadata: HashMap::new(),
        };
        
        {
            let mut trackers = self.active_trackers.write().await;
            trackers.insert(tracker_id.clone(), tracker);
        }
        
        // Log start event
        self.log_event(DebugEvent {
            id: Uuid::new_v4().to_string(),
            event_type: DebugEventType::CommandExecution,
            severity: DebugSeverity::Info,
            title: "Operation Started".to_string(),
            description: format!("Started tracking operation: {}", operation),
            source: agent_id.unwrap_or_else(|| "server".to_string()),
            timestamp: Utc::now(),
            duration: None,
            metadata: HashMap::from([
                ("tracker_id".to_string(), tracker_id.clone()),
                ("operation".to_string(), operation),
            ]),
            stack_trace: None,
            related_events: Vec::new(),
            resolved: false,
            resolution_notes: None,
        }).await;
        
        tracker_id
    }

    /// Add checkpoint to tracker
    pub async fn add_checkpoint(&self, tracker_id: &str, name: String, status: String, data: Option<String>) {
        let mut trackers = self.active_trackers.write().await;
        
        if let Some(tracker) = trackers.get_mut(tracker_id) {
            let checkpoint = ExecutionCheckpoint {
                name: name.clone(),
                timestamp: Utc::now(),
                elapsed_ms: tracker.start_time.elapsed().as_millis() as u64,
                status: status.clone(),
                data,
            };
            
            tracker.checkpoints.push(checkpoint);
            
            // Log checkpoint event
            drop(trackers); // Release lock before async call
            
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: DebugEventType::InfoEvent,
                severity: DebugSeverity::Info,
                title: "Checkpoint Reached".to_string(),
                description: format!("Checkpoint '{}' reached with status: {}", name, status),
                source: tracker.agent_id.clone().unwrap_or_else(|| "server".to_string()),
                timestamp: Utc::now(),
                duration: None,
                metadata: HashMap::from([
                    ("tracker_id".to_string(), tracker_id.to_string()),
                    ("checkpoint_name".to_string(), name),
                    ("status".to_string(), status),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: false,
                resolution_notes: None,
            }).await;
        }
    }

    /// Complete tracking an operation
    pub async fn complete_tracking(&self, tracker_id: &str, success: bool, result: Option<String>) {
        let tracker = {
            let mut trackers = self.active_trackers.write().await;
            trackers.remove(tracker_id)
        };
        
        if let Some(tracker) = tracker {
            let duration = tracker.start_time.elapsed().as_millis() as u64;
            
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: if success { 
                    DebugEventType::CommandCompletion 
                } else { 
                    DebugEventType::CommandFailure 
                },
                severity: if success { DebugSeverity::Info } else { DebugSeverity::Medium },
                title: if success { "Operation Completed" } else { "Operation Failed" }.to_string(),
                description: format!(
                    "Operation '{}' {} in {}ms", 
                    tracker.operation,
                    if success { "completed successfully" } else { "failed" },
                    duration
                ),
                source: tracker.agent_id.unwrap_or_else(|| "server".to_string()),
                timestamp: Utc::now(),
                duration: Some(duration),
                metadata: HashMap::from([
                    ("tracker_id".to_string(), tracker_id.to_string()),
                    ("operation".to_string(), tracker.operation),
                    ("success".to_string(), success.to_string()),
                    ("checkpoints".to_string(), tracker.checkpoints.len().to_string()),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: success,
                resolution_notes: result,
            }).await;
        }
    }

    /// Record performance metrics
    pub async fn record_metrics(&self, metrics: PerformanceMetrics) {
        let config = self.config.read().await;
        
        if !config.performance_monitoring {
            return;
        }
        
        // Store metrics
        {
            let mut history = self.metrics_history.write().await;
            history.push_back(metrics.clone());
            
            // Keep only recent metrics (last 24 hours)
            let cutoff = Utc::now() - Duration::hours(24);
            while let Some(front) = history.front() {
                if front.timestamp < cutoff {
                    history.pop_front();
                } else {
                    break;
                }
            }
        }
        
        // Broadcast metrics
        let _ = self.metrics_broadcaster.send(metrics.clone());
        
        // Check thresholds and generate alerts
        self.check_metric_thresholds(&metrics).await;
    }

    /// Check metric thresholds and generate alerts
    async fn check_metric_thresholds(&self, metrics: &PerformanceMetrics) {
        let config = self.config.read().await;
        let thresholds = &config.alert_thresholds;
        
        // CPU threshold check
        if metrics.cpu_usage > thresholds.cpu_threshold {
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: DebugEventType::PerformanceMetric,
                severity: DebugSeverity::High,
                title: "High CPU Usage".to_string(),
                description: format!("CPU usage is {}%, exceeding threshold of {}%", 
                                   metrics.cpu_usage, thresholds.cpu_threshold),
                source: "server".to_string(),
                timestamp: Utc::now(),
                duration: None,
                metadata: HashMap::from([
                    ("metric_type".to_string(), "cpu_usage".to_string()),
                    ("current_value".to_string(), metrics.cpu_usage.to_string()),
                    ("threshold".to_string(), thresholds.cpu_threshold.to_string()),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: false,
                resolution_notes: None,
            }).await;
        }
        
        // Memory threshold check
        if metrics.memory_usage > thresholds.memory_threshold {
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: DebugEventType::PerformanceMetric,
                severity: DebugSeverity::High,
                title: "High Memory Usage".to_string(),
                description: format!("Memory usage is {} bytes, exceeding threshold of {} bytes", 
                                   metrics.memory_usage, thresholds.memory_threshold),
                source: "server".to_string(),
                timestamp: Utc::now(),
                duration: None,
                metadata: HashMap::from([
                    ("metric_type".to_string(), "memory_usage".to_string()),
                    ("current_value".to_string(), metrics.memory_usage.to_string()),
                    ("threshold".to_string(), thresholds.memory_threshold.to_string()),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: false,
                resolution_notes: None,
            }).await;
        }
        
        // Response time threshold check
        if metrics.response_time_avg > thresholds.response_time_threshold {
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: DebugEventType::PerformanceMetric,
                severity: DebugSeverity::Medium,
                title: "High Response Time".to_string(),
                description: format!("Average response time is {}ms, exceeding threshold of {}ms", 
                                   metrics.response_time_avg, thresholds.response_time_threshold),
                source: "server".to_string(),
                timestamp: Utc::now(),
                duration: None,
                metadata: HashMap::from([
                    ("metric_type".to_string(), "response_time".to_string()),
                    ("current_value".to_string(), metrics.response_time_avg.to_string()),
                    ("threshold".to_string(), thresholds.response_time_threshold.to_string()),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: false,
                resolution_notes: None,
            }).await;
        }
        
        // Error rate threshold check
        if metrics.error_rate > thresholds.error_rate_threshold {
            self.log_event(DebugEvent {
                id: Uuid::new_v4().to_string(),
                event_type: DebugEventType::ErrorEvent,
                severity: DebugSeverity::High,
                title: "High Error Rate".to_string(),
                description: format!("Error rate is {}%, exceeding threshold of {}%", 
                                   metrics.error_rate, thresholds.error_rate_threshold),
                source: "server".to_string(),
                timestamp: Utc::now(),
                duration: None,
                metadata: HashMap::from([
                    ("metric_type".to_string(), "error_rate".to_string()),
                    ("current_value".to_string(), metrics.error_rate.to_string()),
                    ("threshold".to_string(), thresholds.error_rate_threshold.to_string()),
                ]),
                stack_trace: None,
                related_events: Vec::new(),
                resolved: false,
                resolution_notes: None,
            }).await;
        }
    }

    /// Handle alert notifications
    async fn handle_alert(&self, event: DebugEvent) {
        for handler in &self.alert_handlers {
            if handler.get_severity_filter().contains(&event.severity) {
                if let Err(e) = handler.handle_alert(&event).await {
                    error!("Alert handler '{}' failed: {}", handler.get_handler_name(), e);
                }
            }
        }
    }

    /// Get recent events
    pub async fn get_recent_events(&self, limit: Option<usize>) -> Vec<DebugEvent> {
        let events = self.events.read().await;
        let limit = limit.unwrap_or(100);
        
        events.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// Get events by type
    pub async fn get_events_by_type(&self, event_type: DebugEventType) -> Vec<DebugEvent> {
        let events = self.events.read().await;
        
        events.iter()
            .filter(|e| e.event_type == event_type)
            .cloned()
            .collect()
    }

    /// Get performance metrics history
    pub async fn get_metrics_history(&self, hours: Option<u64>) -> Vec<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        let hours = hours.unwrap_or(24);
        let cutoff = Utc::now() - Duration::hours(hours as i64);
        
        history.iter()
            .filter(|m| m.timestamp >= cutoff)
            .cloned()
            .collect()
    }

    /// Get current system health
    pub async fn get_system_health(&self) -> HashMap<String, HealthCheck> {
        self.health_checks.read().await.clone()
    }

    /// Update health check
    pub async fn update_health_check(&self, check: HealthCheck) {
        let mut health_checks = self.health_checks.write().await;
        health_checks.insert(check.component.clone(), check);
    }

    /// Subscribe to debug events
    pub fn subscribe_to_events(&self) -> broadcast::Receiver<DebugEvent> {
        self.event_broadcaster.subscribe()
    }

    /// Subscribe to performance metrics
    pub fn subscribe_to_metrics(&self) -> broadcast::Receiver<PerformanceMetrics> {
        self.metrics_broadcaster.subscribe()
    }

    /// Export debug data
    pub async fn export_debug_data(&self, format: &str, start_time: Option<DateTime<Utc>>, end_time: Option<DateTime<Utc>>) -> Result<String, String> {
        let events = self.events.read().await;
        let metrics = self.metrics_history.read().await;
        
        let start = start_time.unwrap_or_else(|| Utc::now() - Duration::hours(24));
        let end = end_time.unwrap_or_else(|| Utc::now());
        
        let filtered_events: Vec<&DebugEvent> = events.iter()
            .filter(|e| e.timestamp >= start && e.timestamp <= end)
            .collect();
        
        let filtered_metrics: Vec<&PerformanceMetrics> = metrics.iter()
            .filter(|m| m.timestamp >= start && m.timestamp <= end)
            .collect();
        
        match format.to_lowercase().as_str() {
            "json" => {
                let export_data = DebugExportData {
                    events: filtered_events.into_iter().cloned().collect(),
                    metrics: filtered_metrics.into_iter().cloned().collect(),
                    export_timestamp: Utc::now(),
                    time_range: (start, end),
                };
                
                serde_json::to_string_pretty(&export_data)
                    .map_err(|e| format!("JSON serialization failed: {}", e))
            },
            "csv" => {
                // Implement CSV export
                let mut csv_data = String::new();
                csv_data.push_str("timestamp,event_type,severity,title,description,source,duration\n");
                
                for event in filtered_events {
                    csv_data.push_str(&format!(
                        "{},{:?},{:?},{},{},{},{}\n",
                        event.timestamp.format("%Y-%m-%d %H:%M:%S"),
                        event.event_type,
                        event.severity,
                        event.title.replace(',', ';'),
                        event.description.replace(',', ';'),
                        event.source,
                        event.duration.unwrap_or(0)
                    ));
                }
                
                Ok(csv_data)
            },
            _ => Err(format!("Unsupported export format: {}", format)),
        }
    }

    /// Clean up old data
    pub async fn cleanup_old_data(&self) {
        let config = self.config.read().await;
        
        if !config.auto_cleanup {
            return;
        }
        
        let cutoff = Utc::now() - Duration::hours(config.retention_hours as i64);
        
        // Clean up events
        {
            let mut events = self.events.write().await;
            while let Some(front) = events.front() {
                if front.timestamp < cutoff {
                    events.pop_front();
                } else {
                    break;
                }
            }
        }
        
        // Clean up metrics
        {
            let mut metrics = self.metrics_history.write().await;
            while let Some(front) = metrics.front() {
                if front.timestamp < cutoff {
                    metrics.pop_front();
                } else {
                    break;
                }
            }
        }
    }
}

/// Debug export data structure
#[derive(Debug, Serialize, Deserialize)]
pub struct DebugExportData {
    pub events: Vec<DebugEvent>,
    pub metrics: Vec<PerformanceMetrics>,
    pub export_timestamp: DateTime<Utc>,
    pub time_range: (DateTime<Utc>, DateTime<Utc>),
}

/// Log alert handler
pub struct LogAlertHandler;

impl LogAlertHandler {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl AlertHandler for LogAlertHandler {
    async fn handle_alert(&self, event: &DebugEvent) -> Result<(), String> {
        // Enhanced logging for alerts
        match event.severity {
            DebugSeverity::Critical => {
                error!("🚨 CRITICAL ALERT: {} - {} (Source: {})", 
                       event.title, event.description, event.source);
            },
            DebugSeverity::High => {
                warn!("⚠️  HIGH ALERT: {} - {} (Source: {})", 
                      event.title, event.description, event.source);
            },
            _ => {
                info!("ℹ️  ALERT: {} - {} (Source: {})", 
                      event.title, event.description, event.source);
            },
        }
        
        Ok(())
    }
    
    fn get_handler_name(&self) -> String {
        "LogAlertHandler".to_string()
    }
    
    fn get_severity_filter(&self) -> Vec<DebugSeverity> {
        vec![DebugSeverity::Critical, DebugSeverity::High, DebugSeverity::Medium]
    }
}

/// Metrics alert handler
pub struct MetricsAlertHandler;

impl MetricsAlertHandler {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl AlertHandler for MetricsAlertHandler {
    async fn handle_alert(&self, event: &DebugEvent) -> Result<(), String> {
        // Could send metrics to external monitoring systems
        // For now, just log structured metrics
        
        let metric_data = serde_json::json!({
            "alert_id": event.id,
            "timestamp": event.timestamp,
            "severity": event.severity,
            "type": event.event_type,
            "source": event.source,
            "metadata": event.metadata
        });
        
        info!("📊 METRICS_ALERT: {}", metric_data);
        
        Ok(())
    }
    
    fn get_handler_name(&self) -> String {
        "MetricsAlertHandler".to_string()
    }
    
    fn get_severity_filter(&self) -> Vec<DebugSeverity> {
        vec![DebugSeverity::Critical, DebugSeverity::High]
    }
}

/// Convenience functions for common debug operations
pub mod debug_utils {
    use super::*;
    
    /// Create a simple info event
    pub fn create_info_event(title: String, description: String, source: String) -> DebugEvent {
        DebugEvent {
            id: Uuid::new_v4().to_string(),
            event_type: DebugEventType::InfoEvent,
            severity: DebugSeverity::Info,
            title,
            description,
            source,
            timestamp: Utc::now(),
            duration: None,
            metadata: HashMap::new(),
            stack_trace: None,
            related_events: Vec::new(),
            resolved: false,
            resolution_notes: None,
        }
    }
    
    /// Create an error event with stack trace
    pub fn create_error_event(title: String, description: String, source: String, error: &dyn std::error::Error) -> DebugEvent {
        DebugEvent {
            id: Uuid::new_v4().to_string(),
            event_type: DebugEventType::ErrorEvent,
            severity: DebugSeverity::High,
            title,
            description,
            source,
            timestamp: Utc::now(),
            duration: None,
            metadata: HashMap::new(),
            stack_trace: Some(format!("{:?}", error)),
            related_events: Vec::new(),
            resolved: false,
            resolution_notes: None,
        }
    }
    
    /// Create a performance event
    pub fn create_performance_event(title: String, description: String, duration: u64, metadata: HashMap<String, String>) -> DebugEvent {
        DebugEvent {
            id: Uuid::new_v4().to_string(),
            event_type: DebugEventType::PerformanceMetric,
            severity: DebugSeverity::Info,
            title,
            description,
            source: "server".to_string(),
            timestamp: Utc::now(),
            duration: Some(duration),
            metadata,
            stack_trace: None,
            related_events: Vec::new(),
            resolved: false,
            resolution_notes: None,
        }
    }
    
    /// Create a security event
    pub fn create_security_event(title: String, description: String, source: String, severity: DebugSeverity) -> DebugEvent {
        DebugEvent {
            id: Uuid::new_v4().to_string(),
            event_type: DebugEventType::SecurityEvent,
            severity,
            title,
            description,
            source,
            timestamp: Utc::now(),
            duration: None,
            metadata: HashMap::new(),
            stack_trace: None,
            related_events: Vec::new(),
            resolved: false,
            resolution_notes: None,
        }
    }
} 