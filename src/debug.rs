use std::collections::VecDeque;
use std::sync::{<PERSON>, Mutex};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use tokio::sync::broadcast;
use tracing::{info, warn, error, debug};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum DebugLevel {
    Info,
    Warning,
    Error,
    Debug,
    Success,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct DebugEvent {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub level: DebugLevel,
    pub message: String,
    pub source: String,
    pub details: Option<serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemStatus {
    pub server_uptime: u64,
    pub active_connections: u32,
    pub total_agents: u32,
    pub memory_usage: u64,
    pub cpu_usage: f64,
    pub disk_usage: u64,
    pub network_traffic: NetworkTraffic,
    pub last_activity: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkTraffic {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStatus {
    pub id: String,
    pub hostname: String,
    pub ip_address: String,
    pub os_info: String,
    pub last_seen: DateTime<Utc>,
    pub status: String,
    pub capabilities: Vec<String>,
    pub active_commands: u32,
}

pub struct DebugManager {
    events: Arc<Mutex<VecDeque<DebugEvent>>>,
    max_events: usize,
    broadcast_tx: broadcast::Sender<DebugEvent>,
    system_start_time: DateTime<Utc>,
}

impl DebugManager {
    pub fn new(max_events: usize) -> Self {
        let (broadcast_tx, _) = broadcast::channel(1000);
        Self {
            events: Arc::new(Mutex::new(VecDeque::new())),
            max_events,
            broadcast_tx,
            system_start_time: Utc::now(),
        }
    }

    pub fn log(&self, level: DebugLevel, message: String, source: String) {
        let event = DebugEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            level: level.clone(),
            message,
            source: source.clone(),
            details: None,
        };

        // Add to events queue
        if let Ok(mut events) = self.events.lock() {
            events.push_back(event.clone());
            if events.len() > self.max_events {
                events.pop_front();
            }
        }

        // Broadcast to subscribers
        let _ = self.broadcast_tx.send(event.clone());

        // Log to tracing
        match level {
            DebugLevel::Info => info!("[{}] {}", source, event.message),
            DebugLevel::Warning => warn!("[{}] {}", source, event.message),
            DebugLevel::Error => error!("[{}] {}", source, event.message),
            DebugLevel::Debug => debug!("[{}] {}", source, event.message),
            DebugLevel::Success => info!("[{}] ✅ {}", source, event.message),
        }
    }

    pub fn log_with_details(&self, level: DebugLevel, message: String, source: String, details: serde_json::Value) {
        let event = DebugEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            level: level.clone(),
            message,
            source: source.clone(),
            details: Some(details.clone()),
        };

        if let Ok(mut events) = self.events.lock() {
            events.push_back(event.clone());
            if events.len() > self.max_events {
                events.pop_front();
            }
        }

        let _ = self.broadcast_tx.send(event.clone());

        match level {
            DebugLevel::Info => info!("[{}] {} - Details: {:?}", source, event.message, details),
            DebugLevel::Warning => warn!("[{}] {} - Details: {:?}", source, event.message, details),
            DebugLevel::Error => error!("[{}] {} - Details: {:?}", source, event.message, details),
            DebugLevel::Debug => debug!("[{}] {} - Details: {:?}", source, event.message, details),
            DebugLevel::Success => info!("[{}] ✅ {} - Details: {:?}", source, event.message, details),
        }
    }

    pub fn get_events(&self) -> Vec<DebugEvent> {
        if let Ok(events) = self.events.lock() {
            events.iter().cloned().collect()
        } else {
            Vec::new()
        }
    }

    pub fn subscribe(&self) -> broadcast::Receiver<DebugEvent> {
        self.broadcast_tx.subscribe()
    }

    pub fn get_system_status(&self) -> SystemStatus {
        // This would be enhanced with actual system metrics
        SystemStatus {
            server_uptime: (Utc::now() - self.system_start_time).num_seconds() as u64,
            active_connections: 0, // Would be updated from connection manager
            total_agents: 0, // Would be updated from agent manager
            memory_usage: 0, // Would be updated with actual memory usage
            cpu_usage: 0.0, // Would be updated with actual CPU usage
            disk_usage: 0, // Would be updated with actual disk usage
            network_traffic: NetworkTraffic {
                bytes_sent: 0,
                bytes_received: 0,
                packets_sent: 0,
                packets_received: 0,
            },
            last_activity: Utc::now(),
        }
    }

    pub fn echo_test(&self, message: &str) -> String {
        let echo_message = format!("Echo: {}", message);
        self.log(DebugLevel::Info, echo_message.clone(), "EchoTest".to_string());
        echo_message
    }

    pub fn clear_events(&self) {
        if let Ok(mut events) = self.events.lock() {
            events.clear();
        }
    }
}

// Global debug manager instance
lazy_static::lazy_static! {
    pub static ref DEBUG_MANAGER: Arc<DebugManager> = Arc::new(DebugManager::new(1000));
}

// Convenience functions
pub fn log_info(message: &str, source: &str) {
    DEBUG_MANAGER.log(DebugLevel::Info, message.to_string(), source.to_string());
}

pub fn log_warning(message: &str, source: &str) {
    DEBUG_MANAGER.log(DebugLevel::Warning, message.to_string(), source.to_string());
}

pub fn log_error(message: &str, source: &str) {
    DEBUG_MANAGER.log(DebugLevel::Error, message.to_string(), source.to_string());
}

pub fn log_debug(message: &str, source: &str) {
    DEBUG_MANAGER.log(DebugLevel::Debug, message.to_string(), source.to_string());
}

pub fn log_success(message: &str, source: &str) {
    DEBUG_MANAGER.log(DebugLevel::Success, message.to_string(), source.to_string());
}

pub fn echo_test(message: &str) -> String {
    DEBUG_MANAGER.echo_test(message)
} 