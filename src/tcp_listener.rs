// ===============================================================================
// Ikunc2 C2 服务器 - TCP监听器模块
// 处理TCP协议的代理连接和通信
// ===============================================================================

use std::net::SocketAddr;
use tokio::net::TcpListener;
use tracing::{info, error, warn, debug};
use std::time::Duration;

use crate::server::{AppState, handle_agent_connection};

// 启动TCP监听器 - 接受代理的TCP连接
pub async fn start_tcp_listener(addr: SocketAddr, state: AppState) -> Result<(), Box<dyn std::error::Error>> {
    // 改进的绑定逻辑，包含重试机制
    let listener = match TcpListener::bind(addr).await {
        Ok(listener) => {
            info!("✅ TCP listener successfully bound to {}", addr);
            listener
        }
        Err(e) => {
            error!("❌ Failed to bind TCP listener to {}: {}", addr, e);
            
            // 尝试绑定到备用端口
            let backup_port = addr.port() + 1;
            let backup_addr = SocketAddr::new(addr.ip(), backup_port);
            
            warn!("🔄 Attempting to bind to backup port {}", backup_port);
            match TcpListener::bind(backup_addr).await {
                Ok(listener) => {
                    warn!("⚠️  TCP listener bound to backup address: {}", backup_addr);
                    info!("💡 请更新客户端配置以连接到端口 {}", backup_port);
                    listener
                }
                Err(backup_err) => {
                    error!("❌ Failed to bind to backup port {}: {}", backup_port, backup_err);
                    return Err(format!("无法绑定TCP监听器到端口 {} 或备用端口 {}", addr.port(), backup_port).into());
                }
            }
        }
    };

    let mut connection_count = 0u64;
    let mut error_count = 0u64;
    const MAX_CONSECUTIVE_ERRORS: u64 = 10;
    
    info!("🔌 TCP listener started and waiting for connections...");

    loop {
        match listener.accept().await {
            Ok((stream, client_addr)) => {
                connection_count += 1;
                error_count = 0; // 重置错误计数
                
                info!("🔗 New TCP connection #{} from: {}", connection_count, client_addr);
                debug!("📊 Total connections handled: {}", connection_count);
                
                let state_clone = state.clone();
                tokio::spawn(async move {
                    handle_agent_connection(state_clone, stream, client_addr).await;
                    debug!("✅ Connection to {} handled", client_addr);
                });
            }
            Err(e) => {
                error_count += 1;
                error!("❌ Failed to accept TCP connection (error #{}/{}): {}", 
                    error_count, MAX_CONSECUTIVE_ERRORS, e);
                
                if error_count >= MAX_CONSECUTIVE_ERRORS {
                    error!("💥 Too many consecutive connection errors ({}), TCP listener may be unstable", 
                        MAX_CONSECUTIVE_ERRORS);
                    error!("🔄 Restarting TCP listener...");
                    
                    // 短暂延迟后重新开始
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    error_count = 0;
                    
                    warn!("🔄 TCP listener restarted, continuing to accept connections...");
                } else {
                    // 对于偶尔的错误，只是短暂等待然后继续
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }
    }
} 