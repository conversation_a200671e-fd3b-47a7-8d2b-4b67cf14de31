# ===============================================================================
# Ikunc2 C2 Server - Agent Connection Diagnostic Script
# Helps troubleshoot agent connection timeouts and issues
# ===============================================================================

param(
    [string]$ServerIP = "127.0.0.1",
    [int]$Port = 8080,
    [string]$Protocol = "http"
)

Write-Host "🔍 Ikunc2 Agent Connection Diagnostics" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Target Configuration:" -ForegroundColor Yellow
Write-Host "  Server IP: $ServerIP" -ForegroundColor White
Write-Host "  Port: $Port" -ForegroundColor White
Write-Host "  Protocol: $Protocol" -ForegroundColor White
Write-Host ""

# Test 1: Basic network connectivity
Write-Host "🌐 Test 1: Network Connectivity" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

try {
    $ping = Test-Connection -ComputerName $ServerIP -Count 4 -Quiet
    if ($ping) {
        Write-Host "✅ Ping successful - Network is reachable" -ForegroundColor Green
    } else {
        Write-Host "❌ Ping failed - Network connectivity issue" -ForegroundColor Red
        Write-Host "   Check if the server IP is correct and reachable" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Ping test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Port connectivity
Write-Host "🔌 Test 2: Port Connectivity" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

try {
    $connection = Test-NetConnection -ComputerName $ServerIP -Port $Port -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ Port $Port is open and accepting connections" -ForegroundColor Green
    } else {
        Write-Host "❌ Port $Port is not accessible" -ForegroundColor Red
        Write-Host "   Possible causes:" -ForegroundColor Yellow
        Write-Host "   - Server is not running on port $Port" -ForegroundColor Yellow
        Write-Host "   - Firewall is blocking the port" -ForegroundColor Yellow
        Write-Host "   - Wrong port number" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Port test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: HTTP endpoint test
Write-Host "🌍 Test 3: HTTP Endpoint Test" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

$baseUrl = "${Protocol}://${ServerIP}:${Port}"
$registerUrl = "$baseUrl/api/agents/register"

Write-Host "Testing endpoint: $registerUrl" -ForegroundColor White

try {
    # Create a test agent info payload
    $testAgent = @{
        id = "test-agent-$(Get-Random)"
        hostname = $env:COMPUTERNAME
        username = $env:USERNAME
        os = "windows"
        arch = "x86_64"
        ip = "127.0.0.1"
    }
    
    $headers = @{
        "Content-Type" = "application/json"
        "User-Agent" = "Ikunc2-Agent-Test"
    }
    
    $response = Invoke-WebRequest -Uri $registerUrl -Method POST -Body ($testAgent | ConvertTo-Json) -Headers $headers -TimeoutSec 30 -UseBasicParsing
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Server is responding correctly" -ForegroundColor Green
        Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor White
    } else {
        Write-Host "⚠️  Server responded with status: $($response.StatusCode)" -ForegroundColor Yellow
        Write-Host "   This might be expected for test data" -ForegroundColor Yellow
    }
} catch {
    $errorMessage = $_.Exception.Message
    Write-Host "❌ HTTP test failed: $errorMessage" -ForegroundColor Red
    
    if ($errorMessage -like "*timeout*") {
        Write-Host "   -> Timeout error: Server is slow or unreachable" -ForegroundColor Yellow
    } elseif ($errorMessage -like "*connection*") {
        Write-Host "   -> Connection error: Server not running or wrong port" -ForegroundColor Yellow
    } elseif ($errorMessage -like "*SSL*" -or $errorMessage -like "*TLS*") {
        Write-Host "   -> SSL/TLS error: Certificate issues or protocol mismatch" -ForegroundColor Yellow
    }
}

Write-Host ""

# Test 4: Server process check
Write-Host "🖥️  Test 4: Server Process Check" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$serverProcesses = Get-Process | Where-Object { $_.ProcessName -like "*c2-gui*" -or $_.ProcessName -like "*ikunc2*" }

if ($serverProcesses) {
    Write-Host "✅ Found potential server processes:" -ForegroundColor Green
    foreach ($proc in $serverProcesses) {
        Write-Host "   Process: $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor White
    }
} else {
    Write-Host "❌ No server processes found" -ForegroundColor Red
    Write-Host "   Make sure the C2 server is running" -ForegroundColor Yellow
}

Write-Host ""

# Test 5: Firewall check
Write-Host "🛡️  Test 5: Firewall Check" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

try {
    $firewallRules = Get-NetFirewallRule | Where-Object { 
        $_.DisplayName -like "*Ikunc2*" -or 
        $_.DisplayName -like "*$Port*" 
    } | Where-Object { $_.Enabled -eq "True" }
    
    if ($firewallRules) {
        Write-Host "✅ Found firewall rules for port $Port" -ForegroundColor Green
        foreach ($rule in $firewallRules) {
            Write-Host "   Rule: $($rule.DisplayName) - $($rule.Direction)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️  No firewall rules found for port $Port" -ForegroundColor Yellow
        Write-Host "   You may need to configure firewall rules" -ForegroundColor Yellow
        Write-Host "   Run: .\setup_windows_firewall.ps1" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Firewall check failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 6: Port usage check
Write-Host "📊 Test 6: Port Usage Check" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

try {
    $connections = Get-NetTCPConnection | Where-Object { $_.LocalPort -eq $Port }
    
    if ($connections) {
        Write-Host "✅ Port $Port is in use:" -ForegroundColor Green
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            $processName = if ($process) { $process.ProcessName } else { "Unknown" }
            Write-Host "   State: $($conn.State) - Process: $processName (PID: $($conn.OwningProcess))" -ForegroundColor White
        }
    } else {
        Write-Host "❌ Port $Port is not in use" -ForegroundColor Red
        Write-Host "   The server may not be listening on this port" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Port usage check failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Summary and recommendations
Write-Host "📋 Summary and Recommendations" -ForegroundColor Magenta
Write-Host "===============================" -ForegroundColor Magenta

Write-Host ""
Write-Host "Common Issues and Solutions:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. 🚫 Server not running:" -ForegroundColor White
Write-Host "   - Build and start the server: cargo run --release" -ForegroundColor Cyan
Write-Host "   - Check if it's listening on the correct port" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. 🔒 Firewall blocking:" -ForegroundColor White
Write-Host "   - Run firewall setup: .\setup_windows_firewall.ps1" -ForegroundColor Cyan
Write-Host "   - Check Windows Defender Firewall settings" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. 🌐 Network issues:" -ForegroundColor White
Write-Host "   - Verify the server IP address is correct" -ForegroundColor Cyan
Write-Host "   - Check if you're using the right network interface" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. ⚙️  Port mismatch:" -ForegroundColor White
Write-Host "   - Default server port is 8080, not 80" -ForegroundColor Cyan
Write-Host "   - Make sure agent and server use the same port" -ForegroundColor Cyan
Write-Host ""
Write-Host "5. 🔐 Protocol mismatch:" -ForegroundColor White
Write-Host "   - Use HTTP for testing, HTTPS for production" -ForegroundColor Cyan
Write-Host "   - Check TLS certificate configuration" -ForegroundColor Cyan

Write-Host ""
Write-Host "✅ Diagnostic completed!" -ForegroundColor Green 