@echo off
echo 🚀 Building Ikunc2 C2 System...

REM Check if Rust is installed
cargo --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Rust/Cargo not found. Please install Rust first.
    exit /b 1
)

echo [INFO] Building main application...
cargo build --release
if errorlevel 1 (
    echo [ERROR] Failed to build main application
    exit /b 1
)
echo [INFO] Main application built successfully!

echo [INFO] Building agent...
cd agent
cargo build --release
if errorlevel 1 (
    echo [ERROR] Failed to build agent
    exit /b 1
)
echo [INFO] Agent built successfully!
cd ..

REM Create output directory
if not exist dist mkdir dist
copy target\release\c2-gui.exe dist\ >nul
copy agent\target\release\agent.exe dist\ >nul

echo [INFO] Build completed! Files are in the 'dist' directory:
echo   - c2-gui.exe (main application)
echo   - agent.exe (agent binary)

echo [INFO] To run the server:
echo   dist\c2-gui.exe server

echo [INFO] To run the GUI client:
echo   dist\c2-gui.exe

echo [INFO] To run the agent:
echo   set AGENT_IP=127.0.0.1
echo   set AGENT_PORT=5555
echo   dist\agent.exe

pause 