# PowerShell script to ensure the 'data' directory exists for the C2 GUI server

$releaseDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$dataDir = Join-Path $releaseDir 'data'

if (-Not (Test-Path $dataDir)) {
    Write-Host "Creating data directory at: $dataDir"
    New-Item -ItemType Directory -Path $dataDir | Out-Null
} else {
    Write-Host "Data directory already exists: $dataDir"
}

# Optionally, set permissions (uncomment if needed)
# $user = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
# icacls $dataDir /grant "$user:(OI)(CI)F" | Out-Null

Write-Host "Database directory check complete." 