#!/bin/bash

# Ikunc2 C2 Server - Ubuntu VPS Deployment Script
# This script sets up the complete environment and deploys the C2 server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ikunc2"
SERVICE_NAME="ikunc2-c2"
USER_NAME="ikunc2"
INSTALL_DIR="/opt/ikunc2"
LOG_DIR="/var/log/ikunc2"
DATA_DIR="/var/lib/ikunc2"

echo -e "${BLUE}🚀 Ikunc2 C2 Server - Ubuntu VPS Deployment${NC}"
echo "=================================================="

# Function to print status messages
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
   exit 1
fi

# Update system packages
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required packages
print_status "Installing required packages..."
sudo apt install -y curl wget git build-essential pkg-config libssl-dev

# Install Rust if not already installed
if ! command -v cargo &> /dev/null; then
    print_status "Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    export PATH="$HOME/.cargo/bin:$PATH"
else
    print_status "Rust is already installed"
    source ~/.cargo/env
fi

# Create service user
if ! id "$USER_NAME" &>/dev/null; then
    print_status "Creating service user: $USER_NAME"
    sudo useradd -r -s /bin/false -d $INSTALL_DIR $USER_NAME
else
    print_status "Service user $USER_NAME already exists"
fi

# Create directories
print_status "Creating installation directories..."
sudo mkdir -p $INSTALL_DIR
sudo mkdir -p $LOG_DIR
sudo mkdir -p $DATA_DIR
sudo mkdir -p $INSTALL_DIR/logs

# Set ownership
sudo chown -R $USER_NAME:$USER_NAME $INSTALL_DIR
sudo chown -R $USER_NAME:$USER_NAME $LOG_DIR
sudo chown -R $USER_NAME:$USER_NAME $DATA_DIR

# Set permissions
sudo chmod 755 $INSTALL_DIR
sudo chmod 755 $LOG_DIR
sudo chmod 755 $DATA_DIR
sudo chmod 755 $INSTALL_DIR/logs

# Build the application
print_status "Building Ikunc2 C2 Server..."
cargo build --release

# Copy binary to installation directory
print_status "Installing binary..."
sudo cp target/release/c2-gui $INSTALL_DIR/ikunc2-server
sudo chown $USER_NAME:$USER_NAME $INSTALL_DIR/ikunc2-server
sudo chmod +x $INSTALL_DIR/ikunc2-server

# Create systemd service file
print_status "Creating systemd service..."
sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<EOF
[Unit]
Description=Ikunc2 C2 Server
After=network.target

[Service]
Type=simple
User=$USER_NAME
Group=$USER_NAME
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/ikunc2-server
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Environment variables
Environment=IKUNC2_DB_PATH=$DATA_DIR/ikunc2.db
Environment=IKUNC2_LOG_FILE=$LOG_DIR/ikunc2.log
Environment=IKUNC2_LOG_LEVEL=info
Environment=IKUNC2_HOST=0.0.0.0
Environment=IKUNC2_PORT=8080
Environment=IKUNC2_TCP_PORT=5555
Environment=RUST_LOG=info

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DATA_DIR $LOG_DIR

[Install]
WantedBy=multi-user.target
EOF

# Create environment file for easy configuration
print_status "Creating environment configuration file..."
sudo tee $INSTALL_DIR/.env > /dev/null <<EOF
# Ikunc2 C2 Server Configuration
# Database
IKUNC2_DB_PATH=$DATA_DIR/ikunc2.db

# Logging
IKUNC2_LOG_FILE=$LOG_DIR/ikunc2.log
IKUNC2_LOG_LEVEL=info

# Server
IKUNC2_HOST=0.0.0.0
IKUNC2_PORT=8080
IKUNC2_TCP_PORT=5555

# Security (change these in production!)
IKUNC2_SESSION_SECRET=$(openssl rand -base64 64)
IKUNC2_SESSION_TIMEOUT=24

# Database settings
IKUNC2_DB_MAX_CONNECTIONS=10
EOF

sudo chown $USER_NAME:$USER_NAME $INSTALL_DIR/.env
sudo chmod 600 $INSTALL_DIR/.env

# Create logrotate configuration
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/$SERVICE_NAME > /dev/null <<EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER_NAME $USER_NAME
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

# Enable and start the service
print_status "Enabling and starting service..."
sudo systemctl daemon-reload
sudo systemctl enable $SERVICE_NAME
sudo systemctl start $SERVICE_NAME

# Check service status
if sudo systemctl is-active --quiet $SERVICE_NAME; then
    print_status "Service started successfully!"
else
    print_error "Service failed to start. Check logs with: sudo journalctl -u $SERVICE_NAME"
    exit 1
fi

# Create firewall rules (if ufw is available)
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall rules..."
    sudo ufw allow 8080/tcp comment "Ikunc2 Web GUI"
    sudo ufw allow 5555/tcp comment "Ikunc2 TCP Listener"
    print_warning "Firewall rules added. Make sure to enable UFW if needed: sudo ufw enable"
fi

# Create management script
print_status "Creating management script..."
sudo tee $INSTALL_DIR/manage.sh > /dev/null <<EOF
#!/bin/bash

SERVICE_NAME="$SERVICE_NAME"
INSTALL_DIR="$INSTALL_DIR"

case "\$1" in
    start)
        sudo systemctl start \$SERVICE_NAME
        ;;
    stop)
        sudo systemctl stop \$SERVICE_NAME
        ;;
    restart)
        sudo systemctl restart \$SERVICE_NAME
        ;;
    status)
        sudo systemctl status \$SERVICE_NAME
        ;;
    logs)
        sudo journalctl -u \$SERVICE_NAME -f
        ;;
    config)
        sudo nano \$INSTALL_DIR/.env
        sudo systemctl restart \$SERVICE_NAME
        ;;
    backup)
        sudo cp \$INSTALL_DIR/.env \$INSTALL_DIR/.env.backup.\$(date +%Y%m%d_%H%M%S)
        sudo cp $DATA_DIR/ikunc2.db $DATA_DIR/ikunc2.db.backup.\$(date +%Y%m%d_%H%M%S)
        echo "Backup created"
        ;;
    *)
        echo "Usage: \$0 {start|stop|restart|status|logs|config|backup}"
        exit 1
        ;;
esac
EOF

sudo chmod +x $INSTALL_DIR/manage.sh
sudo chown $USER_NAME:$USER_NAME $INSTALL_DIR/manage.sh

# Final status
print_status "Deployment completed successfully!"
echo ""
echo -e "${GREEN}🎯 Ikunc2 C2 Server is now running!${NC}"
echo ""
echo -e "${BLUE}📋 Service Information:${NC}"
echo "  Service Name: $SERVICE_NAME"
echo "  Installation: $INSTALL_DIR"
echo "  Database: $DATA_DIR/ikunc2.db"
echo "  Logs: $LOG_DIR/"
echo "  Web GUI: http://$(hostname -I | awk '{print $1}'):8080"
echo "  TCP Listener: $(hostname -I | awk '{print $1}'):5555"
echo ""
echo -e "${BLUE}🔧 Management Commands:${NC}"
echo "  Start:   $INSTALL_DIR/manage.sh start"
echo "  Stop:    $INSTALL_DIR/manage.sh stop"
echo "  Restart: $INSTALL_DIR/manage.sh restart"
echo "  Status:  $INSTALL_DIR/manage.sh status"
echo "  Logs:    $INSTALL_DIR/manage.sh logs"
echo "  Config:  $INSTALL_DIR/manage.sh config"
echo "  Backup:  $INSTALL_DIR/manage.sh backup"
echo ""
echo -e "${YELLOW}⚠️  Security Notes:${NC}"
echo "  1. Change the default admin password in the web interface"
echo "  2. Update the session secret in $INSTALL_DIR/.env"
echo "  3. Configure firewall rules as needed"
echo "  4. Consider using HTTPS with a reverse proxy"
echo ""
echo -e "${GREEN}✅ Deployment complete!${NC}" 