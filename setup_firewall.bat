@echo off
REM ===============================================================================
REM Ikunc2 C2 Server - Windows Firewall Configuration (Batch Version)
REM Configures firewall rules for agent-server communication
REM ===============================================================================

echo.
echo 🛡️  Ikunc2 C2 Server - Windows Firewall Configuration
echo ====================================================

REM Check for Administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
) else (
    echo ❌ This script requires Administrator privileges!
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo ℹ️  Configuring firewall rules for ports 80, 443, and 8080...

REM Remove existing rules (if they exist)
echo Removing any existing Ikunc2 firewall rules...
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-80" >nul 2>&1
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-80" >nul 2>&1
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-443" >nul 2>&1
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-443" >nul 2>&1
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-8080" >nul 2>&1
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-8080" >nul 2>&1

REM Create inbound rules
echo Creating inbound firewall rules...

echo   Configuring port 80 (HTTP)...
netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Inbound-80" dir=in action=allow protocol=TCP localport=80 description="Ikunc2 C2 Server - Allow inbound HTTP connections"
if %errorLevel% == 0 (
    echo   ✅ Port 80 inbound rule created
) else (
    echo   ❌ Failed to create port 80 inbound rule
)

echo   Configuring port 443 (HTTPS)...
netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Inbound-443" dir=in action=allow protocol=TCP localport=443 description="Ikunc2 C2 Server - Allow inbound HTTPS connections"
if %errorLevel% == 0 (
    echo   ✅ Port 443 inbound rule created
) else (
    echo   ❌ Failed to create port 443 inbound rule
)

echo   Configuring port 8080 (HTTP Alternative)...
netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Inbound-8080" dir=in action=allow protocol=TCP localport=8080 description="Ikunc2 C2 Server - Allow inbound HTTP alternative connections"
if %errorLevel% == 0 (
    echo   ✅ Port 8080 inbound rule created
) else (
    echo   ❌ Failed to create port 8080 inbound rule
)

REM Create outbound rules
echo Creating outbound firewall rules...

netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Outbound-80" dir=out action=allow protocol=TCP remoteport=80 description="Ikunc2 C2 Server - Allow outbound HTTP connections"
if %errorLevel% == 0 (
    echo   ✅ Port 80 outbound rule created
) else (
    echo   ❌ Failed to create port 80 outbound rule
)

netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Outbound-443" dir=out action=allow protocol=TCP remoteport=443 description="Ikunc2 C2 Server - Allow outbound HTTPS connections"
if %errorLevel% == 0 (
    echo   ✅ Port 443 outbound rule created
) else (
    echo   ❌ Failed to create port 443 outbound rule
)

netsh advfirewall firewall add rule name="Ikunc2-C2-Server-Outbound-8080" dir=out action=allow protocol=TCP remoteport=8080 description="Ikunc2 C2 Server - Allow outbound HTTP alternative connections"
if %errorLevel% == 0 (
    echo   ✅ Port 8080 outbound rule created
) else (
    echo   ❌ Failed to create port 8080 outbound rule
)

echo.
echo ✅ Firewall configuration completed!
echo.
echo ℹ️  Current firewall status:
netsh advfirewall show allprofiles state

echo.
echo 🎯 Next steps:
echo 1. Start your Ikunc2 C2 server
echo 2. Verify agents can connect on ports: 80, 443, 8080
echo 3. Monitor connections in the server dashboard
echo.
echo 🛡️  Security Notes:
echo - These rules allow traffic on the specified ports
echo - Ensure your C2 server uses proper authentication
echo - Monitor network traffic for security
echo - Consider using HTTPS (port 443) for encrypted communication
echo.

pause 