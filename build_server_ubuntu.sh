#!/bin/bash

# ===============================================================================
# Ikunc2 C2 Server - Enhanced Ubuntu Deployment Script
# Advanced automation with comprehensive error handling and security features
# ===============================================================================

set -euo pipefail  # Enhanced error handling
IFS=$'\n\t'       # Secure Internal Field Separator

# Color codes for enhanced output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# Script configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_NAME="ikunc2"
readonly BUILD_DATE="$(date '+%Y-%m-%d_%H-%M-%S')"
readonly BUILD_VERSION="v2.0.0"
readonly DIST_DIR="${SCRIPT_DIR}/dist"
readonly LOGS_DIR="${SCRIPT_DIR}/logs"
readonly BACKUP_DIR="${SCRIPT_DIR}/backups"

# System requirements
readonly MIN_RUST_VERSION="1.70.0"
readonly MIN_DISK_SPACE_MB=500
readonly MIN_RAM_MB=512

# Logging setup
mkdir -p "${LOGS_DIR}"
readonly LOG_FILE="${LOGS_DIR}/build_${BUILD_DATE}.log"
exec > >(tee -a "${LOG_FILE}")
exec 2>&1

# ===============================================================================
# Utility Functions
# ===============================================================================

print_banner() {
    echo -e "${CYAN}"
    echo "════════════════════════════════════════════════════════════════════════"
    echo "  🎯 Ikunc2 C2 Server - Enhanced Ubuntu Deployment Script ${BUILD_VERSION}"
    echo "  📊 Hybrid Storage Architecture with Advanced Security Features"
    echo "  🕐 Build Date: ${BUILD_DATE}"
    echo "════════════════════════════════════════════════════════════════════════"
    echo -e "${NC}"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

# Progress indicator
show_progress() {
    local duration=$1
    local sleep_interval=0.1
    local progress=0
    local bar_length=50

    while [ $progress -le $duration ]; do
        local filled_length=$((progress * bar_length / duration))
        local empty_length=$((bar_length - filled_length))
        
        printf "\r${CYAN}["
        printf "%*s" $filled_length | tr ' ' '█'
        printf "%*s" $empty_length | tr ' ' '░'
        printf "] %d%% ${NC}" $((progress * 100 / duration))
        
        sleep $sleep_interval
        progress=$((progress + 1))
    done
    echo
}

# Error handler
error_handler() {
    local line_no=$1
    local error_code=$2
    log_error "An error occurred on line $line_no: exit code $error_code"
    log_error "Check the log file: ${LOG_FILE}"
    exit $error_code
}

trap 'error_handler ${LINENO} $?' ERR

# ===============================================================================
# System Checks and Validations
# ===============================================================================

check_system_requirements() {
    log_step "🔍 Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        log_error "Unable to detect OS. This script is designed for Ubuntu/Debian systems."
        exit 1
    fi
    
    source /etc/os-release
    log_info "Detected OS: $PRETTY_NAME"
    
    # Check architecture
    local arch=$(uname -m)
    log_info "Architecture: $arch"
    
    # Check disk space
    local available_space=$(df . | tail -1 | awk '{print $4}')
    local available_mb=$((available_space / 1024))
    
    if [[ $available_mb -lt $MIN_DISK_SPACE_MB ]]; then
        log_error "Insufficient disk space. Required: ${MIN_DISK_SPACE_MB}MB, Available: ${available_mb}MB"
        exit 1
    fi
    
    log_info "Available disk space: ${available_mb}MB ✓"
    
    # Check RAM
    local ram_mb=$(free -m | awk 'NR==2{print $2}')
    if [[ $ram_mb -lt $MIN_RAM_MB ]]; then
        log_warning "Low RAM detected: ${ram_mb}MB (Recommended: ≥${MIN_RAM_MB}MB)"
    else
        log_info "Available RAM: ${ram_mb}MB ✓"
    fi
    
    # Check required commands
    local required_commands=("curl" "git" "gcc" "make" "pkg-config" "libssl-dev")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null && ! dpkg -l | grep -q "$cmd"; then
            log_warning "Missing dependency: $cmd"
            install_dependencies
            break
        fi
    done
    
    log_success "System requirements check completed"
}

install_dependencies() {
    log_step "📦 Installing system dependencies..."
    
    # Update package list
    log_info "Updating package list..."
    sudo apt-get update -qq
    
    # Install required packages
    local packages=(
        "curl"
        "git" 
        "build-essential"
        "pkg-config"
        "libssl-dev"
        "libsqlite3-dev"
        "ca-certificates"
        "gnupg"
        "lsb-release"
        "software-properties-common"
        "ufw"
        "fail2ban"
        "htop"
        "tmux"
        "jq"
        "gcc-mingw-w64-x86-64"
        "gcc-mingw-w64"
    )
    
    log_info "Installing packages: ${packages[*]}"
    sudo apt-get install -y "${packages[@]}"
    
    log_success "Dependencies installed successfully"
}

check_and_install_rust() {
    log_step "🦀 Managing Rust installation..."
    
    if command -v cargo &> /dev/null; then
        local current_version=$(rustc --version | awk '{print $2}')
        log_info "Found Rust version: $current_version"
        
        # Version comparison (simplified)
        if [[ "$current_version" < "$MIN_RUST_VERSION" ]]; then
            log_warning "Rust version $current_version is below minimum required $MIN_RUST_VERSION"
            log_info "Updating Rust..."
            rustup update
        else
            log_info "Rust version is sufficient ✓"
        fi
    else
        log_info "Installing Rust toolchain..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain stable
        source "$HOME/.cargo/env"
        
        # Add cargo to PATH for this session
        export PATH="$HOME/.cargo/bin:$PATH"
    fi
    
    # Install additional Rust components
    log_info "Installing additional Rust components..."
    rustup component add clippy rustfmt
    
    # Install cross-compilation targets for agent building
    log_info "Installing cross-compilation targets..."
    rustup target add x86_64-pc-windows-gnu
    rustup target add x86_64-unknown-linux-gnu
    rustup target add x86_64-apple-darwin
    
    # Verify installation
    cargo --version
    rustc --version
    
    log_success "Rust installation completed"
}

# ===============================================================================
# Build Process
# ===============================================================================

clean_previous_builds() {
    log_step "🧹 Cleaning previous builds..."
    
    if [[ -d "${DIST_DIR}" ]]; then
        log_info "Backing up previous build..."
        mkdir -p "${BACKUP_DIR}"
        mv "${DIST_DIR}" "${BACKUP_DIR}/dist_backup_${BUILD_DATE}" 2>/dev/null || true
    fi
    
    # Clean Rust build artifacts
    log_info "Cleaning Rust build cache..."
    cargo clean
    
    log_success "Cleanup completed"
}

build_project() {
    log_step "🔨 Building Ikunc2 C2 Server..."
    
    # Set build environment
    export RUST_BACKTRACE=1
    export CARGO_TARGET_DIR="target"
    
    # Build with optimizations
    log_info "Compiling with release optimizations..."
    echo -e "${CYAN}Building... This may take a few minutes${NC}"
    
    # Show progress during build
    cargo build --release --verbose 2>&1 | while IFS= read -r line; do
        echo "$line"
        if [[ "$line" =~ "Compiling" ]]; then
            echo -ne "${YELLOW}●${NC}"
        fi
    done
    echo
    
    # Verify build
    if [[ -f "target/release/c2-gui" ]]; then
        log_success "Build completed successfully"
        local binary_size=$(ls -lh target/release/c2-gui | awk '{print $5}')
        log_info "Binary size: $binary_size"
    else
        log_error "Build failed - binary not found"
        exit 1
    fi
}

create_deployment_structure() {
    log_step "📁 Creating deployment structure..."
    
    # Create directory structure
    mkdir -p "${DIST_DIR}"/{bin,data,logs,config,scripts,docs,ssl}
    
    # Copy binary
    cp target/release/c2-gui "${DIST_DIR}/bin/"
    chmod +x "${DIST_DIR}/bin/c2-gui"
    
    # Create symlink for easier access
    ln -sf "bin/c2-gui" "${DIST_DIR}/c2-gui"
    
    log_success "Deployment structure created"
}

create_configuration_files() {
    log_step "⚙️ Creating configuration files..."
    
    # Main configuration file
    cat > "${DIST_DIR}/config/server.toml" << 'EOF'
[server]
host = "0.0.0.0"
port = 8080
tcp_port = 5555

[security]
tls_enabled = true
cert_path = "ssl/server.crt"
key_path = "ssl/server.key"
session_timeout = 3600
max_login_attempts = 5

[storage]
database_path = "data/users.db"
agents_file = "data/agents.json"
commands_file = "data/command_history.json"
backup_enabled = true
backup_interval = 3600

[logging]
level = "info"
file = "logs/server.log"
max_size = "10MB"
max_files = 5
EOF

    # Environment configuration
    cat > "${DIST_DIR}/config/.env" << EOF
# Ikunc2 C2 Server Configuration
RUST_LOG=info
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
TCP_PORT=5555
DATABASE_URL=sqlite:data/users.db
AGENTS_FILE=data/agents.json
COMMANDS_FILE=data/command_history.json
TLS_CERT_PATH=ssl/server.crt
TLS_KEY_PATH=ssl/server.key
SESSION_SECRET=$(openssl rand -hex 32)
EOF

    # Logging configuration
    cat > "${DIST_DIR}/config/log4rs.yaml" << 'EOF'
appenders:
  stdout:
    kind: console
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S)} [{l}] {M}: {m}{n}"
  file:
    kind: file
    path: "logs/server.log"
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S)} [{l}] {M}: {m}{n}"
    append: true

root:
  level: info
  appenders:
    - stdout
    - file

loggers:
  ikunc2:
    level: debug
    additive: false
    appenders:
      - stdout
      - file
EOF

    log_success "Configuration files created"
}

create_startup_scripts() {
    log_step "🚀 Creating startup scripts..."
    
    # Enhanced startup script
    cat > "${DIST_DIR}/start.sh" << 'EOF'
#!/bin/bash

# Ikunc2 C2 Server Startup Script
# Enhanced version with monitoring and error handling

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $*"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# Banner
echo -e "${CYAN}"
cat << 'BANNER'
  ██╗██╗  ██╗██╗   ██╗███╗   ██╗ ██████╗██████╗ 
  ██║██║ ██╔╝██║   ██║████╗  ██║██╔════╝╚════██╗
  ██║█████╔╝ ██║   ██║██╔██╗ ██║██║      █████╔╝
  ██║██╔═██╗ ██║   ██║██║╚██╗██║██║     ██╔═══╝ 
  ██║██║  ██╗╚██████╔╝██║ ╚████║╚██████╗███████╗
  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚═╝  ╚═══╝ ╚═════╝╚══════╝
                                                 
  Advanced Command & Control Platform v2.0.0
BANNER
echo -e "${NC}"

# Pre-flight checks
log_info "🔍 Performing pre-flight checks..."

# Check if binary exists
if [[ ! -f "c2-gui" ]]; then
    log_error "c2-gui binary not found!"
    exit 1
fi

# Check directories
for dir in data logs config ssl; do
    if [[ ! -d "$dir" ]]; then
        log_info "Creating directory: $dir"
        mkdir -p "$dir"
    fi
done

# Check SSL certificates
if [[ ! -f "ssl/server.crt" ]] || [[ ! -f "ssl/server.key" ]]; then
    log_warning "SSL certificates not found. Generating self-signed certificates..."
    ./scripts/generate_ssl.sh
fi

# Load environment variables
if [[ -f "config/.env" ]]; then
    source config/.env
fi

# System information
log_info "📊 System Information:"
echo "  🖥️  Hostname: $(hostname)"
echo "  🏗️  Architecture: $(uname -m)"
echo "  💾 Available Memory: $(free -h | awk 'NR==2{print $7}')"
echo "  💿 Available Disk: $(df -h . | tail -1 | awk '{print $4}')"
echo ""

log_info "🎯 Server Configuration:"
echo "  🌐 Web Interface: http://localhost:${SERVER_PORT:-8080}"
echo "  🔌 TCP Listener: ${SERVER_HOST:-0.0.0.0}:${TCP_PORT:-5555}"
echo "  🗄️  Database: ${DATABASE_URL:-sqlite:data/users.db}"
echo "  📄 Agents File: ${AGENTS_FILE:-data/agents.json}"
echo "  📝 Commands File: ${COMMANDS_FILE:-data/command_history.json}"
echo "  🔐 TLS: $([ -f ssl/server.crt ] && echo "Enabled" || echo "Disabled")"
echo ""

log_info "🔑 Default Credentials:"
echo "  👤 Username: admin"
echo "  🔒 Password: admin"
echo "  ⚠️  Please change default credentials after first login!"
echo ""

log_info "🚀 Starting Ikunc2 C2 Server..."
echo "  📋 Logs: logs/server.log"
echo "  🛑 Stop: Press Ctrl+C"
echo ""

# Start server with monitoring
exec ./c2-gui 2>&1 | tee -a logs/server.log
EOF

    chmod +x "${DIST_DIR}/start.sh"
    
    # Stop script
    cat > "${DIST_DIR}/stop.sh" << 'EOF'
#!/bin/bash

echo "🛑 Stopping Ikunc2 C2 Server..."

# Find and kill the process
pkill -f "c2-gui" || true

echo "✅ Server stopped"
EOF

    chmod +x "${DIST_DIR}/stop.sh"
    
    # Status script
    cat > "${DIST_DIR}/status.sh" << 'EOF'
#!/bin/bash

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "📊 Ikunc2 C2 Server Status"
echo "=========================="

# Check if process is running
if pgrep -f "c2-gui" > /dev/null; then
    echo -e "Status: ${GREEN}Running${NC}"
    echo "PID: $(pgrep -f "c2-gui")"
else
    echo -e "Status: ${RED}Stopped${NC}"
fi

# Check ports
echo ""
echo "🔌 Port Status:"
if ss -tlnp | grep -q ":8080"; then
    echo -e "  Web (8080): ${GREEN}Listening${NC}"
else
    echo -e "  Web (8080): ${RED}Not listening${NC}"
fi

if ss -tlnp | grep -q ":5555"; then
    echo -e "  TCP (5555): ${GREEN}Listening${NC}"
else
    echo -e "  TCP (5555): ${RED}Not listening${NC}"
fi

# Check disk space
echo ""
echo "💾 Disk Usage:"
df -h . | tail -1 | awk '{print "  Available: " $4 " (" $5 " used)"}'

# Check logs
echo ""
echo "📋 Recent Logs:"
if [[ -f "logs/server.log" ]]; then
    tail -5 "logs/server.log" | sed 's/^/  /'
else
    echo "  No log file found"
fi
EOF

    chmod +x "${DIST_DIR}/status.sh"
    
    log_success "Startup scripts created"
}

create_utility_scripts() {
    log_step "🛠️ Creating utility scripts..."
    
    mkdir -p "${DIST_DIR}/scripts"
    
    # SSL certificate generation script
    cat > "${DIST_DIR}/scripts/generate_ssl.sh" << 'EOF'
#!/bin/bash

echo "🔐 Generating SSL certificates..."

mkdir -p ssl

# Generate private key
openssl genrsa -out ssl/server.key 2048

# Generate certificate signing request
openssl req -new -key ssl/server.key -out ssl/server.csr -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Generate self-signed certificate
openssl x509 -req -days 365 -in ssl/server.csr -signkey ssl/server.key -out ssl/server.crt

# Clean up
rm ssl/server.csr

echo "✅ SSL certificates generated:"
echo "  Certificate: ssl/server.crt"
echo "  Private Key: ssl/server.key"
EOF

    chmod +x "${DIST_DIR}/scripts/generate_ssl.sh"
    
    # Backup script
    cat > "${DIST_DIR}/scripts/backup.sh" << 'EOF'
#!/bin/bash

BACKUP_DIR="backups/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 Creating backup..."

# Backup data files
if [[ -d "data" ]]; then
    cp -r data "$BACKUP_DIR/"
fi

# Backup configuration
if [[ -d "config" ]]; then
    cp -r config "$BACKUP_DIR/"
fi

# Backup logs (last 100 lines only)
if [[ -f "logs/server.log" ]]; then
    mkdir -p "$BACKUP_DIR/logs"
    tail -100 logs/server.log > "$BACKUP_DIR/logs/server.log"
fi

echo "✅ Backup created: $BACKUP_DIR"
EOF

    chmod +x "${DIST_DIR}/scripts/backup.sh"
    
    # Update script
    cat > "${DIST_DIR}/scripts/update.sh" << 'EOF'
#!/bin/bash

echo "🔄 Updating Ikunc2 C2 Server..."

# Create backup before update
./scripts/backup.sh

# Stop server if running
./stop.sh

echo "⬇️  Download and install updates here"
echo "⚠️  This script needs to be customized for your update mechanism"

echo "✅ Update completed"
EOF

    chmod +x "${DIST_DIR}/scripts/update.sh"
    
    log_success "Utility scripts created"
}

create_systemd_service() {
    log_step "⚙️ Creating systemd service..."
    
    cat > "${DIST_DIR}/ikunc2.service" << EOF
[Unit]
Description=Ikunc2 C2 Server - Advanced Command & Control Platform
Documentation=https://github.com/ikunc2/ikunc2
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=${DIST_DIR}
ExecStart=${DIST_DIR}/c2-gui
ExecStop=/bin/kill -TERM \$MAINPID
ExecReload=/bin/kill -HUP \$MAINPID

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=${DIST_DIR}
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Restart policy
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ikunc2

# Environment
Environment=RUST_LOG=info
EnvironmentFile=-${DIST_DIR}/config/.env

[Install]
WantedBy=multi-user.target
EOF

    log_success "Systemd service file created"
}

setup_security() {
    log_step "🔒 Setting up security configurations..."
    
    # Create security documentation
    cat > "${DIST_DIR}/docs/SECURITY.md" << 'EOF'
# Security Guidelines for Ikunc2 C2 Server

## Initial Setup
1. Change default admin credentials immediately
2. Generate strong SSL certificates for production
3. Configure firewall rules
4. Enable fail2ban for brute force protection

## Firewall Configuration
```bash
# Allow SSH (adjust port as needed)
sudo ufw allow 22/tcp

# Allow web interface
sudo ufw allow 8080/tcp

# Allow agent connections
sudo ufw allow 5555/tcp

# Enable firewall
sudo ufw enable
```

## SSL/TLS
- Replace self-signed certificates with proper certificates
- Use Let's Encrypt for public deployments
- Regularly rotate certificates

## Monitoring
- Monitor logs regularly: `tail -f logs/server.log`
- Set up log rotation
- Monitor system resources

## Regular Maintenance
- Update system packages regularly
- Backup data periodically
- Review access logs
- Rotate SSL certificates
EOF

    # Set restrictive permissions
    chmod 600 "${DIST_DIR}/config/.env" 2>/dev/null || true
    chmod 700 "${DIST_DIR}/ssl" 2>/dev/null || true
    
    log_success "Security configuration completed"
}

create_documentation() {
    log_step "📚 Creating documentation..."
    
    # Main README
    cat > "${DIST_DIR}/README.md" << EOF
# Ikunc2 C2 Server ${BUILD_VERSION}

Advanced Command & Control Platform with Hybrid Storage Architecture

## 🚀 Quick Start

\`\`\`bash
# Start the server
./start.sh

# Check status
./status.sh

# Stop the server
./stop.sh
\`\`\`

## 🌐 Access

- **Web Interface**: http://localhost:8080
- **Default Credentials**: admin/admin (⚠️ Change immediately!)
- **TCP Port**: 5555

## 📊 Storage Architecture

- **User Database**: SQLite (data/users.db)
- **Agent Information**: JSON (data/agents.json)
- **Command History**: JSON (data/command_history.json)

## 🔧 Configuration

Configuration files are located in the \`config/\` directory:
- \`server.toml\` - Main server configuration
- \`.env\` - Environment variables
- \`log4rs.yaml\` - Logging configuration

## 🛠️ Utilities

- \`scripts/generate_ssl.sh\` - Generate SSL certificates
- \`scripts/backup.sh\` - Create data backup
- \`scripts/update.sh\` - Update server

## 🔒 Security

See \`docs/SECURITY.md\` for detailed security guidelines.

## 📋 System Service

Install as a system service:

\`\`\`bash
sudo cp ikunc2.service /etc/systemd/system/
sudo systemctl enable ikunc2
sudo systemctl start ikunc2
sudo systemctl status ikunc2
\`\`\`

## 📝 Logs

Logs are stored in \`logs/server.log\` and rotated automatically.

## 🆘 Troubleshooting

1. Check logs: \`tail -f logs/server.log\`
2. Verify ports: \`./status.sh\`
3. Check permissions: \`ls -la data/\`

Built on: ${BUILD_DATE}
Version: ${BUILD_VERSION}
EOF

    # Installation guide
    cat > "${DIST_DIR}/docs/INSTALLATION.md" << 'EOF'
# Installation Guide

## System Requirements

- Ubuntu 18.04+ or Debian 10+
- 512MB RAM minimum (1GB recommended)
- 500MB disk space
- Rust 1.70.0+ (auto-installed)

## Installation Steps

1. Extract the archive
2. Run the startup script: `./start.sh`
3. Access web interface: http://localhost:8080
4. Login with admin/admin
5. Change default credentials immediately

## Production Deployment

For production deployment:

1. Generate proper SSL certificates
2. Configure firewall rules
3. Set up monitoring
4. Install as system service
5. Configure backup schedule

See SECURITY.md for detailed security guidelines.
EOF

    log_success "Documentation created"
}

# ===============================================================================
# Main Execution
# ===============================================================================

main() {
    print_banner
    
    log_info "Starting enhanced build process..."
    log_info "Build directory: ${SCRIPT_DIR}"
    log_info "Log file: ${LOG_FILE}"
    
    # Execute build steps
    check_system_requirements
    install_dependencies
    check_and_install_rust
    clean_previous_builds
    build_project
    create_deployment_structure
    create_configuration_files
    create_startup_scripts
    create_utility_scripts
    create_systemd_service
    setup_security
    create_documentation
    
    # Generate SSL certificates
    log_step "🔐 Generating SSL certificates..."
    cd "${DIST_DIR}"
    ./scripts/generate_ssl.sh
    cd "${SCRIPT_DIR}"
    
    # Final steps
    log_step "🎯 Finalizing deployment..."
    
    # Create archive
    local archive_name="${PROJECT_NAME}-${BUILD_VERSION}-ubuntu-$(date +%Y%m%d).tar.gz"
    tar -czf "${archive_name}" -C "${DIST_DIR}" .
    log_info "Archive created: ${archive_name}"
    
    # Summary
    echo
    echo -e "${GREEN}════════════════════════════════════════════════════════════════════════${NC}"
    echo -e "${GREEN}                    🎉 DEPLOYMENT SUCCESSFUL! 🎉${NC}"
    echo -e "${GREEN}════════════════════════════════════════════════════════════════════════${NC}"
    echo
    echo -e "${CYAN}📁 Deployment Information:${NC}"
    echo -e "  📂 Directory: ${DIST_DIR}"
    echo -e "  📦 Archive: ${archive_name}"
    echo -e "  📋 Log: ${LOG_FILE}"
    echo
    echo -e "${CYAN}🔧 Server Configuration:${NC}"
    echo -e "  🌐 Web Interface: http://localhost:8080"
    echo -e "  🔌 TCP Listener: 0.0.0.0:5555"
    echo -e "  🔑 Default Login: admin/admin"
    echo -e "  🔐 TLS: Enabled (self-signed certificates)"
    echo
    echo -e "${CYAN}💾 Storage Architecture:${NC}"
    echo -e "  🗄️  User Database: SQLite (data/users.db)"
    echo -e "  📄 Agent Information: JSON (data/agents.json)"
    echo -e "  📝 Command History: JSON (data/command_history.json)"
    echo
    echo -e "${CYAN}🚀 Quick Start:${NC}"
    echo -e "  cd ${DIST_DIR}"
    echo -e "  ./start.sh"
    echo
    echo -e "${CYAN}📊 System Service:${NC}"
    echo -e "  sudo cp ${DIST_DIR}/ikunc2.service /etc/systemd/system/"
    echo -e "  sudo systemctl enable ikunc2"
    echo -e "  sudo systemctl start ikunc2"
    echo
    echo -e "${YELLOW}⚠️  Security Notice:${NC}"
    echo -e "  - Change default admin password immediately"
    echo -e "  - Configure firewall rules for production"
    echo -e "  - Replace self-signed certificates for public deployment"
    echo -e "  - See docs/SECURITY.md for detailed guidelines"
    echo
    echo -e "${GREEN}Build completed at: $(date)${NC}"
    echo -e "${GREEN}Total build time: $SECONDS seconds${NC}"
}

# Execute main function
main "$@" 