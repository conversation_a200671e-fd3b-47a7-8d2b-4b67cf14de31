# Quick start script for Ikunc2 C2 Server
Write-Host "🚀 Starting Ikunc2 C2 Server..." -ForegroundColor Green

# Create necessary directories
$directories = @("data", "certs", "logs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "📁 Created directory: $dir" -ForegroundColor Yellow
    }
}

# Check if binary exists
if (!(Test-Path ".\target\release\c2-gui.exe")) {
    Write-Host "❌ Server binary not found. Please run setup_windows.ps1 first to build the project." -ForegroundColor Red
    exit 1
}

Write-Host "🌐 Starting server..." -ForegroundColor Cyan
Write-Host "Web Interface: http://localhost:8080" -ForegroundColor Yellow
Write-Host "Login: admin / admin" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host ""

# Start the server
& .\target\release\c2-gui.exe 