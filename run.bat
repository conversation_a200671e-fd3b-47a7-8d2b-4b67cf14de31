@echo off
echo 🚀 Starting Ikunc2 C2 Server...

REM Create necessary directories
if not exist "data" mkdir data
if not exist "certs" mkdir certs
if not exist "logs" mkdir logs

REM Check if binary exists
if not exist "target\release\c2-gui.exe" (
    echo ❌ Server binary not found. Please build the project first:
    echo    cargo build --release
    pause
    exit /b 1
)

echo 🌐 Web Interface: http://localhost:8080
echo 🔐 Login: admin / admin
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server
target\release\c2-gui.exe 