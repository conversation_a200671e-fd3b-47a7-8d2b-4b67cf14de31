# Ikunc2 C2 Platform - Enhanced Version

## 概述

Ikunc2 C2是一个功能强大的命令与控制（Command & Control）平台，使用Rust语言开发。本版本包含了完善的终端管理平台、agent生成平台和agent断开通信功能。

## 🚀 主要功能

### 1. 终端管理平台
- **多会话支持**: 支持为每个agent创建多个终端会话
- **实时命令执行**: 实时执行Shell、PowerShell、文件操作等命令
- **会话管理**: 创建、关闭、监控终端会话
- **命令历史**: 记录和查看命令执行历史
- **快速命令**: 预设常用命令快速执行
- **输出管理**: 实时查看和管理命令输出

### 2. Agent生成平台
- **多平台支持**: 支持Windows、Linux、macOS
- **多架构支持**: x64、x86、ARM64
- **代码混淆**: 高级代码混淆技术
- **持久化**: 自动安装持久化机制
- **反调试**: 内置反调试和沙箱检测
- **实时构建**: 支持实时构建进度跟踪
- **自定义功能**: 支持添加自定义功能模块

### 3. Agent断开通信功能
- **优雅断开**: 支持优雅断开agent连接
- **强制断开**: 支持强制断开agent连接
- **状态监控**: 实时监控agent连接状态
- **自动重连**: agent支持自动重连机制
- **连接日志**: 详细的连接和断开日志

## 📋 系统要求

- Rust 1.70+
- Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.15+
- 至少2GB RAM
- 网络连接

## 🛠️ 安装和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd c2-gui
```

### 2. 编译项目
```bash
# 编译主服务器
cargo build --release

# 编译agent
cd agent
cargo build --release
```

### 3. 运行服务器
```bash
# Windows
run.bat

# Linux/macOS
./run.sh
```

### 4. 访问Web界面
打开浏览器访问: `http://localhost:8080`
默认登录凭据: `admin/admin`

## 🔧 配置说明

### 服务器配置
编辑 `config.toml` 文件：
```toml
[server]
http_addr = "127.0.0.1:8080"
tcp_addr = "127.0.0.1:5555"

[logging]
level = "info"
```

### Agent配置
在 `agent/src/main.rs` 中修改：
```rust
const SERVER_IP: &str = "127.0.0.1";
const SERVER_PORT: &str = "8080";
const AGENT_ID: &str = "agent-001";
const HEARTBEAT_INTERVAL: u64 = 30;
```

## 📖 使用指南

### 1. 终端管理

#### 创建终端会话
```bash
# 通过API创建会话
curl -X POST http://localhost:8080/api/terminal/create-session \
  -H "Content-Type: application/json" \
  -d '{"agent_id": "agent-001", "session_type": "shell"}'
```

#### 执行命令
```bash
# 执行Shell命令
curl -X POST http://localhost:8080/api/terminal/execute-command \
  -H "Content-Type: application/json" \
  -d '{"session_id": "session-id", "command": "whoami"}'
```

#### 快速命令
```bash
# 执行快速命令
curl -X POST http://localhost:8080/api/terminal/quick-command \
  -H "Content-Type: application/json" \
  -d '{"session_id": "session-id", "command_type": "whoami"}'
```

### 2. Agent生成

#### 通过Web界面
1. 访问 `http://localhost:8080/builder`
2. 填写配置信息：
   - 目标操作系统
   - 目标架构
   - 服务器IP和端口
   - 协议类型
   - 混淆选项
   - 持久化选项
3. 点击"构建Agent"
4. 下载生成的agent二进制文件

#### 通过API
```bash
curl -X POST http://localhost:8080/api/build-agent-optimized \
  -H "Content-Type: application/json" \
  -d '{
    "target_os": "windows",
    "target_arch": "x64",
    "server_ip": "127.0.0.1",
    "server_port": "8080",
    "protocol": "http",
    "obfuscation": true,
    "persistence": true,
    "stealth_mode": false,
    "custom_features": []
  }'
```

### 3. Agent管理

#### 查看所有Agent
```bash
curl http://localhost:8080/api/clients
```

#### 断开Agent连接
```bash
# 优雅断开
curl -X POST http://localhost:8080/api/disconnect-agent \
  -H "Content-Type: application/json" \
  -d '{"agent_id": "agent-001", "reason": "Maintenance", "force": false}'

# 强制断开
curl -X POST http://localhost:8080/api/disconnect-agent \
  -H "Content-Type: application/json" \
  -d '{"agent_id": "agent-001", "reason": "Security", "force": true}'
```

#### 查看Agent状态
```bash
curl http://localhost:8080/api/agent-status/agent-001
```

## 🔌 API接口

### 终端管理API
- `POST /api/terminal/create-session` - 创建终端会话
- `POST /api/terminal/execute-command` - 执行命令
- `GET /api/terminal/sessions` - 获取所有会话
- `GET /api/terminal/session/:session_id` - 获取会话信息
- `POST /api/terminal/close-session` - 关闭会话
- `GET /api/terminal/output/:session_id` - 获取会话输出
- `POST /api/terminal/quick-command` - 执行快速命令
- `POST /api/terminal/clear-output` - 清除输出
- `GET /api/terminal/statistics` - 获取统计信息

### Agent管理API
- `GET /api/clients` - 获取所有agent
- `POST /api/disconnect-agent` - 断开agent连接
- `GET /api/agent-status/:agent_id` - 获取agent状态
- `GET /api/agent-statistics` - 获取agent统计信息

### 构建API
- `POST /api/build-agent-optimized` - 构建agent
- `GET /api/build-progress/:build_id` - 获取构建进度

## 🛡️ 安全特性

### 代码混淆
- 字符串混淆
- 函数名混淆
- 控制流混淆
- 死代码注入

### 反调试技术
- 调试器检测
- 沙箱环境检测
- 用户交互检测
- 系统资源检测

### 持久化机制
- Windows注册表持久化
- Linux systemd服务持久化
- 自动启动配置

## 📊 监控和日志

### 实时监控
- Agent连接状态
- 命令执行状态
- 系统资源使用
- 网络流量监控

### 日志系统
- 结构化日志
- 多级别日志
- 日志轮转
- 日志导出

## 🔧 开发指南

### 添加新命令类型
1. 在 `src/server.rs` 中添加新的 `CommandType`
2. 在 `src/terminal_manager.rs` 中实现命令处理逻辑
3. 在Web界面中添加相应的UI组件

### 添加新的Agent功能
1. 在 `agent/src/main.rs` 中实现新功能
2. 在 `src/agent_builder.rs` 中添加代码生成逻辑
3. 更新Web界面的构建选项

### 自定义协议
1. 实现新的通信协议
2. 更新agent和服务器代码
3. 添加协议配置选项

## 🐛 故障排除

### 常见问题

#### Agent无法连接
1. 检查服务器IP和端口配置
2. 确认防火墙设置
3. 检查网络连接
4. 查看服务器日志

#### 命令执行失败
1. 检查agent权限
2. 确认命令语法
3. 查看agent日志
4. 检查目标系统兼容性

#### 构建失败
1. 检查Rust工具链
2. 确认依赖项安装
3. 检查目标平台支持
4. 查看构建日志

## 📄 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## ⚠️ 免责声明

本工具仅用于合法的安全测试和教育目的。使用者需要遵守当地法律法规，不得用于非法活动。开发者不承担任何因使用本工具而产生的法律责任。 