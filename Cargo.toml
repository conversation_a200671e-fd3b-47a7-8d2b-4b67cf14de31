[package]
name = "c2-gui"
version = "2.0.0"
edition = "2021"
authors = ["IkunC2 Team"]
description = "Advanced Command & Control Platform with Enhanced Capabilities"
license = "MIT"

[[bin]]
name = "agent"
path = "agent/src/main.rs"

[dependencies]
# Core async runtime
tokio = { version = "1.41", features = ["full"] }
tokio-tungstenite = "0.24"
futures = "0.3"

# Web framework and HTTP
axum = { version = "0.7", features = ["macros", "ws"] }
tower = "0.5"
tower-http = { version = "0.6", features = ["cors", "fs", "trace"] }
tower-sessions = { version = "0.12", features = ["memory-store"] }
tower-cookies = "0.10"
hyper = "1.6"
hyper-util = "0.1"

# TLS and HTTPS support  
hyper-tls = "0.6"
native-tls = "0.2"
tokio-native-tls = "0.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_urlencoded = "0.7"

# Database and storage
sqlx = { version = "0.8", features = ["runtime-tokio-native-tls", "sqlite", "chrono", "uuid"] }
rusqlite = { version = "0.32", features = ["bundled"] }

# Configuration
config = "0.14"
toml = "0.8"

# Cryptography and security
bcrypt = "0.16"
jsonwebtoken = "9.0"
uuid = { version = "1.11", features = ["v4", "serde"] }
base64 = "0.21"
rand = "0.8"
lazy_static = "1.4"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"
color-eyre = "0.6"

# Error handling
thiserror = "2.0"
anyhow = "1.0"

# Utilities
chrono = { version = "0.4", features = ["serde"] }
dashmap = "6.1"
sysinfo = "0.32"
whoami = "1.6"
reqwest = { version = "0.12", features = ["json", "native-tls"] }
tempfile = "3.20.0"

# Development dependencies
[dev-dependencies]
tempfile = "3.20.0"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

