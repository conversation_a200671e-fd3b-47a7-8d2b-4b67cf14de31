# 🛡️ Windows Firewall Setup Guide for Ikunc2 C2 Server

This guide helps you configure Windows Firewall to allow communication between agents and the server on the required ports: **80**, **443**, and **8080**.

## 🚀 Quick Setup (Recommended)

### Option 1: PowerShell Script (Advanced)
```powershell
# Run as Administrator
.\setup_windows_firewall.ps1
```

### Option 2: <PERSON><PERSON> (Simple)
```cmd
# Right-click and "Run as administrator"
setup_firewall.bat
```

### Option 3: Integrated Setup
```powershell
# Run the main setup which includes firewall configuration
.\setup_windows.ps1
```

## 🔧 Manual Configuration

If you prefer to configure the firewall manually or the scripts don't work, follow these steps:

### Step 1: Open Windows Defender Firewall

1. Press `Win + R` and type `wf.msc`
2. Or go to **Control Panel** → **System and Security** → **Windows Defender Firewall** → **Advanced Settings**

### Step 2: Create Inbound Rules

For each port (80, 443, 8080), create an inbound rule:

1. Click **"Inbound Rules"** in the left panel
2. Click **"New Rule..."** in the right panel
3. Select **"Port"** → Click **"Next"**
4. Select **"TCP"** → Enter the port number (e.g., 80) → Click **"Next"**
5. Select **"Allow the connection"** → Click **"Next"**
6. Check all profiles (Domain, Private, Public) → Click **"Next"**
7. Name the rule (e.g., "Ikunc2-C2-Server-Port-80") → Click **"Finish"**

### Step 3: Create Outbound Rules

Repeat the same process for **"Outbound Rules"** for each port.

## 📋 Required Firewall Rules

The following rules need to be created:

### Inbound Rules (Allow connections TO the server)
- **Name**: `Ikunc2-C2-Server-Inbound-80`
  - **Port**: 80 (HTTP)
  - **Protocol**: TCP
  - **Action**: Allow
  - **Profiles**: All

- **Name**: `Ikunc2-C2-Server-Inbound-443`
  - **Port**: 443 (HTTPS)
  - **Protocol**: TCP
  - **Action**: Allow
  - **Profiles**: All

- **Name**: `Ikunc2-C2-Server-Inbound-8080`
  - **Port**: 8080 (HTTP Alternative)
  - **Protocol**: TCP
  - **Action**: Allow
  - **Profiles**: All

### Outbound Rules (Allow connections FROM the server)
- **Name**: `Ikunc2-C2-Server-Outbound-80`
- **Name**: `Ikunc2-C2-Server-Outbound-443`
- **Name**: `Ikunc2-C2-Server-Outbound-8080`

## 🔍 Verification

### Check Rules via PowerShell
```powershell
# List all Ikunc2 firewall rules
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*Ikunc2*"} | Select-Object DisplayName, Direction, Action, Enabled

# Check specific ports
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*80*" -or $_.DisplayName -like "*443*" -or $_.DisplayName -like "*8080*"}
```

### Check Rules via Command Line
```cmd
# Show firewall state
netsh advfirewall show allprofiles state

# List all rules
netsh advfirewall firewall show rule name=all | findstr "Ikunc2"
```

### Test Port Connectivity
```powershell
# Test if ports are listening (run after starting the server)
netstat -an | findstr ":80 "
netstat -an | findstr ":443 "
netstat -an | findstr ":8080 "
```

## 🛠️ Troubleshooting

### Common Issues

1. **"Access Denied" errors**
   - Ensure you're running as Administrator
   - Right-click PowerShell/Command Prompt → "Run as administrator"

2. **Rules created but agents can't connect**
   - Check if Windows Defender is blocking the application
   - Verify the server is actually listening on the configured ports
   - Check if another application is using the ports

3. **Scripts fail to run**
   - Check PowerShell execution policy: `Get-ExecutionPolicy`
   - If restricted, run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Firewall Status Commands
```cmd
# Check firewall status
netsh advfirewall show allprofiles

# Disable firewall (NOT RECOMMENDED for production)
netsh advfirewall set allprofiles state off

# Enable firewall
netsh advfirewall set allprofiles state on
```

## 🗑️ Cleanup (Remove Rules)

### Using PowerShell Script
```powershell
.\setup_windows_firewall.ps1 -Remove
```

### Manual Removal
```cmd
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-80"
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-443"
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Inbound-8080"
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-80"
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-443"
netsh advfirewall firewall delete rule name="Ikunc2-C2-Server-Outbound-8080"
```

## 🔒 Security Considerations

1. **Port Selection**: Only ports 80, 443, and 8080 are allowed for security reasons
2. **Encrypted Communication**: Use HTTPS (port 443) when possible
3. **Monitoring**: Regularly monitor network connections and firewall logs
4. **Authentication**: Ensure proper authentication is configured in the C2 server
5. **Network Segmentation**: Consider restricting access to specific IP ranges if possible

## 🌐 Cloud/VPS Considerations

If you're using a cloud VPS (Azure, AWS, GCP), you may also need to configure:

1. **Security Groups** (AWS) or **Network Security Groups** (Azure)
2. **Cloud provider firewall rules**
3. **Load balancer configurations** (if applicable)

### Example for Azure
```powershell
# Add security group rules for Azure VM
az network nsg rule create --resource-group myResourceGroup --nsg-name myNSG --name Allow-C2-Ports --protocol Tcp --priority 1000 --destination-port-ranges 80 443 8080 --access Allow
```

## 📞 Support

If you encounter issues:
1. Check the server logs for specific error messages
2. Verify the firewall rules are correctly applied
3. Test connectivity using tools like `telnet` or `Test-NetConnection`
4. Ensure no other security software is blocking the connections

---

**⚠️ Important**: Always test your firewall configuration in a controlled environment before deploying to production! 