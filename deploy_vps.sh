#!/bin/bash

# ===============================================================================
# Ikunc2 C2 Server - VPS Deployment Script
# Automated deployment script for VPS environments
# ===============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ikunc2-c2"
SERVICE_NAME="ikunc2-c2"
INSTALL_DIR="/opt/ikunc2-c2"
SERVICE_USER="ikunc2"
LOG_DIR="/var/log/ikunc2-c2"
DATA_DIR="/opt/ikunc2-c2/data"

echo -e "${BLUE}🚀 Ikunc2 C2 Server - VPS Deployment Script${NC}"
echo "=================================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}❌ This script should not be run as root${NC}"
   echo "Please run as a regular user with sudo privileges"
   exit 1
fi

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Update system packages
print_info "Updating system packages..."
sudo apt update && sudo apt upgrade -y
print_status "System packages updated"

# Install required dependencies
print_info "Installing required dependencies..."
sudo apt install -y \
    build-essential \
    curl \
    git \
    pkg-config \
    libssl-dev \
    ca-certificates \
    systemd \
    ufw \
    fail2ban \
    nginx \
    supervisor

print_status "Dependencies installed"

# Install Rust if not already installed
if ! command -v cargo &> /dev/null; then
    print_info "Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    print_status "Rust installed"
else
    print_status "Rust already installed"
fi

# Create service user
print_info "Creating service user..."
sudo useradd -r -s /bin/false -d $INSTALL_DIR $SERVICE_USER 2>/dev/null || true
print_status "Service user created"

# Create directories
print_info "Creating directories..."
sudo mkdir -p $INSTALL_DIR
sudo mkdir -p $LOG_DIR
sudo mkdir -p $DATA_DIR
sudo chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
sudo chown -R $SERVICE_USER:$SERVICE_USER $LOG_DIR
sudo chown -R $SERVICE_USER:$SERVICE_USER $DATA_DIR
print_status "Directories created"

# Clone or copy project
if [ -d ".git" ]; then
    print_info "Copying project files..."
    sudo cp -r . $INSTALL_DIR/
    sudo chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR
else
    print_info "Cloning project from repository..."
    sudo -u $SERVICE_USER git clone https://github.com/your-repo/ikunc2-c2.git $INSTALL_DIR || {
        print_error "Failed to clone repository"
        exit 1
    }
fi
print_status "Project files copied"

# Build the project
print_info "Building C2 server..."
cd $INSTALL_DIR
sudo -u $SERVICE_USER cargo build --release
print_status "C2 server built successfully"

# Create systemd service file
print_info "Creating systemd service..."
sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<EOF
[Unit]
Description=Ikunc2 C2 Server
After=network.target
Wants=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/target/release/c2-gui
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DATA_DIR $LOG_DIR

# Environment variables
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable $SERVICE_NAME
print_status "Systemd service created and enabled"

# Create Nginx configuration
print_info "Configuring Nginx..."
sudo tee /etc/nginx/sites-available/$SERVICE_NAME > /dev/null <<EOF
server {
    listen 80;
    server_name _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Logging
    access_log /var/log/nginx/$SERVICE_NAME.access.log;
    error_log /var/log/nginx/$SERVICE_NAME.error.log;
}
EOF

# Enable Nginx site
sudo ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx
print_status "Nginx configured"

# Configure firewall
print_info "Configuring firewall..."
sudo ufw --force enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5555/tcp  # TCP listener port
print_status "Firewall configured"

# Configure fail2ban
print_info "Configuring fail2ban..."
sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3
EOF

sudo systemctl restart fail2ban
print_status "Fail2ban configured"

# Create log rotation
print_info "Configuring log rotation..."
sudo tee /etc/logrotate.d/$SERVICE_NAME > /dev/null <<EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_USER
    postrotate
        systemctl reload $SERVICE_NAME
    endscript
}
EOF
print_status "Log rotation configured"

# Create monitoring script
print_info "Creating monitoring script..."
sudo tee $INSTALL_DIR/monitor.sh > /dev/null <<EOF
#!/bin/bash
# Monitoring script for Ikunc2 C2 Server

LOG_FILE="$LOG_DIR/monitor.log"
SERVICE_NAME="$SERVICE_NAME"

echo "\$(date): Starting monitoring check" >> \$LOG_FILE

# Check if service is running
if ! systemctl is-active --quiet \$SERVICE_NAME; then
    echo "\$(date): Service is down, attempting restart" >> \$LOG_FILE
    systemctl restart \$SERVICE_NAME
    sleep 5
    
    if systemctl is-active --quiet \$SERVICE_NAME; then
        echo "\$(date): Service restarted successfully" >> \$LOG_FILE
    else
        echo "\$(date): Failed to restart service" >> \$LOG_FILE
    fi
else
    echo "\$(date): Service is running normally" >> \$LOG_FILE
fi

# Check disk space
DISK_USAGE=\$(df / | tail -1 | awk '{print \$5}' | sed 's/%//')
if [ \$DISK_USAGE -gt 80 ]; then
    echo "\$(date): Warning: Disk usage is \${DISK_USAGE}%" >> \$LOG_FILE
fi

# Check memory usage
MEM_USAGE=\$(free | grep Mem | awk '{printf("%.0f", \$3/\$2 * 100.0)}')
if [ \$MEM_USAGE -gt 80 ]; then
    echo "\$(date): Warning: Memory usage is \${MEM_USAGE}%" >> \$LOG_FILE
fi
EOF

sudo chmod +x $INSTALL_DIR/monitor.sh
sudo chown $SERVICE_USER:$SERVICE_USER $INSTALL_DIR/monitor.sh

# Add monitoring to crontab
(sudo crontab -l 2>/dev/null; echo "*/5 * * * * $INSTALL_DIR/monitor.sh") | sudo crontab -
print_status "Monitoring script created"

# Create backup script
print_info "Creating backup script..."
sudo tee $INSTALL_DIR/backup.sh > /dev/null <<EOF
#!/bin/bash
# Backup script for Ikunc2 C2 Server

BACKUP_DIR="/opt/backups/ikunc2-c2"
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ikunc2-c2_\$DATE.tar.gz"

mkdir -p \$BACKUP_DIR

# Create backup
tar -czf \$BACKUP_DIR/\$BACKUP_FILE \\
    --exclude='target' \\
    --exclude='*.log' \\
    --exclude='data/agents.json' \\
    --exclude='data/command_history.json' \\
    $INSTALL_DIR

# Keep only last 7 backups
find \$BACKUP_DIR -name "ikunc2-c2_*.tar.gz" -mtime +7 -delete

echo "Backup created: \$BACKUP_FILE"
EOF

sudo chmod +x $INSTALL_DIR/backup.sh
sudo chown $SERVICE_USER:$SERVICE_USER $INSTALL_DIR/backup.sh

# Add backup to crontab (daily at 2 AM)
(sudo crontab -l 2>/dev/null; echo "0 2 * * * $INSTALL_DIR/backup.sh") | sudo crontab -
print_status "Backup script created"

# Start the service
print_info "Starting C2 server..."
sudo systemctl start $SERVICE_NAME
sleep 3

# Check if service started successfully
if systemctl is-active --quiet $SERVICE_NAME; then
    print_status "C2 server started successfully"
else
    print_error "Failed to start C2 server"
    sudo systemctl status $SERVICE_NAME
    exit 1
fi

# Display final information
echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo "=================================================="
echo -e "${BLUE}Service Information:${NC}"
echo "  Service Name: $SERVICE_NAME"
echo "  Install Directory: $INSTALL_DIR"
echo "  Data Directory: $DATA_DIR"
echo "  Log Directory: $LOG_DIR"
echo ""
echo -e "${BLUE}Access Information:${NC}"
echo "  Web Interface: http://$(curl -s ifconfig.me)"
echo "  Default Login: admin / admin"
echo ""
echo -e "${BLUE}Management Commands:${NC}"
echo "  Check Status: sudo systemctl status $SERVICE_NAME"
echo "  Start Service: sudo systemctl start $SERVICE_NAME"
echo "  Stop Service: sudo systemctl stop $SERVICE_NAME"
echo "  Restart Service: sudo systemctl restart $SERVICE_NAME"
echo "  View Logs: sudo journalctl -u $SERVICE_NAME -f"
echo ""
echo -e "${BLUE}Security Information:${NC}"
echo "  Firewall: UFW enabled with SSH, HTTP, HTTPS, and TCP port 5555 allowed"
echo "  Fail2ban: Configured for SSH and Nginx protection"
echo "  Log Rotation: Configured for daily rotation with 30-day retention"
echo "  Monitoring: Automatic health checks every 5 minutes"
echo "  Backup: Daily backups at 2 AM with 7-day retention"
echo ""
echo -e "${YELLOW}⚠️  Important Security Notes:${NC}"
echo "  1. Change default admin password immediately"
echo "  2. Configure SSL/TLS certificates for HTTPS"
echo "  3. Regularly update the system and application"
echo "  4. Monitor logs for suspicious activity"
echo "  5. Consider using a VPN for secure access"
echo ""
print_status "Deployment script completed!" 