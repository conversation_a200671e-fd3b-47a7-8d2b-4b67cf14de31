@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Ikunc2 C2 Server - Windows 混合存储架构
echo SQLite 数据库用于用户账号，JSON 文件用于 Agent 信息
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置变量
set PROJECT_DIR=%~dp0
set DIST_DIR=%PROJECT_DIR%dist
set TEMP_DIR=C:\temp
set RUSTUP_URL=https://win.rustup.rs/x86_64
set RUSTUP_INSTALLER=%TEMP_DIR%\rustup-init.exe

:: 创建临时目录
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

:: 1. 下载并安装 Rust
echo ===== 下载并安装 Rust =====
if not exist "%RUSTUP_INSTALLER%" (
    echo 正在下载 Rust 安装程序...
    powershell -Command "Invoke-WebRequest -Uri '%RUSTUP_URL%' -OutFile '%RUSTUP_INSTALLER%'"
    if %errorLevel% neq 0 (
        echo Rust 下载失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 正在安装 Rust...
%RUSTUP_INSTALLER% -y --quiet
if %errorLevel% neq 0 (
    echo Rust 安装失败
    pause
    exit /b 1
)

:: 刷新环境变量
call refreshenv.cmd 2>nul || (
    echo 正在刷新环境变量...
    set PATH=%USERPROFILE%\.cargo\bin;%PATH%
)

:: 2. 编译项目
echo ===== 编译 Rust 项目 =====
cd /d "%PROJECT_DIR%"
cargo build --release
if %errorLevel% neq 0 (
    echo 项目编译失败
    pause
    exit /b 1
)

:: 3. 创建部署目录
echo ===== 创建部署目录 =====
if not exist "%DIST_DIR%" mkdir "%DIST_DIR%"
if not exist "%DIST_DIR%\data" mkdir "%DIST_DIR%\data"
if not exist "%DIST_DIR%\logs" mkdir "%DIST_DIR%\logs"

:: 4. 拷贝可执行文件
echo ===== 拷贝可执行文件 =====
copy "%PROJECT_DIR%target\release\c2-gui.exe" "%DIST_DIR%\" >nul 2>&1
if %errorLevel% neq 0 (
    copy "%PROJECT_DIR%target\release\c2-gui" "%DIST_DIR%\" >nul 2>&1
)

:: 5. 生成启动脚本
echo ===== 生成启动脚本 =====
echo @echo off > "%DIST_DIR%\start.bat"
echo cd /d "%%~dp0" >> "%DIST_DIR%\start.bat"
echo echo 正在启动 Ikunc2 C2 Server - 混合存储版... >> "%DIST_DIR%\start.bat"
echo echo. >> "%DIST_DIR%\start.bat"
echo echo 📊 存储架构: >> "%DIST_DIR%\start.bat"
echo echo   🗄️  用户数据库: SQLite (data\users.db) >> "%DIST_DIR%\start.bat"
echo echo   📄 Agent 信息: JSON (data\agents.json) >> "%DIST_DIR%\start.bat"
echo echo   📄 命令历史: JSON (data\command_history.json) >> "%DIST_DIR%\start.bat"
echo echo. >> "%DIST_DIR%\start.bat"
echo echo 🔑 默认登录: admin/admin >> "%DIST_DIR%\start.bat"
echo echo 🌐 Web 界面: http://localhost:8080 >> "%DIST_DIR%\start.bat"
echo echo 🔌 TCP 监听: 0.0.0.0:5555 >> "%DIST_DIR%\start.bat"
echo echo. >> "%DIST_DIR%\start.bat"
echo c2-gui.exe >> "%DIST_DIR%\start.bat"
echo pause >> "%DIST_DIR%\start.bat"

:: 6. 生成服务安装脚本
echo ===== 生成服务安装脚本 =====
echo @echo off > "%DIST_DIR%\install_service.bat"
echo echo 正在安装 Ikunc2 C2 Server 为 Windows 服务... >> "%DIST_DIR%\install_service.bat"
echo sc create "Ikunc2C2Server" binPath= "%%~dp0c2-gui.exe" start= auto >> "%DIST_DIR%\install_service.bat"
echo sc description "Ikunc2C2Server" "Ikunc2 C2 Server - 混合存储架构版本" >> "%DIST_DIR%\install_service.bat"
echo sc start "Ikunc2C2Server" >> "%DIST_DIR%\install_service.bat"
echo echo 服务安装完成！ >> "%DIST_DIR%\install_service.bat"
echo pause >> "%DIST_DIR%\install_service.bat"

:: 7. 生成服务卸载脚本
echo ===== 生成服务卸载脚本 =====
echo @echo off > "%DIST_DIR%\uninstall_service.bat"
echo echo 正在卸载 Ikunc2 C2 Server 服务... >> "%DIST_DIR%\uninstall_service.bat"
echo sc stop "Ikunc2C2Server" >> "%DIST_DIR%\uninstall_service.bat"
echo sc delete "Ikunc2C2Server" >> "%DIST_DIR%\uninstall_service.bat"
echo echo 服务卸载完成！ >> "%DIST_DIR%\uninstall_service.bat"
echo pause >> "%DIST_DIR%\uninstall_service.bat"

:: 8. 清理临时文件
echo ===== 清理临时文件 =====
if exist "%RUSTUP_INSTALLER%" del "%RUSTUP_INSTALLER%"

echo ========================================
echo           部署完成！
echo ========================================
echo.
echo 🎯 部署信息:
echo 📁 文件位置: %DIST_DIR%
echo 📊 存储架构:
echo   🗄️  用户数据库: SQLite (自动创建在 data\users.db)
echo   📄 Agent 信息: JSON (自动创建在 data\agents.json)
echo   📄 命令历史: JSON (自动创建在 data\command_history.json)
echo 🔑 默认登录: admin/admin
echo 🌐 Web 界面: http://localhost:8080
echo 🔌 TCP 监听: 0.0.0.0:5555
echo.
echo 🚀 启动方式:
echo 1. 直接启动: 双击 start.bat
echo 2. 安装为系统服务: 右键管理员运行 install_service.bat
echo 3. 卸载服务: 右键管理员运行 uninstall_service.bat
echo.
echo ✨ 混合存储特性:
echo - 账号信息使用 SQLite 数据库存储
echo - Agent 信息使用 JSON 文件存储
echo - 命令历史使用 JSON 文件存储
echo - 会话管理使用 SQLite 数据库
echo - 自动数据库表创建和初始化
echo - 自动 JSON 文件管理和持久化
echo - 定期清理过期数据
echo - 跨平台兼容
echo.
pause 