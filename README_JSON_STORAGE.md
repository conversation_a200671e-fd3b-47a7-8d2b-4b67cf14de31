# Ikunc2 C2 Server - JSON 存储架构

## 概述

Ikunc2 C2 Server 已从 MySQL 数据库迁移到 JSON 文件存储，简化了部署和维护流程。

## 主要变更

### 1. 存储架构
- **之前**: MySQL 数据库存储
- **现在**: JSON 文件存储
- **优势**: 
  - 无需安装和配置数据库
  - 数据文件直接可读
  - 简化部署流程
  - 跨平台兼容性更好

### 2. 数据文件结构
```
data/
└── json/
    ├── users.json          # 用户信息
    ├── agents.json         # Agent 信息
    ├── command_history.json # 命令历史
    └── sessions.json       # 会话数据
```

### 3. 配置变更
- 移除了 `IKUNC2_DB_URL` 环境变量
- 新增 `IKUNC2_DATA_DIR` 环境变量（默认: `data/json`）
- 新增 `IKUNC2_AUTO_SAVE_INTERVAL` 环境变量（默认: 300秒）

## 部署方式

### Ubuntu/Linux 部署
```bash
# 运行部署脚本
bash build_server_ubuntu.sh
```

### Windows 部署
```cmd
# 以管理员身份运行
build_server_windows.bat
```

## 数据格式

### 用户数据 (users.json)
```json
{
  "user_id": {
    "id": "uuid",
    "username": "admin",
    "password_hash": "bcrypt_hash",
    "is_active": true,
    "last_login": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### Agent 数据 (agents.json)
```json
{
  "agent_id": {
    "id": "uuid",
    "pc_name": "DESKTOP-ABC123",
    "ip_address": "*************",
    "username": "user",
    "process_name": "agent.exe",
    "os_type": "Windows",
    "is_connected": true,
    "first_seen": "2024-01-01T00:00:00Z",
    "last_seen": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 命令历史 (command_history.json)
```json
{
  "history_id": {
    "id": "uuid",
    "client_id": "agent_id",
    "command": "dir",
    "output": "command output",
    "success": true,
    "executed_at": "2024-01-01T00:00:00Z"
  }
}
```

### 会话数据 (sessions.json)
```json
{
  "session_id": {
    "id": "uuid",
    "user_id": "user_id",
    "data": {
      "user_id": "user_id",
      "username": "admin"
    },
    "expiry": "2024-01-01T00:00:00Z"
  }
}
```

## 功能特性

### 1. 自动保存
- 数据自动保存到 JSON 文件
- 可配置保存间隔
- 支持手动保存

### 2. 数据持久化
- 程序重启后数据保持
- 支持数据备份和恢复
- 文件格式易于阅读和编辑

### 3. 用户认证
- 支持 bcrypt 密码哈希
- 默认管理员账户: admin/admin
- 支持用户状态管理

### 4. Agent 管理
- 自动记录 Agent 连接信息
- 支持 Agent 状态跟踪
- 命令历史记录

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `IKUNC2_DATA_DIR` | `data/json` | JSON 数据存储目录 |
| `IKUNC2_AUTO_SAVE_INTERVAL` | `300` | 自动保存间隔（秒） |

## 迁移说明

### 从 MySQL 迁移
1. 停止旧版本服务
2. 备份 MySQL 数据（可选）
3. 部署新版本
4. 新版本会自动创建 JSON 存储结构

### 数据备份
```bash
# 备份 JSON 数据
cp -r data/json backup/
```

## 故障排除

### 1. 数据文件损坏
```bash
# 删除损坏的文件，系统会重新创建
rm data/json/users.json
```

### 2. 权限问题
```bash
# 确保数据目录有写权限
chmod -R 755 data/json/
```

### 3. 存储空间不足
- 检查磁盘空间
- 清理日志文件
- 考虑压缩历史数据

## 性能考虑

- JSON 存储适合中小规模部署
- 大量数据时考虑定期清理历史记录
- 可以配置自动保存间隔来平衡性能和安全性

## 安全建议

1. 定期备份 JSON 数据文件
2. 设置适当的文件权限
3. 考虑加密敏感数据
4. 定期清理过期会话 