# Ikunc2 C2 系统

一个现代化的命令与控制（C2）系统，使用 Rust 构建，具有 Web 界面和 Axum 服务器。

## 功能特性

- **现代化架构**: 基于 Axum Web 框架和异步编程
- **跨平台支持**: 支持 Windows、Linux 和 macOS
- **实时通信**: 基于 TCP 的代理通信和 HTTP API
- **文件管理**: 浏览、下载和管理目标系统文件
- **命令执行**: 在远程系统上执行 Shell 命令
- **代理构建器**: 为不同目标编译自定义代理
- **现代化界面**: 基于 Web 的 GUI，采用 Havoc 主题风格

## 系统架构

### 组件

1. **服务器** (`src/server.rs`): 基于 Axum 的 HTTP API 服务器
2. **TCP 监听器** (`src/tcp_listener.rs`): 处理代理连接
3. **Web 界面** (`src/web.rs`): 基于 Web 的管理界面
4. **代理构建器** (`src/agent_builder.rs`): 生成跨平台代理
5. **数据库** (`src/database.rs`): 会话和用户管理

### 通信流程

```
Web 客户端 <--HTTP--> 服务器 <--TCP--> 代理
```

## 构建说明

### 系统要求

- Rust 1.70+ 和 Cargo
- 用于代理编译的目标特定工具链

### 构建命令

```bash
# 构建主应用程序（服务器 + Web 界面）
cargo build --release

# 构建代理
cargo run -- agent
```

## 使用方法

### 启动服务器（VPS）

```bash
# 仅运行服务器
cargo run --release

# 或使用自定义日志级别
RUST_LOG=debug cargo run --release
```

服务器将启动：
- Web 界面: `http://0.0.0.0:8080`
- TCP 监听器: `0.0.0.0:5555`

### 访问 Web 界面

在浏览器中访问 `http://your-vps-ip:8080`

默认登录凭据：
- 用户名: `admin`
- 密码: `admin`

### 部署代理

1. **构建代理**: 使用 Web 界面中的构建器标签页
2. **配置**: 设置目标 IP、端口和平台
3. **编译**: 生成平台特定的代理
4. **部署**: 传输到目标系统并执行

### 代理环境变量

```bash
# 设置代理连接详情
export AGENT_IP=your-vps-ip
export AGENT_PORT=5555

# 运行代理
./agent
```

## API 端点

### HTTP API（服务器）

- `GET /` - Web 界面主页
- `GET /login` - 登录页面
- `POST /login` - 用户登录
- `GET /dashboard` - 控制台仪表板
- `GET /agents` - 代理管理页面
- `GET /builder` - 代理构建页面
- `POST /builder` - 构建新代理
- `GET /api/clients` - 获取连接的代理列表
- `POST /api/command` - 向代理发送命令
- `GET /api/command_output` - 获取命令输出
- `POST /api/file_operation` - 执行文件操作
- `GET /api/file_list` - 获取文件列表
- `GET /api/folder_path` - 获取当前文件夹路径
- `GET /health` - 健康检查

### TCP 协议（代理通信）

代理使用自定义 TCP 协议和 JSON 消息进行通信：

```json
{
  "agent_info": {
    "id": "uuid",
    "hostname": "目标主机",
    "username": "用户",
    "process_name": "agent.exe",
    "ip": "*************",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

## 系统信息收集

代理会自动收集以下系统信息：

- **主机名**: 目标系统的主机名
- **用户名**: 当前运行用户
- **进程名**: 代理进程名称
- **IP 地址**: 目标系统 IP
- **主机 ID**: 唯一标识符
- **平台信息**: 操作系统类型和版本

## 安全注意事项

⚠️ **警告**: 这是一个安全测试工具。仅在您拥有或获得明确测试许可的系统上使用。

- 实施适当的身份验证
- 在生产环境中使用 TLS/SSL
- 限制网络访问
- 监控未经授权的使用

## 开发

### 项目结构

```
c2-gui/
├── src/
│   ├── main.rs          # 应用程序入口点
│   ├── server.rs        # Axum HTTP 服务器
│   ├── tcp_listener.rs  # TCP 代理处理器
│   ├── web.rs           # Web 界面
│   ├── agent_builder.rs # 代理构建器
│   └── database.rs      # 数据库管理
├── agent/
│   ├── src/main.rs      # 代理代码
│   └── Cargo.toml       # 代理依赖
└── Cargo.toml           # 主依赖
```

### 添加功能

1. **新命令**: 在代理的 `handle_command()` 中添加
2. **新 API 端点**: 在 `server.rs` 中添加路由
3. **UI 更改**: 修改 `web.rs`

### 测试

```bash
# 运行测试
cargo test

# 使用日志运行
RUST_LOG=debug cargo test
```

## 故障排除

### 常见问题

1. **代理无法连接**: 检查防火墙和网络连接
2. **Web 界面无法访问**: 验证服务器地址和端口
3. **命令不工作**: 检查代理权限和目标操作系统

### 日志记录

启用详细日志记录：

```bash
RUST_LOG=debug cargo run --release
RUST_LOG=debug ./agent
```

## 许可证

本项目仅供教育和授权安全测试使用。

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 进行更改
4. 添加测试
5. 提交拉取请求

## 免责声明

本软件仅供教育和授权安全测试使用。用户有责任确保在使用此工具之前获得适当的授权。

## 部署到 VPS

### Ubuntu 部署步骤

1. **安装 Rust**:
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

2. **克隆项目**:
```bash
git clone <repository-url>
cd c2-gui
```

3. **构建项目**:
```bash
cargo build --release
```

4. **配置防火墙**:
```bash
sudo ufw allow 8080
sudo ufw allow 5555
```

5. **运行服务器**:
```bash
./target/release/c2-gui
```

6. **设置系统服务** (可选):
```bash
sudo nano /etc/systemd/system/ikunc2.service
```

服务文件内容：
```ini
[Unit]
Description=Ikunc2 C2 Server
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/path/to/c2-gui
ExecStart=/path/to/c2-gui/target/release/c2-gui
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable ikunc2
sudo systemctl start ikunc2
```

# Ikunc2 C2 GUI 使用教程

## 1. 项目简介

Ikunc2 是一个基于 Rust + Axum 的 C2（命令与控制）系统，支持 Web 界面管理、代理生成、会话管理等功能。界面风格参考 Havoc，支持在 Ubuntu VPS 上部署服务端，客户端通过浏览器访问 Web GUI。

## 2. 环境准备

- 推荐系统：Ubuntu 20.04/22.04 VPS
- 依赖：Rust 1.70+、SQLite3

### 安装 Rust
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env
```

### 安装 SQLite3
```bash
sudo apt update
sudo apt install sqlite3 libsqlite3-dev
```

## 3. 服务端部署

1. 上传项目到 VPS
2. 进入项目目录，编译并运行：
```bash
cd c2-gui
cargo build --release
./target/release/c2-gui
```
3. 默认监听端口：
   - Web 界面：http://服务器IP:8080
   - 代理连接：5555 端口

## 4. Web 界面使用

1. 浏览器访问 `http://服务器IP:8080`
2. 默认登录账号/密码：`admin` / `admin`
3. 登录后可进行：
   - 控制台总览（在线代理、命令统计等）
   - 代理管理（查看/选择在线代理）
   - 代理构建（生成不同平台的代理源码）

## 5. 生成与部署代理

1. 在"代理构建"页面，填写服务器 IP、端口、输出文件名、目标平台（Windows/Linux/macOS）
2. 点击"构建代理"，下载生成的代理源码
3. 在目标主机上用 Rust 编译并运行该源码，或自行交叉编译

### Windows 示例
```bash
rustc agent.rs -o agent.exe
./agent.exe
```

### Linux/macOS 示例
```bash
rustc agent.rs -o agent
./agent
```

## 6. 代理上线与管理

- 代理运行后会自动连接到服务端，Web 界面"代理管理"可实时查看在线状态
- 可对代理下发命令、文件操作等（后续可扩展更多功能）

## 7. 常见问题

- **无法访问 Web 界面？**
  - 检查 VPS 防火墙是否放行 8080 端口
  - 检查服务端是否正常运行
- **代理无法连接？**
  - 检查 5555 端口是否开放
  - 检查服务器 IP/端口填写是否正确

## 8. 安全建议

- 部署后请及时修改默认密码
- 建议使用 HTTPS 反向代理（如 Nginx）保护 Web 界面
- 生产环境请加强访问控制与日志审计

## 9. 开发与自定义

- 所有 Web 页面均可自定义美化（见 `src/web.rs`）
- 代理生成逻辑可在 `src/agent_builder.rs` 扩展
- 支持自定义命令、文件管理等功能扩展

---

如有问题或建议，欢迎 Issue 反馈！ 