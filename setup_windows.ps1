# Ikunc2 C2 Server - Windows Setup Script
# This script sets up the required environment for the Ikunc2 C2 Server

Write-Host "🚀 Setting up Ikunc2 C2 Server Environment..." -ForegroundColor Green

# Create necessary directories
$directories = @("data", "data/json", "certs", "logs")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created directory: $dir" -ForegroundColor Yellow
    } else {
        Write-Host "📁 Directory already exists: $dir" -ForegroundColor Gray
    }
}

# Check if cargo is installed
if (!(Get-Command cargo -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Cargo/Rust not found. Please install Rust from https://rustup.rs/" -ForegroundColor Red
    Write-Host "After installing Rust, run this script again." -ForegroundColor Red
    exit 1
}

Write-Host "🔧 Building Ikunc2 C2 Server..." -ForegroundColor Green
cargo build --release

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build successful!" -ForegroundColor Green
    
    # Ask user if they want to configure firewall
    Write-Host ""
    Write-Host "🛡️  Firewall Configuration" -ForegroundColor Cyan
    Write-Host "The C2 server needs firewall rules for ports 80, 443, and 8080." -ForegroundColor Yellow
    $configureFirewall = Read-Host "Would you like to configure Windows Firewall automatically? (y/N)"
    
    if ($configureFirewall -eq "y" -or $configureFirewall -eq "Y") {
        Write-Host "Configuring Windows Firewall..." -ForegroundColor Green
        
        # Check if running as Administrator
        if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
            Write-Host "⚠️  Administrator privileges required for firewall configuration!" -ForegroundColor Yellow
            Write-Host "Please run the following command as Administrator:" -ForegroundColor Yellow
            Write-Host "  .\setup_windows_firewall.ps1" -ForegroundColor White
        } else {
            # Run firewall configuration
            if (Test-Path "setup_windows_firewall.ps1") {
                & .\setup_windows_firewall.ps1
            } else {
                Write-Host "❌ setup_windows_firewall.ps1 not found!" -ForegroundColor Red
                Write-Host "Please download the firewall setup script." -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "⚠️  Firewall not configured. You may need to manually allow ports 80, 443, and 8080." -ForegroundColor Yellow
        Write-Host "To configure later, run: .\setup_windows_firewall.ps1" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "🎯 Ikunc2 C2 Server Setup Complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Directory Structure:" -ForegroundColor Cyan
    Write-Host "  📁 data/         - Database and JSON storage" -ForegroundColor Gray
    Write-Host "  📁 certs/        - TLS certificates (auto-generated)" -ForegroundColor Gray
    Write-Host "  📁 logs/         - Application logs" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🚀 To start the server:" -ForegroundColor Cyan
    Write-Host "  .\target\release\c2-gui.exe" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🌐 Web Interface will be available at:" -ForegroundColor Cyan
    Write-Host "  HTTP:  http://localhost:8080" -ForegroundColor Yellow
    Write-Host "  HTTPS: https://localhost:8080 (with TLS enabled)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 Agent Communication:" -ForegroundColor Cyan
    Write-Host "  TCP:   Port 5555 (raw socket)" -ForegroundColor Yellow
    Write-Host "  HTTP:  Port 8080 (web traffic)" -ForegroundColor Yellow
    Write-Host "  HTTPS: Port 8080 (encrypted web traffic)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔐 Default Login:" -ForegroundColor Cyan
    Write-Host "  Username: admin" -ForegroundColor Yellow
    Write-Host "  Password: admin" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "⚙️  Environment Variables (optional):" -ForegroundColor Cyan
    Write-Host "  IKUNC2_ENABLE_TLS=true          - Enable HTTPS" -ForegroundColor Gray
    Write-Host "  IKUNC2_CERT_PATH=certs/cert.pem - Custom certificate" -ForegroundColor Gray
    Write-Host "  IKUNC2_KEY_PATH=certs/key.pem   - Custom private key" -ForegroundColor Gray
    Write-Host "  IKUNC2_PORT=8080                - HTTP/HTTPS port" -ForegroundColor Gray
    Write-Host "  IKUNC2_TCP_PORT=5555            - TCP listener port" -ForegroundColor Gray
} else {
    Write-Host "❌ Build failed. Please check the error messages above." -ForegroundColor Red
    Write-Host "💡 Common issues:" -ForegroundColor Yellow
    Write-Host "  - Missing Rust/Cargo installation" -ForegroundColor Gray
    Write-Host "  - Network issues downloading dependencies" -ForegroundColor Gray
    Write-Host "  - Insufficient disk space" -ForegroundColor Gray
    exit 1
} 