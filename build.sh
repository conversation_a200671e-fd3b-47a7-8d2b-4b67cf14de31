#!/bin/bash

# Ikunc2 C2 System Build Script

set -e

echo "🚀 Building Ikunc2 C2 System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    print_error "Rust/Cargo not found. Please install Rust first."
    exit 1
fi

print_status "Building main application..."
cargo build --release

if [ $? -eq 0 ]; then
    print_status "Main application built successfully!"
else
    print_error "Failed to build main application"
    exit 1
fi

print_status "Building agent..."
cd agent
cargo build --release

if [ $? -eq 0 ]; then
    print_status "Agent built successfully!"
else
    print_error "Failed to build agent"
    exit 1
fi

cd ..

# Create output directory
mkdir -p dist
cp target/release/c2-gui dist/
cp agent/target/release/agent dist/

print_status "Build completed! Files are in the 'dist' directory:"
echo "  - c2-gui (main application)"
echo "  - agent (agent binary)"

print_status "To run the server:"
echo "  ./dist/c2-gui server"

print_status "To run the GUI client:"
echo "  ./dist/c2-gui"

print_status "To run the agent:"
echo "  AGENT_IP=127.0.0.1 AGENT_PORT=5555 ./dist/agent" 