# Ikunc2 C2 服务器 - 生产环境部署指南

## 🚀 Ubuntu VPS 部署

本指南将帮助你在 Ubuntu VPS 上以生产环境方式部署 Ikunc2 C2 服务器。

## 📋 前置条件

- Ubuntu 20.04+ VPS
- Root 或 sudo 权限
- 至少 1GB 内存
- 10GB+ 磁盘空间
- 公网 IP 地址

## 🔧 快速部署

### 1. 克隆并部署

```bash
# 克隆仓库
git clone <your-repo-url>
cd c2-gui

# 赋予部署脚本可执行权限
chmod +x deploy_ubuntu.sh

# 运行部署脚本
./deploy_ubuntu.sh
```

脚本会自动完成：
- 安装 Rust 及依赖
- 构建应用
- 创建 systemd 服务
- 配置日志和数据库
- 设置防火墙规则
- 启动服务

### 2. 访问 Web 界面

部署完成后，在浏览器访问：
```
http://YOUR_VPS_IP:8080
```

默认账号：
- 用户名: `admin`
- 密码: `admin`

**⚠️ 强烈建议：首次登录后立即修改默认密码！**

## 🔧 手动配置

### 环境变量

应用可通过环境变量进行配置：

```bash
# 服务器配置
IKUNC2_HOST=0.0.0.0          # 服务器主机名
IKUNC2_PORT=8080             # Web GUI 端口
IKUNC2_TCP_PORT=5555         # TCP 监听端口

# 数据库配置
IKUNC2_DB_PATH=/var/lib/ikunc2/ikunc2.db
IKUNC2_DB_MAX_CONNECTIONS=10

# 安全配置
IKUNC2_SESSION_SECRET=你的密钥
IKUNC2_SESSION_TIMEOUT=24    # 小时

# 日志配置
IKUNC2_LOG_LEVEL=info        # debug, info, warn, error
IKUNC2_LOG_FILE=/var/log/ikunc2/ikunc2.log
```

### 服务管理

```bash
# 启动服务
sudo systemctl start ikunc2-c2

# 停止服务
sudo systemctl stop ikunc2-c2

# 重启服务
sudo systemctl restart ikunc2-c2

# 查看状态
sudo systemctl status ikunc2-c2

# 查看日志
sudo journalctl -u ikunc2-c2 -f

# 设置开机自启
sudo systemctl enable ikunc2-c2
```

### 使用管理脚本

部署后会在 `/opt/ikunc2/manage.sh` 生成管理脚本：

```bash
# 启动服务
/opt/ikunc2/manage.sh start

# 停止服务
/opt/ikunc2/manage.sh stop

# 重启服务
/opt/ikunc2/manage.sh restart

# 查看状态
/opt/ikunc2/manage.sh status

# 查看日志
/opt/ikunc2/manage.sh logs

# 编辑配置
/opt/ikunc2/manage.sh config

# 备份
/opt/ikunc2/manage.sh backup
```

## 🔒 安全配置

### 1. 修改默认密码

1. 访问 Web 界面
2. 使用 `admin/admin` 登录
3. 进入设置 → 修改密码
4. 设置强密码

### 2. 更新 Session 密钥

```bash
# 生成新密钥
openssl rand -base64 64

# 编辑配置
sudo nano /opt/ikunc2/.env

# 更新 IKUNC2_SESSION_SECRET
# 重启服务
sudo systemctl restart ikunc2-c2
```

### 3. 防火墙配置

```bash
# 只开放必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8080/tcp  # Web GUI
sudo ufw allow 5555/tcp  # TCP 监听

# 启用防火墙
sudo ufw enable

# 查看状态
sudo ufw status
```

### 4. 反向代理（推荐）

生产环境建议使用 HTTPS 反向代理：

```bash
# 安装 nginx
sudo apt install nginx

# 创建 nginx 配置
sudo nano /etc/nginx/sites-available/ikunc2
```

Nginx 配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/ikunc2 /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 📊 监控与日志

### 日志位置

- 应用日志: `/var/log/ikunc2/`
- 系统日志: `sudo journalctl -u ikunc2-c2`
- 数据库: `/var/lib/ikunc2/ikunc2.db`

### 常用监控命令

```bash
# 查看服务状态
sudo systemctl status ikunc2-c2

# 实时查看日志
sudo journalctl -u ikunc2-c2 -f

# 查看磁盘使用
df -h /var/lib/ikunc2

# 查看内存占用
ps aux | grep ikunc2

# 查看端口占用
sudo netstat -tlnp | grep ikunc2
```

## 🔄 备份与恢复

### 自动备份

管理脚本自带备份功能：

```bash
# 创建备份
/opt/ikunc2/manage.sh backup
```

### 手动备份

```bash
# 备份数据库
sudo cp /var/lib/ikunc2/ikunc2.db /backup/ikunc2.db.$(date +%Y%m%d)

# 备份配置
sudo cp /opt/ikunc2/.env /backup/env.$(date +%Y%m%d)
```

### 恢复

```bash
# 停止服务
sudo systemctl stop ikunc2-c2

# 恢复数据库
sudo cp /backup/ikunc2.db.YYYYMMDD /var/lib/ikunc2/ikunc2.db
sudo chown ikunc2:ikunc2 /var/lib/ikunc2/ikunc2.db

# 恢复配置
sudo cp /backup/env.YYYYMMDD /opt/ikunc2/.env
sudo chown ikunc2:ikunc2 /opt/ikunc2/.env

# 启动服务
sudo systemctl start ikunc2-c2
```

## 🚨 故障排查

### 常见问题

1. **服务无法启动**
   ```bash
   # 查看日志
   sudo journalctl -u ikunc2-c2 -n 50
   
   # 检查权限
   sudo chown -R ikunc2:ikunc2 /opt/ikunc2 /var/lib/ikunc2 /var/log/ikunc2
   ```

2. **数据库报错**
   ```bash
   # 检查数据库文件
   ls -la /var/lib/ikunc2/
   
   # 检查 SQLite 数据库
   sqlite3 /var/lib/ikunc2/ikunc2.db ".tables"
   ```

3. **端口被占用**
   ```bash
   # 查看占用端口的进程
   sudo netstat -tlnp | grep :8080
   
   # 结束进程或更换端口
   sudo systemctl stop 冲突服务
   ```

4. **权限不足**
   ```bash
   # 修复权限
   sudo chown -R ikunc2:ikunc2 /opt/ikunc2
   sudo chmod 755 /opt/ikunc2/ikunc2-server
   ```

### 性能调优

高并发场景建议：

```bash
# 增加数据库连接数
echo "IKUNC2_DB_MAX_CONNECTIONS=50" >> /opt/ikunc2/.env

# 优化日志级别
echo "IKUNC2_LOG_LEVEL=warn" >> /opt/ikunc2/.env

# 重启服务
sudo systemctl restart ikunc2-c2
```

## 📞 支持

如遇问题请：
1. 查看日志：`sudo journalctl -u ikunc2-c2 -f`
2. 检查配置：`/opt/ikunc2/.env`
3. 查看服务状态：`sudo systemctl status ikunc2-c2`

## 🔄 更新

升级应用流程：

```bash
# 停止服务
sudo systemctl stop ikunc2-c2

# 备份当前版本
/opt/ikunc2/manage.sh backup

# 拉取最新代码
git pull

# 重新构建
cargo build --release

# 更新二进制
sudo cp target/release/c2-gui /opt/ikunc2/ikunc2-server
sudo chown ikunc2:ikunc2 /opt/ikunc2/ikunc2-server

# 启动服务
sudo systemctl start ikunc2-c2
```

---

**🎯 你的 Ikunc2 C2 服务器已准备好投入生产环境！** 