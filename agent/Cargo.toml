[package]
name = "ikunc2-agent"
version = "2.0.0"
edition = "2021"
authors = ["IkunC2 Team"]
description = "Ikunc2 C2 Agent - Advanced Command & Control Agent"
license = "MIT"

[dependencies]
# Core async runtime
tokio = { version = "1.41", features = ["full"] }

# HTTP client
reqwest = { version = "0.12", features = ["json", "native-tls"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# System information
whoami = "1.6"

# UUID generation
uuid = { version = "1.11", features = ["v4", "serde"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "2.0"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true
