# ===============================================================================
# Ikunc2 C2 Server - Windows Firewall Configuration Script
# Configures firewall rules for agent-server communication
# ===============================================================================

# Requires Administrator privileges
#Requires -RunAsAdministrator

param(
    [string]$ServerExecutable = "c2-gui.exe",
    [switch]$Remove = $false,
    [switch]$Verbose = $false
)

# Color output functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" -Color "Green"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" -Color "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" -Color "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" -Color "Red"
}

# Configuration
$RequiredPorts = @(80, 443, 8080)
$RuleName = "Ikunc2-C2-Server"

Write-ColorOutput "`n🛡️  Ikunc2 C2 Server - Windows Firewall Configuration" -Color "Magenta"
Write-ColorOutput "======================================================" -Color "Magenta"

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script requires Administrator privileges!"
    Write-Info "Please run PowerShell as Administrator and try again."
    Write-Info "Right-click PowerShell and select 'Run as Administrator'"
    exit 1
}

Write-Success "Running with Administrator privileges"

if ($Remove) {
    Write-Info "Removing existing firewall rules..."
    
    # Remove existing rules
    foreach ($port in $RequiredPorts) {
        $inboundRule = "$RuleName-Inbound-$port"
        $outboundRule = "$RuleName-Outbound-$port"
        
        try {
            Remove-NetFirewallRule -DisplayName $inboundRule -ErrorAction SilentlyContinue
            Remove-NetFirewallRule -DisplayName $outboundRule -ErrorAction SilentlyContinue
            Write-Success "Removed rules for port $port"
        }
        catch {
            Write-Warning "Could not remove rules for port $port (they may not exist)"
        }
    }
    
    Write-Success "Firewall rules removal completed"
    exit 0
}

Write-Info "Configuring firewall rules for Ikunc2 C2 Server..."
Write-Info "Ports to configure: $($RequiredPorts -join ', ')"

# Get the current directory for the executable path
$CurrentDir = Get-Location
$ExecutablePath = Join-Path $CurrentDir $ServerExecutable

if (Test-Path $ExecutablePath) {
    Write-Success "Found server executable: $ExecutablePath"
} else {
    Write-Warning "Server executable not found at: $ExecutablePath"
    Write-Info "Firewall rules will be created for the ports only (not application-specific)"
}

# Remove existing rules first (if they exist)
Write-Info "Removing any existing Ikunc2 firewall rules..."
foreach ($port in $RequiredPorts) {
    $inboundRule = "$RuleName-Inbound-$port"
    $outboundRule = "$RuleName-Outbound-$port"
    
    Remove-NetFirewallRule -DisplayName $inboundRule -ErrorAction SilentlyContinue
    Remove-NetFirewallRule -DisplayName $outboundRule -ErrorAction SilentlyContinue
}

# Create new firewall rules
Write-Info "Creating new firewall rules..."

foreach ($port in $RequiredPorts) {
    $portDesc = switch ($port) {
        80 { "HTTP" }
        443 { "HTTPS" }
        8080 { "HTTP/HTTPS Alternative" }
        default { "Port $port" }
    }
    
    Write-Info "Configuring port $port ($portDesc)..."
    
    # Inbound rules (allowing connections TO the server)
    $inboundRule = "$RuleName-Inbound-$port"
    try {
        if (Test-Path $ExecutablePath) {
            New-NetFirewallRule -DisplayName $inboundRule `
                -Description "Ikunc2 C2 Server - Allow inbound connections on port $port ($portDesc)" `
                -Direction Inbound `
                -Protocol TCP `
                -LocalPort $port `
                -Action Allow `
                -Profile Any `
                -Program $ExecutablePath `
                -Enabled True | Out-Null
        } else {
            New-NetFirewallRule -DisplayName $inboundRule `
                -Description "Ikunc2 C2 Server - Allow inbound connections on port $port ($portDesc)" `
                -Direction Inbound `
                -Protocol TCP `
                -LocalPort $port `
                -Action Allow `
                -Profile Any `
                -Enabled True | Out-Null
        }
        Write-Success "  ✓ Inbound rule created for port $port"
    }
    catch {
        Write-Error "  ✗ Failed to create inbound rule for port $port`: $($_.Exception.Message)"
    }
    
    # Outbound rules (allowing connections FROM the server)
    $outboundRule = "$RuleName-Outbound-$port"
    try {
        if (Test-Path $ExecutablePath) {
            New-NetFirewallRule -DisplayName $outboundRule `
                -Description "Ikunc2 C2 Server - Allow outbound connections on port $port ($portDesc)" `
                -Direction Outbound `
                -Protocol TCP `
                -RemotePort $port `
                -Action Allow `
                -Profile Any `
                -Program $ExecutablePath `
                -Enabled True | Out-Null
        } else {
            New-NetFirewallRule -DisplayName $outboundRule `
                -Description "Ikunc2 C2 Server - Allow outbound connections on port $port ($portDesc)" `
                -Direction Outbound `
                -Protocol TCP `
                -RemotePort $port `
                -Action Allow `
                -Profile Any `
                -Enabled True | Out-Null
        }
        Write-Success "  ✓ Outbound rule created for port $port"
    }
    catch {
        Write-Error "  ✗ Failed to create outbound rule for port $port`: $($_.Exception.Message)"
    }
}

# Display current firewall rules
Write-Info "`nCurrent Ikunc2 firewall rules:"
try {
    $rules = Get-NetFirewallRule | Where-Object { $_.DisplayName -like "$RuleName*" } | Sort-Object DisplayName
    
    if ($rules.Count -eq 0) {
        Write-Warning "No Ikunc2 firewall rules found"
    } else {
        foreach ($rule in $rules) {
            $status = if ($rule.Enabled -eq "True") { "✅ Enabled" } else { "❌ Disabled" }
            $direction = $rule.Direction
            $profile = $rule.Profile
            Write-Info "  $($rule.DisplayName) - $direction - $profile - $status"
        }
    }
}
catch {
    Write-Warning "Could not retrieve firewall rules: $($_.Exception.Message)"
}

# Test port connectivity
Write-Info "`nTesting port availability..."
foreach ($port in $RequiredPorts) {
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $port)
        $listener.Start()
        $listener.Stop()
        Write-Success "  ✓ Port $port is available"
    }
    catch {
        Write-Warning "  ⚠️  Port $port may be in use or blocked: $($_.Exception.Message)"
    }
}

# Display Windows Defender Firewall status
Write-Info "`nWindows Defender Firewall Status:"
try {
    $firewallProfiles = Get-NetFirewallProfile
    foreach ($profile in $firewallProfiles) {
        $status = if ($profile.Enabled) { "✅ Enabled" } else { "❌ Disabled" }
        Write-Info "  $($profile.Name) Profile: $status"
    }
}
catch {
    Write-Warning "Could not retrieve firewall profile status"
}

Write-Success "`n🎯 Firewall configuration completed!"
Write-Info "`nNext steps:"
Write-Info "1. Start your Ikunc2 C2 server"
Write-Info "2. Verify agents can connect on ports: $($RequiredPorts -join ', ')"
Write-Info "3. Monitor connections in the server dashboard"

Write-Info "`nTo remove these rules later, run:"
Write-Info "  .\setup_windows_firewall.ps1 -Remove"

Write-ColorOutput "`n🛡️  Security Notes:" -Color "Yellow"
Write-Info "- These rules allow traffic on the specified ports"
Write-Info "- Ensure your C2 server uses proper authentication"
Write-Info "- Monitor network traffic for security"
Write-Info "- Consider using HTTPS (port 443) for encrypted communication"

Write-ColorOutput "`n✅ Configuration completed successfully!" -Color "Green" 